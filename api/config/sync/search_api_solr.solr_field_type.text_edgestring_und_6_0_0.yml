uuid: 87c58ede-e396-4c32-86f7-11f83521a71b
langcode: en
status: true
dependencies: {  }
_core:
  default_config_hash: iifw_DxebhUOezFaJM3sD9m2rvzGXWkwxPC_g_58nag
id: text_edgestring_und_6_0_0
label: 'Edge NGram String Field'
minimum_solr_version: 6.0.0
custom_code: edgestring
field_type_language_code: und
domains: {  }
field_type:
  name: text_edgenstring
  class: solr.TextField
  positionIncrementGap: 100
  analyzers:
    -
      type: index
      tokenizer:
        class: solr.KeywordTokenizerFactory
      filters:
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
        -
          class: solr.EdgeNGramFilterFactory
          minGramSize: 2
          maxGramSize: 25
    -
      type: query
      tokenizer:
        class: solr.KeywordTokenizerFactory
      filters:
        -
          class: solr.LowerCaseFilterFactory
        -
          class: solr.RemoveDuplicatesTokenFilterFactory
unstemmed_field_type: null
spellcheck_field_type: null
collated_field_type: null
solr_configs: {  }
text_files: {  }
