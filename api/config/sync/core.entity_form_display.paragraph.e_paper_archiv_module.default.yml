uuid: b24413f4-0f72-45f4-b9d4-98a4f7d2277d
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.e_paper_archiv_module.field_button
    - field.field.paragraph.e_paper_archiv_module.field_button_bool
    - field.field.paragraph.e_paper_archiv_module.field_load_newest
    - field.field.paragraph.e_paper_archiv_module.field_magazine
    - field.field.paragraph.e_paper_archiv_module.field_title
    - paragraphs.paragraphs_type.e_paper_archiv_module
  module:
    - field_group
    - link
third_party_settings:
  field_group:
    group_magazine_wrap:
      children:
        - field_magazine
      label: 'Magazine Wrap'
      region: content
      parent_name: ''
      weight: 2
      format_type: html_element
      format_settings:
        classes: magazine-field-wrap
        show_empty_fields: true
        id: ''
        element: div
        show_label: false
        label_element: h3
        label_element_classes: ''
        attributes: ''
        effect: none
        speed: fast
        required_fields: true
id: paragraph.e_paper_archiv_module.default
targetEntityType: paragraph
bundle: e_paper_archiv_module
mode: default
content:
  field_button:
    type: link_default
    weight: 4
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_button_bool:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_load_newest:
    type: boolean_checkbox
    weight: 1
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  field_magazine:
    type: string_textfield
    weight: 2
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 5
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden:
  created: true
