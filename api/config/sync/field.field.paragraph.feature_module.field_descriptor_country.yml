uuid: 5a35eeea-5746-43c5-b055-684b54c27da3
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_descriptor_country
    - paragraphs.paragraphs_type.feature_module
    - taxonomy.vocabulary.country
id: paragraph.feature_module.field_descriptor_country
field_name: field_descriptor_country
entity_type: paragraph
bundle: feature_module
label: 'Descriptor Country'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      country: country
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
