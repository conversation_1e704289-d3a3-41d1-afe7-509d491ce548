uuid: c4727067-56fe-4a96-9429-2086a6da53f3
langcode: en
status: true
dependencies:
  config:
    - field.field.node.girl_des_tages_galleries.field_girl_infos
    - field.field.node.girl_des_tages_galleries.field_manual
    - node.type.girl_des_tages_galleries
  module:
    - content_moderation
    - inline_entity_form
    - path
    - scheduler
    - scheduler_content_moderation_integration
id: node.girl_des_tages_galleries.default
targetEntityType: node
bundle: girl_des_tages_galleries
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_girl_infos:
    type: inline_entity_form_complex
    weight: 122
    region: content
    settings:
      form_mode: default
      override_labels: false
      label_singular: ''
      label_plural: ''
      allow_new: false
      allow_existing: true
      match_operator: CONTAINS
      allow_duplicate: false
      collapsible: false
      collapsed: false
      revision: false
    third_party_settings: {  }
  field_manual:
    type: inline_entity_form_complex
    weight: 123
    region: content
    settings:
      form_mode: default
      override_labels: false
      label_singular: ''
      label_plural: ''
      allow_new: false
      allow_existing: true
      match_operator: CONTAINS
      allow_duplicate: false
      collapsible: false
      collapsed: false
      revision: false
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 100
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_state:
    type: scheduler_moderation
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  unpublish_state:
    type: scheduler_moderation
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
hidden: {  }
