uuid: 1f3725a1-2ee3-435a-b012-e462a8abc9fd
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_girl_infos
    - node.type.girl_des_tages_galleries
id: node.girl_des_tages_galleries.field_girl_infos
field_name: field_girl_infos
entity_type: node
bundle: girl_des_tages_galleries
label: 'Galerie des Tages'
description: 'Wird automatisch nachts gesetzt, Änderungen werden nicht dauerhaft gespeichert. Feld "Zusätzliche Galerien" verwenden.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:girl_info'
  handler_settings:
    target_bundles: null
    sort:
      field: _none
      direction: ASC
    auto_create: false
field_type: entity_reference
