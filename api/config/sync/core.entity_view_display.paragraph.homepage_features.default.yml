uuid: b7cd0c19-7cd5-48bc-9782-06d6455b01e0
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.homepage_features.field_icon
    - field.field.paragraph.homepage_features.field_name
    - paragraphs.paragraphs_type.homepage_features
  module:
    - svg_image
id: paragraph.homepage_features.default
targetEntityType: paragraph
bundle: homepage_features
mode: default
content:
  field_icon:
    type: image_url
    label: above
    settings:
      image_style: ''
    third_party_settings: {  }
    weight: 2
    region: content
  field_name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  search_api_excerpt: true
