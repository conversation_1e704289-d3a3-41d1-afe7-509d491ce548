uuid: 27e0ac35-124a-4cf7-a3ce-f76ab2b5a84c
langcode: en
status: true
dependencies:
  config:
    - field.field.girl_info.girl_info.field_additional_girls
    - field.field.girl_info.girl_info.field_category
    - field.field.girl_info.girl_info.field_city
    - field.field.girl_info.girl_info.field_country
    - field.field.girl_info.girl_info.field_export_girl_des_tages
    - field.field.girl_info.girl_info.field_featured
    - field.field.girl_info.girl_info.field_from
    - field.field.girl_info.girl_info.field_image_count
    - field.field.girl_info.girl_info.field_latest_gallery_release
    - field.field.girl_info.girl_info.field_main_focal_point_x
    - field.field.girl_info.girl_info.field_main_focal_point_y
    - field.field.girl_info.girl_info.field_notes
    - field.field.girl_info.girl_info.field_plus_access
    - field.field.girl_info.girl_info.field_province
    - field.field.girl_info.girl_info.field_public_images
    - field.field.girl_info.girl_info.field_second_focal_point_x
    - field.field.girl_info.girl_info.field_second_focal_point_y
    - field.field.girl_info.girl_info.field_tags
    - field.field.girl_info.girl_info.field_third_focal_point_x
    - field.field.girl_info.girl_info.field_third_focal_point_y
    - field.field.girl_info.girl_info.field_to
    - field.field.girl_info.girl_info.field_video_count
  module:
    - datetime
    - pb_girl_info
    - svg_image
    - taxonomy
    - text
id: girl_info.girl_info.default
targetEntityType: girl_info
bundle: girl_info
mode: default
content:
  birthday:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: html_datetime
    third_party_settings: {  }
    weight: 17
    region: content
  bustsize:
    type: number_unformatted
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 27
    region: content
  city:
    type: entity_reference_rss_category
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  country:
    type: entity_reference_rss_category
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 12
    region: content
  cupsize:
    type: entity_reference_rss_category
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 24
    region: content
  custom_access:
    type: boolean
    label: above
    settings:
      format: true-false
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 0
    region: content
  description:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 18
    region: content
  description_original:
    type: basic_string
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 9
    region: content
  descriptor_category:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 5
    region: content
  descriptor_country_ref:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 11
    region: content
  descriptor_month:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 7
    region: content
  descriptor_week:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 6
    region: content
  descriptor_year:
    type: number_integer
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
    weight: 8
    region: content
  descriptors:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 6
    region: content
  eyecolor:
    type: entity_reference_rss_category
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 20
    region: content
  field_additional_girls:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 39
    region: content
  field_category:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 38
    region: content
  field_city:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 36
    region: content
  field_country:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 37
    region: content
  field_export_girl_des_tages:
    type: boolean
    label: above
    settings:
      format: true-false
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 41
    region: content
  field_featured:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 31
    region: content
  field_from:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 32
    region: content
  field_image_count:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 43
    region: content
  field_latest_gallery_release:
    type: timestamp
    label: above
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
    third_party_settings: {  }
    weight: 53
    region: content
  field_main_focal_point_x:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 45
    region: content
  field_main_focal_point_y:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 46
    region: content
  field_notes:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 52
    region: content
  field_plus_access:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 51
    region: content
  field_province:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 35
    region: content
  field_public_images:
    type: image
    label: above
    settings:
      image_link: ''
      image_style: ''
      svg_attributes:
        width: null
        height: null
      svg_render_as_image: true
    third_party_settings: {  }
    weight: 42
    region: content
  field_second_focal_point_x:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 47
    region: content
  field_second_focal_point_y:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 48
    region: content
  field_tags:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 34
    region: content
  field_third_focal_point_x:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 49
    region: content
  field_third_focal_point_y:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 50
    region: content
  field_to:
    type: datetime_default
    label: above
    settings:
      timezone_override: ''
      format_type: medium
    third_party_settings: {  }
    weight: 33
    region: content
  field_video_count:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 44
    region: content
  firstname:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 16
    region: content
  flag_girl_info_flag:
    settings: {  }
    third_party_settings: {  }
    weight: 10
    region: content
  galleries:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 30
    region: content
  girl:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 15
    region: content
  haircolor:
    type: entity_reference_rss_category
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 21
    region: content
  height:
    type: number_unformatted
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 23
    region: content
  hipsize:
    type: number_unformatted
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 25
    region: content
  hometown:
    type: entity_reference_rss_category
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 19
    region: content
  images:
    type: image
    label: above
    settings:
      image_link: ''
      image_style: ''
    third_party_settings: {  }
    weight: 5
    region: content
  lastname:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 28
    region: content
  middlename:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 29
    region: content
  name:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 14
    region: content
  province:
    type: entity_reference_rss_category
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  publish_date:
    type: datetime_custom
    label: above
    settings:
      timezone_override: ''
      date_format: 'Y-m-d\TH:i:s'
    third_party_settings: {  }
    weight: 3
    region: content
  release:
    type: timestamp
    label: above
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
    third_party_settings: {  }
    weight: 4
    region: content
  release_date:
    type: datetime_plain
    label: above
    settings:
      timezone_override: ''
    third_party_settings: {  }
    weight: 4
    region: content
  status:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 40
    region: content
  tags:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 13
    region: content
  unpublish_date:
    type: datetime_custom
    label: above
    settings:
      timezone_override: ''
      date_format: 'Y-m-d\TH:i:s'
    third_party_settings: {  }
    weight: 2
    region: content
  waistsize:
    type: number_unformatted
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 26
    region: content
  weight:
    type: number_unformatted
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 22
    region: content
hidden:
  langcode: true
  main_images: true
  non_nude_images: true
  search_api_excerpt: true
