uuid: ed6a181d-8a3e-4806-a30d-597e098fff4e
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.e_paper_archiv_module.field_button
    - field.field.paragraph.e_paper_archiv_module.field_button_bool
    - field.field.paragraph.e_paper_archiv_module.field_load_newest
    - field.field.paragraph.e_paper_archiv_module.field_magazine
    - field.field.paragraph.e_paper_archiv_module.field_title
    - paragraphs.paragraphs_type.e_paper_archiv_module
  module:
    - link
id: paragraph.e_paper_archiv_module.default
targetEntityType: paragraph
bundle: e_paper_archiv_module
mode: default
content:
  field_button:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 3
    region: content
  field_button_bool:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 2
    region: content
  field_load_newest:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 4
    region: content
  field_magazine:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  search_api_excerpt: true
