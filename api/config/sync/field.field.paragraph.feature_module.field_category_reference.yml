uuid: c58d8f71-9c1f-4296-b1bc-332ae5482874
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_category_reference
    - paragraphs.paragraphs_type.feature_module
    - taxonomy.vocabulary.category
id: paragraph.feature_module.field_category_reference
field_name: field_category_reference
entity_type: paragraph
bundle: feature_module
label: Category
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      category: category
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
