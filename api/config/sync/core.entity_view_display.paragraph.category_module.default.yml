uuid: b1032629-e450-4e3f-bf69-d4f1f3d7e645
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.category_module.field_categories
    - field.field.paragraph.category_module.field_grid_layout_2
    - field.field.paragraph.category_module.field_title
    - paragraphs.paragraphs_type.category_module
  module:
    - options
id: paragraph.category_module.default
targetEntityType: paragraph
bundle: category_module
mode: default
content:
  field_categories:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 2
    region: content
  field_grid_layout_2:
    type: list_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  field_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  search_api_excerpt: true
