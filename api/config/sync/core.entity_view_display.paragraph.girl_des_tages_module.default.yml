uuid: bbe97944-8f7b-4ec1-a181-5b33af26ddff
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.girl_des_tages_module.field_button
    - field.field.paragraph.girl_des_tages_module.field_button_bool
    - field.field.paragraph.girl_des_tages_module.field_grid_layout_3
    - field.field.paragraph.girl_des_tages_module.field_title
    - paragraphs.paragraphs_type.girl_des_tages_module
  module:
    - link
    - options
id: paragraph.girl_des_tages_module.default
targetEntityType: paragraph
bundle: girl_des_tages_module
mode: default
content:
  field_button:
    type: link
    label: above
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 3
    region: content
  field_button_bool:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 2
    region: content
  field_grid_layout_3:
    type: list_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  field_title:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  search_api_excerpt: true
