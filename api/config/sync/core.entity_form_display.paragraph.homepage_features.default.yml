uuid: 2e1c3648-76e0-4eca-a6be-8985bbb8f2d3
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.homepage_features.field_icon
    - field.field.paragraph.homepage_features.field_name
    - image.style.thumbnail
    - paragraphs.paragraphs_type.homepage_features
  module:
    - svg_image
id: paragraph.homepage_features.default
targetEntityType: paragraph
bundle: homepage_features
mode: default
content:
  field_icon:
    type: image_image
    weight: 1
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_name:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 2
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden:
  created: true
