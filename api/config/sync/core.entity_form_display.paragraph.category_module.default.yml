uuid: 4bc7a92b-1a40-48da-8759-6edee5dfb1a4
langcode: en
status: true
dependencies:
  config:
    - field.field.paragraph.category_module.field_categories
    - field.field.paragraph.category_module.field_grid_layout_2
    - field.field.paragraph.category_module.field_title
    - paragraphs.paragraphs_type.category_module
id: paragraph.category_module.default
targetEntityType: paragraph
bundle: category_module
mode: default
content:
  field_categories:
    type: entity_reference_autocomplete
    weight: 2
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_grid_layout_2:
    type: options_select
    weight: 1
    region: content
    settings: {  }
    third_party_settings: {  }
  field_title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 3
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
hidden:
  created: true
