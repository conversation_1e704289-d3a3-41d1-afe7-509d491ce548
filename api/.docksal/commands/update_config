#!/usr/bin/env bash

## Init local docksal.
## (Copy default local docksal configuration to start pattern container. Install drupal.)

fin up

DOCROOT='web'
DOCROOT_PATH=${PROJECT_ROOT}/${DOCROOT}

set -e


#-------------------------- Helper functions --------------------------------

cp "${DOCROOT_PATH}/sites/default/default.settings.docksal.update_config.php" "${DOCROOT_PATH}/sites/default/settings.docksal.php"

fin drush sql:sync @premium.master @self -y

fin drush cr
fin drush cex -y

cp "${DOCROOT_PATH}/sites/default/default.settings.docksal.php" "${DOCROOT_PATH}/sites/default/settings.docksal.php"

fin drush cr
fin drush cim -y

fin drush uli

