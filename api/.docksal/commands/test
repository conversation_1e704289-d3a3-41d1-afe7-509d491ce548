#!/usr/bin/env bash

## Test site installation
##
## Usage: fin test

# Abort if anything fails
set -e

# Debug mode switch
if [[ "${DEBUG}" != "" ]]; then
    set -x
fi

echo "Testing home page..."
curl -sL -I http://${VIRTUAL_HOST} | grep "HTTP/1.1 200 OK"
curl -sL http://${VIRTUAL_HOST} | grep "My Drupal 9 Site"
echo "Testing login page..."
curl -sL -I http://${VIRTUAL_HOST}/user/login | grep "HTTP/1.1 200 OK"
