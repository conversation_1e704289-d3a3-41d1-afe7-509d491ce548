# Migrationsanweisungen

1. Verzeichnisstruktur vom Maintenance Ordner in das public files Verzeichnis kopieren.<br><br>
2. Gallerie Daten (container-xxxxxxxxx.json) nach **files-private/migration/girl_info/data/images** kopieren<br><br>
3. Girl Info Daten (girl--xxxxxxxxx.json) nach **files-private/migration/girl_info/data/infos** kopieren<br><br>
4. User Daten mit Flags nach **files-private/migration/girl_info/data/user** kopieren<br><br>
5. Bilddateien werden vom private files Verzeichnis übernommen, mit gleicher Ordnerstruktur wie in den json Files angegeben.<br><br>
6. Benötigte Module müssen aktiviert sein:
   > fin drush en pb_girl_info migrate_plus migrate_file migrate_tools
7. Drush commands ausführen zum Vorbereiten der Migration Daten in private files:
   > fin drush pbgi:process-info-files<br>
   > fin drush pbgi:process-gallery-files<br>
   > fin drush pbgi:process-user-files
8. Migrationen ausführen:
   1. Girl entities:
     > fin drush mim --group=pb_girls_image<br>
     fin drush mim pb_girls
   2. Gallerie Bilder:
     > fin drush mim pb_girl_info_image
   3. Gallerien:
     > fin drush mim pb_girl_info_gallery
   4. Girl info entities:
     > fin drush mim pb_girl_info
   5. Bei S3 Storage: Metadaten von S3 aktualisieren
     > fin drush s3fs-rc
   6. User:
     > fin drush mim user
   7. Like & Favorites:
     > fin drush mim --group=flags



- SQL updates
- image field to media
- term merge