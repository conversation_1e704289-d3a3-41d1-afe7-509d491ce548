# This file describes an application. You can have multiple applications
# in the same project.
#
# See https://docs.platform.sh/configuration/app.html

# The name of this app. Must be unique within a project.
name: 'api'

# The runtime the application uses.
type: 'php:7.4'

# Set the timezone to Europe/Paris
timezone: 'Europe/Paris'

dependencies:
    php:
        composer/composer: '^2'

runtime:
    # Enable the redis extension so <PERSON><PERSON><PERSON> can communicate with the Redis cache.
    extensions:
        - redis
        - sodium

variables:
    php:
        memory_limit: 128M
        date.timezone: 'Europe/Paris'
        session.cookie_secure: 1
        session.cookie_samesite: 'None'

# The relationships of the application with services or other applications.
#
# The left-hand side is the name of the relationship as it will be exposed
# to the application in the PLATFORM_RELATIONSHIPS variable. The right-hand
# side is in the form `<service name>:<endpoint name>`.
relationships:
    database: 'db:mysql'
    redis: 'cache:redis'
    solrsearch: 'search:main'

# The size of the persistent disk of the application (in MB).
disk: 22344

# The 'mounts' describe writable, persistent filesystem mounts in the application.
mounts:
    # The default Drupal files directory.
    '/web/sites/default/files':
        source: local
        source_path: 'files'
    # <PERSON><PERSON>al gets its own dedicated tmp directory. The settings.platformsh.php
    # file will automatically configure Drupal to use this directory.
    '/tmp':
        source: local
        source_path: 'tmp'
    # Private file uploads are stored outside the web root. The settings.platformsh.php
    # file will automatically configure Drupal to use this directory.
    '/private':
        source: local
        source_path: 'private'
    # Drush needs a scratch space for its own caches.
    '/.drush':
        source: local
        source_path: 'drush'
    # Drush will try to save backups to this directory, so it must be
    # writeable even though you will almost never need to use it.
    '/drush-backups':
        source: local
        source_path: 'drush-backups'
    # Drupal Console will try to save backups to this directory, so it must be
    # writeable even though you will almost never need to use it.
    '/.console':
        source: local
        source_path: 'console'

# Configuration of the build of this application.
build:
    # Automatically run `composer install` on every build.
    flavor: composer

# The hooks executed at various points in the lifecycle of the application.
hooks:
    # The build hook runs after Composer to finish preparing up your code.
    # No services are available but the disk is writeable.
    build: |
        set -e
    # The deploy hook runs after your application has been deployed and started.
    # Code cannot be modified at this point but the database is available.
    # The site is not accepting requests while this script runs so keep it
    # fast.
    deploy: |
        set -e
        php ./drush/platformsh_generate_drush_yml.php
        cd web
        drush -y cache-rebuild
        drush -y updatedb
        drush -y config-import

# The configuration of app when it is exposed to the web.
web:
    locations:
        # All requests not otherwise specified follow these rules.
        '/':
            # The folder from which to serve static assets, for this location.
            #
            # This is a filesystem path, relative to the application root.
            root: 'web'

            # How long to allow static assets from this location to be cached.
            #
            # Can be a time in seconds, or -1 for no caching. Times can be
            # suffixed with "s" (seconds), "m" (minutes), "h" (hours), "d"
            # (days), "w" (weeks), "M" (months, as 30 days) or "y" (years, as
            # 365 days).
            expires: 5m

            # Redirect any incoming request to Drupal's front controller.
            passthru: '/index.php'

            # Deny access to all static files, except those specifically allowed below.
            allow: false

            # Rules for specific URI patterns.
            rules:
                # Allow access to common static files.
                ? '(?i)\.(jpe?g|png|gif|svgz?|css|js|map|ico|bmp|eot|woff2?|otf|ttf)$'
                : allow: true
                '^/robots\.txt$':
                    allow: true
                '^/sitemap\.xml$':
                    allow: true

                # Deny direct access to configuration files.
                '^/sites/sites\.php$':
                    scripts: false
                '^/sites/[^/]+/settings.*?\.php$':
                    scripts: false

        # The files directory has its own special configuration rules.
        '/sites/default/files':
            # Allow access to all files in the public files directory.
            allow: true
            expires: 2w
            passthru: '/index.php'
            root: 'web/sites/default/files'

            # Do not execute PHP scripts from the writeable mount.
            scripts: false

            rules:
                # Provide a longer TTL (2 weeks) for aggregated CSS and JS files.
                '^/sites/default/files/(css|js)':
                    expires: 2w

        # The files directory has its own special configuration rules.
        '/system/files':
            allow: true
            expires: 30m
            passthru: '/index.php'

            # Do not execute PHP scripts from the writeable mount.
            scripts: false

            rules:
                \.jpeg$:
                    headers:
                        cache-control: public, max-age=1800

crons:
    # Run Drupal's cron tasks every 19 minutes.
    drupal:
        spec: '*/19 * * * *'
        cmd: 'cd web ; drush core-cron'
    backup:
        spec: '0 3 * * *'
        cmd: |
            if [ "$PLATFORM_BRANCH" = master ]; then
                platform backup:create --yes --no-wait
            fi
    scheduler:
        spec: '*/5 * * * *'
        cmd: |
            if [ "$PLATFORM_BRANCH" = master ]; then
                timeout 10s wget -q -O /dev/null "https://api.premium.playboy.de/scheduler/cron/2ed96d567560ca1baffe"
            fi
    publish_morning:
        spec: '0 10 * * *'
        cmd: 'cd web ; drush pb:p'
    publish_afternoon:
        spec: '45 17 * * *'
        cmd: 'cd web ; drush pb:p'
    publish_evening:
        spec: '45 21 * * *'
        cmd: 'cd web ; drush pb:p'
    plus_access_sync_galerie_des_tages:
        spec: '10 0 * * *'
        cmd: 'cd web ; drush pbis:plus_access_sync_galerie_des_tages'
    plus_access_true_to_false:
        spec: '12 0 * * *'
        cmd: 'cd web ; drush pbis:plus_access_true_to_false'
    plus_access_all_to_true:
        spec: '14 0 * * *'
        cmd: 'cd web ; drush pbis:plus_access_all_to_true'
