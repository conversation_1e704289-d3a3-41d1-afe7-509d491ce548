<?php
/**
 * Implements hook_views_post_execute().
 */
function graphql_path_fix_views_post_execute(\Drupal\views\ViewExecutable $view) {
  if ($view->id() === 'graphql_category_by_id') {
    foreach ($view->result as &$row) {
      // Check if 'fieldLink' exists and contains the 'url' and 'path'.
      if (isset($row->_entity->field_link->uri)) {
        // Decode the double-encoded path.
        $decoded_path = urldecode($row->_entity->field_link->uri);
        $row->_entity->field_link->uri = $decoded_path;
      }
    }
  }

  if ($view->id() === 'modular_page') {
    \Drupal::logger('graphql_path_fix')->debug('Post execute for view: ' . $view->id());
    foreach ($view->result as &$row) {
      if (isset($row->_entity->field_modules)) {
        foreach ($row->_entity->field_modules as &$module) {
          if (isset($module->entity->field_button->uri)) {
            // Decode the double-encoded path.
            $decoded_path = urldecode($module->entity->field_button->uri);
            $module->entity->field_button->uri = $decoded_path;
          }
        }
      }
    }
  }
}
