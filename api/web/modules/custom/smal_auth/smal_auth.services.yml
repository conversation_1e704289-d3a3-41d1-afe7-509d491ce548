services:
  smal_auth.sso_subscriber:
    class: <PERSON><PERSON><PERSON>\smal_auth\EventSubscriber\SsoSessionSubscriber
    arguments:
      [
        '@current_user',
        '@cache.default',
        '@smal_auth.sso_client',
        '@session_manager',
        '@request_stack'
      ]
    tags:
      - { name: event_subscriber }

  smal_auth.sso_client:
    class: <PERSON><PERSON><PERSON>\smal_auth\Sso\SsoClient