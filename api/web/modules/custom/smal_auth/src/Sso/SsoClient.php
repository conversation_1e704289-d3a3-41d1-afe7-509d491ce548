<?php

namespace Drupal\smal_auth\Sso;

use Symfony\Component\HttpFoundation\Request;

class SsoClient
{

  public function checkSession(Request $request): bool
  {
    // Example: Pass Drupal session cookie to your SSO provider

    $cookies = $request->cookies;

    // Check if the domain is playboy.docksal.site
    if ($request->getHost() === 'playboy-premium-api.docksal.site') {
        \Drupal::logger('smal_auth')->info('Skipping session validation for local domain: playboy.docksal.site');
        return TRUE; // Assume session is valid for local development
    }

    // get array of cookies
    $filtered = array_filter($cookies->all(), function ($key) {
      return strpos($key, 'SSESS') === 0;
    }, ARRAY_FILTER_USE_KEY);

    if (empty($filtered)) {
      return FALSE; // No session cookie found
    }

    $keys = array_keys($filtered);
    // sort the filtered
    usort($keys, function ($a, $b) {
      return strlen($a) - strlen($b);
    });

    $key = reset($keys); // Get the first key
    $session_id = $filtered[$key]; // Get the first session ID

    $loginUrl = 'https://login.playboy.de';
    if (getenv('PLATFORM_BRANCH') === 'staging') {
      $loginUrl = 'https://login.stage.playboy.de';
    }

    // Use the session ID to make a request to your SSO provider
    // Example HTTP call using Guzzle (Drupal's HTTP client)
    try {
      $client = \Drupal::httpClient();
      $response = $client->request('GET', $loginUrl . '/api/session/check.json', [
        'headers' => [
          'Cookie' => $key . '=' . $session_id,
          'Accept' => 'application/json',
        ],
      ]);

      $data = json_decode($response->getBody(), true);
      return $data === 1;
    } catch (\Exception $e) {
      \Drupal::logger('smal_auth')->error("SSO check failed: " . $e->getMessage());
      return FALSE; // Fail-safe: Assume still logged in to not disrupt UX
    }
  }
}
