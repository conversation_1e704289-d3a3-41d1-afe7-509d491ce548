<?php

namespace Drupal\smal_auth\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Drupal\Core\DrupalKernel;
use Drupal\user\Entity\User;
use Drupal\user\UserInterface;
use Drupal\Core\Routing\TrustedRedirectResponse;
use Drupal\smal_auth\Sso\SsoClient;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Returns responses for SMAL Auth routes.
 */
class SmalAuthController extends ControllerBase
{
  /**
   * token to validate against the plenigo APIs
   *
   * @var string
   */
  // production token
  private $_plenigoToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJBY2Nlc3NHcm91cHMiOiJDSEVDS09VVDtBQ0NFU1NfUklHSFRTO1NFVFRJTkdTO0lOVk9JQ0VTO09SREVSUztDVVNUT01FUlM7Q0FMTEJBQ0tTO1BST0RVQ1RTO1NVQlNDUklQVElPTlM7VFJBTlNBQ1RJT05TO1NUQVRVUyIsIkFjY2Vzc1JpZ2h0cyI6IkFQSV9SRUFEX1dSSVRFIiwiQ29tcGFueUlkIjoiQVU4SVhNVEJCTzJVS08zN1c2NUwiLCJJZCI6IjJwQ29OeUwxeGI4b2RZYTNPcFJVNElLaDZwNCJ9.Ie-Agun7csYvtqws83igDywmHFX85wrJZKOQ7Hp9SQU';
  // staging token
  // private $_plenigoToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJBY2Nlc3NHcm91cHMiOiJDSEVDS09VVDtDVVNUT01FUlM7T1JERVJTO1NUQVRVUztBQ0NFU1NfUklHSFRTO0FDQ09VTlRJTkdTO0FDVElWSVRJRVM7QU5BTFlUSUNTO0FQUFNUT1JFUztDQUxMQkFDS1M7RE9XTkxPQURTO0lNUE9SVFM7SU5WT0lDRVM7TUFJTFM7U0ZUUDtWT1VDSEVSUztUUkFOU0FDVElPTlM7U1VCU0NSSVBUSU9OUztQUk9EVUNUUztQUk9DRVNTRVM7V0FMTEVUUztTRVRUSU5HUyIsIkFjY2Vzc1JpZ2h0cyI6IkFQSV9SRUFEX1dSSVRFIiwiQ29tcGFueUlkIjoiRkwzWTk5SFNYMjdJNDNWSktUR0IiLCJJZCI6IjJtc0Q2YWUzOU5lUUlDN01ZOXdLSEZFbzhmciJ9.rVLBWupujxqxwiQqYqz4m41culUuDGqVS1tzpkh6nj8';

  /**
   * plenigo API URL
   *
   * @var string
   */
  private $_plenigoApiURL = 'api.plenigo.com/api/v3.0';
  private $_loginURL = 'https://login.playboy.de';
  private $_playboyDeURL = 'https://www.playboy.de';

  private $_accessRightAllAccess = 'playboy-all-access';
  private $_accessRightPlus = 'playboy-plus';

  /**
   * The request stack.
   *
   * @var \Symfony\Component\HttpFoundation\RequestStack
   */
  protected $requestStack;
  protected $ssoClient;

  /**
   * Constructs an EpCareRedirectController object.
   *
   * @param \Symfony\Component\HttpFoundation\RequestStack $request_stack
   *   The request stack.
   */
  public function __construct(RequestStack $request_stack, SsoClient $ssoClient)
  {
    $this->requestStack = $request_stack;
    $this->ssoClient = $ssoClient;
    if (getenv('PLATFORM_BRANCH') === 'staging') {
      $this->_loginURL = 'https://login.stage.playboy.de';
      $this->_playboyDeURL = 'https://stage.playboy.de';
    }
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container)
  {
    return new static(
      $container->get('request_stack'),
      $container->get('smal_auth.sso_client')
    );
  }

  /**
   * Builds the response.
   */
  public function authenticate()
  {
    // check if session is passed
    $session = $this->requestStack->getCurrentRequest()->query->get('session');
    if (is_null($session)) {
      \Drupal::logger('smal_auth')->error('No session token passed for user, back to login redirect');
      return new TrustedRedirectResponse($this->_loginURL . '?drupalerror=1');
    }

    // prepare HTTP client
    $http = \Drupal::httpClient();

    try {

      // validate user session token
      $response = $http->request(
        'GET',
        'https://' . $this->_plenigoApiURL . '/sessions/validate',
        [
          'query' => ['sessionToken' => $session],
          'headers' => ['X-plenigo-token' => $this->_plenigoToken]
        ]
      );

      // check if response is valid
      $json = json_decode($response->getBody()->getContents(), true);
      if (!isset($json['id'], $json['customerId'], $json['type']) || $json['type'] !== 'CUSTOMER_SESSION') {
        \Drupal::logger('smal_auth')->error('Data passed from plenigo seems incomplete (1), back to login redirect');
        \Drupal::logger('smal_auth')->error(json_encode($json));
        return new TrustedRedirectResponse($this->_loginURL . '?drupalerror=2');
      }

      // fetch user data
      $response = $http->request(
        'GET',
        'https://' . $this->_plenigoApiURL . '/sessions/customerData',
        [
          'query' => ['sessionToken' => $session],
          'headers' => ['X-plenigo-token' => $this->_plenigoToken]
        ]
      );

      // check if response is valid
      $customer = json_decode($response->getBody()->getContents(), true);
      if (!isset($customer['email'], $customer['customerId'])) {
        \Drupal::logger('smal_auth')->error('Data passed from plenigo seems incomplete (2), back to login redirect');
        \Drupal::logger('smal_auth')->error(json_encode($json));
        return new TrustedRedirectResponse($this->_loginURL . '?drupalerror=3');
      }

      // fetch access rights
      $response = $http->request(
        'GET',
        'https://' . $this->_plenigoApiURL . '/customers/' . $customer['customerId'] . '/accessRights',
        [
          'headers' => ['X-plenigo-token' => $this->_plenigoToken]
        ]
      );

      // check if response is valid
      $rights = json_decode($response->getBody()->getContents(), true);
      $activeRightIds = [];
      if (isset($rights['items']) && !empty($rights['items'])) {
        // extract valid access rights
        $activeRights = array_filter($rights['items'], function ($item) {

          // get start time of item
          $startTime = time() + 200000;
          if (isset($item['lifeTimeStart']) && !empty($item['lifeTimeStart'])) {
            $startTime = strtotime($item['lifeTimeStart']);
          }

          // get end time of item
          $endTime = strtotime('2090-11-21T00:00:00Z');
          if (isset($item['lifeTimeEnd']) && !empty($item['lifeTimeEnd'])) {
            $endTime = strtotime($item['lifeTimeEnd']);
          }

          // get blocked state of item
          $blocked = false;
          if (isset($item['blocked']) && $item['blocked'] == 1) {
            $blocked = true;
          }

          $now = time();

          //@TODO proper check if valid
          // return $date['yday'] == '283';
          return !$blocked && $now >= $startTime && ($now <= $endTime);
        });

        if (!empty($activeRights)) {
          $activeRightIds = array_map(function ($item) {
            return $item['accessRightUniqueId'];
          }, $activeRights);
        }
      }

      // no role by default
      $newRole = '';

      if (in_array($this->_accessRightAllAccess, $activeRightIds)) {
        $newRole = 'subscriber';
      } elseif (in_array($this->_accessRightPlus, $activeRightIds)) {
        $newRole = 'plus';
      }

      // check if user exists
      $account = \Drupal::entityTypeManager()->getStorage('user')->loadByProperties(['mail' => $customer['email']]);
      $user = null;

      // register new or login
      if (is_null($account) || count($account) <= 0) {

        // register scenario
        $user = User::create();
        $user->setUsername($customer['email']);
        $user->setPassword($this->_generateRandomString(64));
        if ($newRole !== '') {
          $user->addRole($newRole);
        }
        $user->setEmail($customer['email']);
        $user->enforceIsNew();
        $user->activate();
        $user->save();

        // by default role presse is setup, unset again now
        $user->removeRole('presse');
        $user->save();
      } else {

        // login scenario
        $user = reset($account);

        // remove all roles (to ensure only the right ones are set)
        foreach ($user->getRoles() as $role) {
          if (!in_array($role, ['authenticated', 'presse', 'administrator', 'redakteur', 'site_manager', 'autor', 'gewinnspiel', 'pictorials'])) {
            $user->removeRole($role);
          }
        }

        // add role identified by plenigo
        if ($newRole !== '') {
          $user->addRole($newRole);
        }

        // save changes to user
        $user->save();
      }

      // finally login user
      user_login_finalize($user);

      $return = '/';
      if (!is_null($this->requestStack->getCurrentRequest()->query->get('redirect'))) {
        $newUrl = $this->requestStack->getCurrentRequest()->query->get('redirect');
        $newUrl = str_replace('#', '%23', $newUrl);
        $return = $newUrl;
      }
      return new RedirectResponse($return);
    } catch (\Exception $exception) {
      \Drupal::logger('smal_auth')->error('Exception caught, check next log for details, back to login redirect');
      \Drupal::logger('smal_auth')->error($exception->getMessage());
      return new TrustedRedirectResponse($this->_loginURL . '?drupalerror=4');
    }
  }

  public function checkSession()
  {
    $request = $this->requestStack->getCurrentRequest();
    $is_valid = $this->ssoClient->checkSession($request);
    return new JsonResponse([
      'sso_session_active' => $is_valid,
    ]);
  }

  public function logout()
  {
    $currentUser = \Drupal::currentUser();
    if ($currentUser->isAuthenticated()) {
      user_logout();
      return new JsonResponse(['status' => 'success', 'message' => 'User logged out.']);
    }
    return new JsonResponse(['status' => 'noop', 'message' => 'User already logged out.']);
  }

  public function redirectlogout()
  {
    $currentUser = \Drupal::currentUser();
    if ($currentUser->isAuthenticated()) {
      user_logout();
    }
    return new TrustedRedirectResponse($this->_playboyDeURL . '/sso/redirectlogout');
  }

  public function girlinfo_to_girl($id)
  {

    $entity_storage = $this->entityTypeManager()->getStorage('girl_info');
    $ids = $entity_storage->getQuery()
      ->condition('id', $id)
      ->condition('status', '1')
      ->execute();

    if (empty($ids)) {
      return new TrustedRedirectResponse('https://premium.playboy.de?e');
    }

    $entity = $entity_storage->load(current($ids));
    $id = $entity->get('girl')->entity->id();

    if (empty($entity)) {
      return new TrustedRedirectResponse('https://premium.playboy.de?e');
    }

    return new TrustedRedirectResponse('/girl/' . $id);
  }
  public function girlinfo_to_girl_gallery($id)
  {
    $entity_storage = $this->entityTypeManager()->getStorage('girl_info');
    $ids = $entity_storage->getQuery()
      ->condition('id', $id)
      ->condition('status', '1')
      ->execute();

    if (empty($ids)) {
      return new TrustedRedirectResponse('https://premium.playboy.de?e');
    }

    $entity = $entity_storage->load(current($ids));
    $girlId = $entity->get('girl')->entity->id();
    $girlinfoId = $id;

    if (empty($entity)) {
      return new TrustedRedirectResponse('https://premium.playboy.de?e');
    }

    $returnUrl = \Drupal::request()->query->get('return');

    if ($returnUrl) {
      return new TrustedRedirectResponse('/girl/' . $girlId . '/' . $girlinfoId . '?return=' . rawurlencode($returnUrl));
    } else {
      return new TrustedRedirectResponse('/girl/' . $girlId . '/' . $girlinfoId);
    }
  }


  private function _generateRandomString($length = 10)
  {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ&$%§#+_-';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
      $randomString .= $characters[random_int(0, $charactersLength - 1)];
    }

    return $randomString;
  }
}
