(function (Drupal, once) {
  Drupal.behaviors.ssoChecker = {
    attach(context, settings) {
      once('sso-checker', 'body', context).forEach(() => {
        const lastCheck = localStorage.getItem('smal_sso_check_time');
        const now = Date.now();

        // Check only if more than 1 hour (3600000 ms) has passed
        if (!lastCheck || now - parseInt(lastCheck, 10) > 3600000) {
          // add credentials to fetch
          fetch(`https://login${ document.location.host === 'stage.playboy.de' ? '.stage' : '' }.playboy.de/api/session/check.json`, {
            credentials: 'include',
          })
            .then(res => res.json())
            .then(data => {
              localStorage.setItem('smal_sso_check_time', now.toString());
              if (data === 1) {
                window.location.href = `https://login${ document.location.host === 'stage.playboy.de' ? '.stage' : '' }.playboy.de?return=${ encodeURIComponent(window.location.href) }`;
              }
            })
            .catch(err => {
              // Still update timestamp to prevent spammy retries
              localStorage.setItem('smal_sso_check_time', now.toString());
            });
        }
      });
    },
  };
})(Drupal, once);