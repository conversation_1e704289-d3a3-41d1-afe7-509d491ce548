Drupal.behaviors.pb_focals = {
  attach: function (context, settings) {
    //Using once() to apply the myCustomBehaviour effect when you want to run just one function.
    if (typeof jQuery != 'undefined') {
      jQuery('.field--name-field-media-image .image-preview__img-wrapper, .field--name-field-media-image-1 .image-preview__img-wrapper, .js-media-library-add-form-added-media .media-library-add-form__preview').each(function (element) {
        var $w = jQuery(this);
        if (!$w.hasClass('processed')) {
          var $p = $w.closest('.media-image-edit-form');

          if ($p.length <= 0) {
            $p = $w.closest('.media-public-image-edit-form');
          }

          if ($p.length <= 0) {
            $p = $w.closest('.js-media-library-add-form-added-media');
            $w.html(jQuery('<div class="image-preview__img-wrapper"></div>').html($w.find('img')));
            $w = $w.children('div');
          }
          var $n = jQuery('<div class="focal"></div>');
          var $x = $p.find('.field--name-field-focal-point-x input');
          var $y = $p.find('.field--name-field-focal-point-y input');
          var x = $x.val();
          var y = $y.val();
          if (x != '' && y != '') {
            $n.css({
              top: y + '%',
              left: x + '%'
            });
          }
          $w.append($n);
          $w.addClass('process');
        }
      });
    }
  }
};


jQuery(function () {

  var $ = jQuery;

  $('body').on('click', '.field--name-field-media-image img, .field--name-field-media-image-1 img, .media-library-add-form__preview img', function (e) {
    var $w = $(this);
    var $p = $w.closest('.media-image-edit-form');

    if ($p.length <= 0) {
      $p = $w.closest('.media-public-image-edit-form');
    }

    if ($p.length <= 0) {
      $p = $w.closest('.media-library-add-form__media');
    }
    var ww = $w.width();
    var wh = $w.height();
    var x = e.pageX - $w.offset().left;
    var y = e.pageY - $w.offset().top;
    var focalx = Math.round((x / ww) * 10000) / 100;
    var focaly = Math.round((y / wh) * 10000) / 100;
    $p.find('.field--name-field-focal-point-x input').val(focalx);
    $p.find('.field--name-field-focal-point-y input').val(focaly);
    $p.find('.focal').css({
      top: focaly + '%',
      left: focalx + '%',
    });
  });

  // if ($('.field--name-field-media-image').length > 0) {
  //   $('.field--name-field-media-image').each(function () {
  //     if ($(this).find('image-preview__img-wrapper').length > 0) {
  //       var w = $('.form-item--field-media-image-0 .image-preview__img-wrapper');
  //       var ww = w.width();
  //       var wh = w.height();
  //       w.click(function (e) {
  //         var x = e.pageX - this.offsetLeft;
  //         var y = e.pageY - this.offsetTop;
  //         $('input[name="field_focal_point_x[0][value]"]').val((x / ww) * 100);
  //         $('input[name="field_focal_point_y[0][value]"]').val((y / wh) * 100);
  //       });
  //     }
  //   });
  //   var w = $('.form-item--field-media-image-0 .image-preview__img-wrapper');
  //   var ww = w.width();
  //   var wh = w.height();
  //   w.click(function (e) {
  //     var x = e.pageX - this.offsetLeft;
  //     var y = e.pageY - this.offsetTop;
  //     $('input[name="field_focal_point_x[0][value]"]').val((x / ww) * 100);
  //     $('input[name="field_focal_point_y[0][value]"]').val((y / wh) * 100);
  //   });
  // }
});
