<?php

namespace Drupal\pb_flag\Plugin\rest\resource;

use <PERSON><PERSON>al\rest\ModifiedResourceResponse;
use Dr<PERSON>al\rest\Plugin\ResourceBase;
use <PERSON>upal\user\Entity\User;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

/**
 * Provides a flag rest resource.
 *
 * Provides a resource to flag and unflag entities by uid, entity_id,
 * entity_type and flag_id.
 *
 * @RestResource(
 *   id = "flag_rest_resource",
 *   label = @Translation("Flag rest resource"),
 *   uri_paths = {
 *     "create" = "/api/v1/flag/create",
 *   }
 * )
 */
class FlagRestResource extends ResourceBase {

  /**
   * A current user instance.
   *
   * @var \Drupal\Core\Session\AccountProxyInterface
   */
  protected $currentUser;

  /**
   * The flag service.
   *
   * @var \Drupal\flag\FlagService
   */
  protected $flagService;

  /**
   * The flag service.
   *
   * @var \Drupal\Core\Entity\EntityTypeManager
   */
  protected $entityTypeManager;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    $instance = parent::create($container, $configuration, $plugin_id, $plugin_definition);
    $instance->logger = $container->get('logger.factory')->get('pb_flag');
    $instance->currentUser = $container->get('current_user');
    $instance->entityTypeManager = $container->get('entity_type.manager');
    $instance->flagService = $container->get('flag');
    return $instance;
  }

  /**
   * Responds to POST requests. Toggles the flag state.
   *
   * Required body example:
   * {"flag_id":"girl_flag","entity_type":"girl","entity_id":"161","uid":"1"}
   *
   * @param string $data
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   *
   * @throws \Symfony\Component\HttpKernel\Exception\HttpException
   *   Throws exception expected.
   */
  public function post($data) {

    // You must to implement the logic of your REST Resource here.
    // Use current user after pass authentication to validate access.
    if (!$this->currentUser->hasPermission('restful post flag_rest_resource')) {
      throw new AccessDeniedHttpException();
    }
    //get user
    $user = \Drupal::currentUser();
    $entity = $this->entityTypeManager->getStorage($data['entity_type'])->load($data['entity_id']);
    $flag_service = \Drupal::service('flag');
    $flag = $flag_service->getFlagById($data['flag_id']); // replace by flag machine name

    // Check if already flagged.
    $flagging = $flag_service->getFlagging($flag, $entity, $user);
    if (!$flagging) {
      $flag_service->flag($flag, $entity, $user);
    }
    else {
      $flag_service->unflag($flag, $entity, $user);
      return new ModifiedResourceResponse('unflag', 200);
    }
    return new ModifiedResourceResponse("flag", 200);
  }

}
