<?php

namespace <PERSON><PERSON><PERSON>\playboy_public_image_saver\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;

/**
 * Returns responses for Playboy Public Image Saver routes.
 */
class PlayboyPublicImageSaverController extends ControllerBase {

  /**
   * Builds the response.
   */
  public function build() {

    $build['content'] = [
      '#type' => 'item',
      '#markup' => $this->t('It works!'),
    ];

    return $build;
  }

}
