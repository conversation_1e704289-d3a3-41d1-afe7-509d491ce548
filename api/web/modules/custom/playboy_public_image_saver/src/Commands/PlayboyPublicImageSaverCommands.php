<?php

namespace Drupal\playboy_public_image_saver\Commands;

use Consolidation\OutputFormatters\StructuredData\RowsOfFields;
use Drush\Commands\DrushCommands;
use Drupal\Core\Datetime\DrupalDateTime;
use Dr<PERSON>al\datetime\Plugin\Field\FieldType\DateTimeItemInterface;
use <PERSON><PERSON>al\playboy_public_image_saver\PlayboyPublicImageSaver;

/**
 * A Drush commandfile.
 *
 * In addition to this file, you need a drush.services.yml
 * in root of your module, and a composer.json file that provides the name
 * of the services file to use.
 *
 * See these files for an example of injecting Drupal services:
 *   - http://cgit.drupalcode.org/devel/tree/src/Commands/DevelCommands.php
 *   - http://cgit.drupalcode.org/devel/tree/drush.services.yml
 */
class PlayboyPublicImageSaverCommands extends DrushCommands
{

  /**
   * An example of the table output format.
   *
   * @param array $options An associative array of options whose values come from cli, aliases, config, etc.
   *
   * @field-labels
   *   group: Group
   *   token: Token
   *   name: Name
   * @default-fields group,token,name
   *
   * @command playboy_public_image_saver:generate
   * @aliases pbis:generate
   *
   * @filter-default-field name
   * @return \Consolidation\OutputFormatters\StructuredData\RowsOfFields
   */
  public function generate($options = ['format' => 'table'])
  {
    $query = \Drupal::entityQuery('girl_info');
    $query->accessCheck(FALSE)
      ->condition('status', 1);
    $query->notExists('field_second_focal_point_x');
    $count = $query->count()->execute();

    echo "Count: " . $count . "\n";

    $query = \Drupal::entityQuery('girl_info');
    $girlInfos = $query
      ->accessCheck(FALSE)
      ->condition('status', 1)
      ->notExists('field_second_focal_point_x')
      ->range(0, 50)
      ->execute();

    $girl_infos = \Drupal::entityTypeManager()->getStorage('girl_info')->loadMultiple($girlInfos);

    foreach ($girl_infos as $girl_info) {
      echo "Start: girl_info " . $girl_info->id() . "\n";

      $handler = new PlayboyPublicImageSaver();
      $handler->handle($girl_info);

      echo "Updated: girl_info " . $girl_info->id() . "\n";
    }

    return true;
  }



  /**
   * Run through all girl infos and set plus access to true.
   *
   * @command playboy_public_image_saver:plus_access_all_to_true
   * @aliases pbis:plus_access_all_to_true
   *
   * @filter-default-field name
   * @return \Consolidation\OutputFormatters\StructuredData\RowsOfFields
   */
  public function plus_access_all_to_true($options = ['format' => 'table'])
  {

    // add condition for only the last three months

    // load girl des tages IDs
    $node = \Drupal::entityTypeManager()->getStorage('node')->load(PlayboyPublicImageSaver::$node_id);
    $girl_des_tages_ids = array_column($node->get('field_girl_infos')->getValue(), 'target_id');
    $girl_des_tages_ids = array_merge(array_column($node->get('field_manual')->getValue(), 'target_id'), $girl_des_tages_ids);

    $girl_infos = \Drupal::entityTypeManager()->getStorage('girl_info')->loadMultiple($girl_des_tages_ids);
    foreach ($girl_infos as $girl_info) {
      echo "GdT Start: girl_info " . $girl_info->id() . "\n";
      $girl_info->set('field_plus_access', true);
      $girl_info->save();
      echo "GdT Updated: girl_info " . $girl_info->id() . "\n";
    }

    foreach (PlayboyPublicImageSaver::$plusCategories as $category) {
      $query = \Drupal::entityQuery('girl_info');
      $query->condition('status', 1);
      $query->condition('field_category', $category);
      $query->sort('descriptor_year', 'DESC');
      $query->sort('descriptor_month', 'DESC');
      $query->accessCheck(FALSE);
      $query->range(0, 3);
      $ids = $query->execute();
      $girl_infos = \Drupal::entityTypeManager()->getStorage('girl_info')->loadMultiple($ids);

      foreach ($girl_infos as $girl_info) {
        echo "Start: girl_info " . $girl_info->id() . "\n";
        $girl_info->set('field_plus_access', true);
        $girl_info->save();
        echo "Updated: girl_info " . $girl_info->id() . "\n";
      }
    }

    return true;
  }



  /**
   * Run through all plus girl infos and checks which ones should be false
   *
   * @command playboy_public_image_saver:plus_access_true_to_false
   * @aliases pbis:plus_access_true_to_false
   *
   * @filter-default-field name
   * @return \Consolidation\OutputFormatters\StructuredData\RowsOfFields
   */
  public function plus_access_true_to_false($options = ['format' => 'table'])
  {

    // load girl des tages IDs
    $node = \Drupal::entityTypeManager()->getStorage('node')->load(PlayboyPublicImageSaver::$node_id);
    $girl_info_ids = array_column($node->get('field_girl_infos')->getValue(), 'target_id');
    $girl_info_ids = array_merge(array_column($node->get('field_manual')->getValue(), 'target_id'), $girl_info_ids);

    foreach (PlayboyPublicImageSaver::$plusCategories as $category) {
      $query = \Drupal::entityQuery('girl_info');
      $query->condition('status', 1);
      $query->condition('field_category', $category);
      $query->sort('descriptor_year', 'DESC');
      $query->sort('descriptor_month', 'DESC');
      $query->accessCheck(FALSE);
      $query->range(0, 3);
      $ids = $query->execute();
      $girl_info_ids = array_merge($girl_info_ids, $ids);
    }

    // check all that are plus accessible
    $query = \Drupal::entityQuery('girl_info');
    $query->condition('field_plus_access', true, '=');
    $query->condition('id', $girl_info_ids, 'NOT IN');
    $query->accessCheck(FALSE);

    $girl_info_ids = $query->execute();
    $girl_infos = \Drupal::entityTypeManager()->getStorage('girl_info')->loadMultiple($girl_info_ids);

    foreach ($girl_infos as $girl_info) {
      echo "save: girl_info " . $girl_info->id() . "\n";
      $girl_info->save();
      echo "Done: girl_info " . $girl_info->id() . "\n";
    }

    return true;
  }



  /**
   * Night process to sync the "galerie des tages" from playboy.de to premium
   *
   * @command playboy_public_image_saver:plus_access_sync_galerie_des_tages
   * @aliases pbis:plus_access_sync_galerie_des_tages
   *
   * @filter-default-field name
   * @return \Consolidation\OutputFormatters\StructuredData\RowsOfFields
   */
  public function plus_access_sync_galerie_des_tages($options = ['format' => 'table'])
  {

    // prepare HTTP client
    $http = \Drupal::httpClient();
    $response = $http->request(
      'GET',
      'https://www.playboy.de/api/v1/galerie-des-tages',
      []
    );
    $data = json_decode($response->getBody(), true);

    if (!isset($data) || !is_array($data) || count($data) !== 8) {
      echo $response->getBody();
      return false;
    }

    $ids = [];


    foreach ($data as $gallery) {
      $id = $gallery['field_girl_info_id'];
      if ($id === '') {
        $id = $gallery['id'];
      }
      array_push($ids, $id);
    }

    $node = \Drupal::entityTypeManager()->getStorage('node')->load(PlayboyPublicImageSaver::$node_id);
    $node->set('field_girl_infos', $ids);
    $node->save();
  }
}
