<?php

namespace Drupal\playboy_public_image_saver;

use Drupal\file\Entity\File;
use Drupal\Core\File\FileSystemInterface;
use <PERSON><PERSON>al\image\Entity\ImageStyle;

class PlayboyPublicImageSaver
{
  public static $plusCategories = ['2245', '10199', '2274'];
  public static $node_id = 76;

  public function handle(\Drupal\pb_girl_info\Entity\GirlInfo $girl_info)
  {

    // load girl des tages IDs
    $node = \Drupal::entityTypeManager()->getStorage('node')->load(PlayboyPublicImageSaver::$node_id);
    $girl_des_tages_ids = array_column($node->get('field_girl_infos')->getValue(), 'target_id');
    $girl_des_tages_ids = array_merge(array_column($node->get('field_manual')->getValue(), 'target_id'), $girl_des_tages_ids);

    $girl_info_ids = [];
    foreach (PlayboyPublicImageSaver::$plusCategories as $category) {
      $query = \Drupal::entityQuery('girl_info');
      $query->condition('status', 1);
      $query->accessCheck(FALSE);
      $query->condition('field_category', $category);
      $query->sort('descriptor_year', 'DESC');
      $query->sort('descriptor_month', 'DESC');
      $query->range(0, 3);
      $ids = $query->execute();
      $girl_info_ids = array_merge($girl_info_ids, $ids);
    }

    $new_value = "0";
    $current_value = $girl_info->get('field_plus_access')->value;
    // check if the field descriptor_month and descriptor_year are within the last three months
    if (in_array($girl_info->id(), $girl_info_ids) || in_array($girl_info->id(), $girl_des_tages_ids)) {
      $new_value = "1";
    }

    if ($new_value !== $current_value) {
      $girl_info->set('field_plus_access', $new_value);
      $girl_info->save();
    }


    // get the public images field
    $public_images = $girl_info->get('field_public_images')->getValue();

    $count_images = 0;
    $count_videos = 0;
    $new_focal_x1 = '50';
    $new_focal_y1 = '50';
    $new_focal_x2 = '50';
    $new_focal_y2 = '50';
    $new_focal_x3 = '50';
    $new_focal_y3 = '50';

    $galleries = $girl_info->get('galleries')->referencedEntities();
    if (count($galleries) > 0) {
      foreach ($galleries as $gallery) {
        if ($gallery->get('status')->value == 1) { // Check if the gallery is published
          $count_images += count($gallery->get('field_media_slideshow')->referencedEntities());
          $count_videos += count($gallery->get('field_videos')->referencedEntities());
        }
      }

      // get the first gallery
      $gallery = $galleries[0];
      $images = $gallery->get('field_media_slideshow')->referencedEntities();
      if (count($images) > 0) {
        $image = $images[0];
        $focal_x = $image->get('field_focal_point_x')->value;
        $focal_y = $image->get('field_focal_point_y')->value;
        if (!empty($focal_x) && !empty($focal_y)) {
          $new_focal_x1 = $focal_x;
          $new_focal_y1 = $focal_y;
        }

        if (count($images) > 1) {
          $image = $images[1];
          $focal_x = $image->get('field_focal_point_x')->value;
          $focal_y = $image->get('field_focal_point_y')->value;
          if (!empty($focal_x) && !empty($focal_y)) {
            $new_focal_x2 = $focal_x;
            $new_focal_y2 = $focal_y;
          }

          if (count($images) > 2) {
            $image = $images[2];
            $focal_x = $image->get('field_focal_point_x')->value;
            $focal_y = $image->get('field_focal_point_y')->value;
            if (!empty($focal_x) && !empty($focal_y)) {
              $new_focal_x3 = $focal_x;
              $new_focal_y3 = $focal_y;
            }
          }
        }
      }
    }

    $image_current_value = $girl_info->get('field_image_count')->value;
    $video_current_value = $girl_info->get('field_video_count')->value;
    $focal_current_x1 = $girl_info->get('field_main_focal_point_x')->value;
    $focal_current_y1 = $girl_info->get('field_main_focal_point_y')->value;
    $focal_current_x2 = $girl_info->get('field_second_focal_point_x')->value;
    $focal_current_y2 = $girl_info->get('field_second_focal_point_y')->value;
    $focal_current_x3 = $girl_info->get('field_third_focal_point_x')->value;
    $focal_current_y3 = $girl_info->get('field_third_focal_point_y')->value;
    if (
      $image_current_value != $count_images ||
      $video_current_value != $count_videos ||
      $focal_current_x1 != $new_focal_x1 ||
      $focal_current_y1 != $new_focal_y1 ||
      $focal_current_x2 != $new_focal_x2 ||
      $focal_current_y2 != $new_focal_y2 ||
      $focal_current_x3 != $new_focal_x3 ||
      $focal_current_y3 != $new_focal_y3
    ) {
      $girl_info->set('field_image_count', $count_images);
      $girl_info->set('field_video_count', $count_videos);
      $girl_info->set('field_main_focal_point_x', $new_focal_x1);
      $girl_info->set('field_main_focal_point_y', $new_focal_y1);
      $girl_info->set('field_second_focal_point_x', $new_focal_x2);
      $girl_info->set('field_second_focal_point_y', $new_focal_y2);
      $girl_info->set('field_third_focal_point_x', $new_focal_x3);
      $girl_info->set('field_third_focal_point_y', $new_focal_y3);
      $girl_info->save();
    }



    // if the public images field is empty, get the first 3 images from the first gallery
    if (empty($public_images) || count($public_images) == 0) {

      // prepare the file paths
      $public_filepath = 'public://public-images/' . date('Y') . '-' . date('m') . '/' . rand();
      $file_system = \Drupal::service('file_system');

      // if there are galleries, get the first one
      if (count($galleries) > 0) {
        $gallery = $galleries[0];

        // get the images from the gallery
        $images = $gallery->get('field_media_slideshow')->referencedEntities();

        if (count($images) > 0) {

          // loop through the first 3 images
          for ($i = 0; $i < 3; $i++) {

            // if the image exists
            if (isset($images[$i])) {

              // get the image
              $image = $images[$i];

              // if the image has the media image field
              if ($image->hasField('field_media_image')) {
                $file = $image->get('field_media_image')->entity;

                // if the file is a File
                if ($file instanceof \Drupal\file\Entity\File) {

                  // get full URI of file
                  $filepath = $file->getFileUri();

                  $wrapper = \Drupal::service('stream_wrapper_manager')->getViaScheme('private');
                  $path = $wrapper->getDirectoryPath() . '/' . \Drupal\Core\StreamWrapper\StreamWrapperManager::getTarget($filepath);

                  // prepare file system and folder
                  $file_system->prepareDirectory($public_filepath, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);

                  try {

                    // if it's the second and third image, save it in the temporary folder
                    if ($i > 0) {
                      $url_to_sign = 'https://api.premium.playboy.de/image/token?token=j3usz77Jiw9oOqks7Hmd73uHwpP9P0P9&url=https://api.premium.playboy.de/system/files' . $path . '%3Fwidth=600';

                      $client = \Drupal::httpClient();
                      $response = $client->get($url_to_sign);

                      if ($response->getStatusCode() !== 200) {
                        continue; // skip if the response is not 200
                      }
                      // get the contents of the response
                      $contents = $response->getBody()->getContents();

                      if (empty($contents)) {
                        continue; // skip if the contents are empty
                      }

                      $file_system->saveData($contents, $public_filepath . '/' . basename($filepath), FileSystemInterface::EXISTS_REPLACE);
                    } else {
                      // copy the file to the public folder as is for the first image
                      $file_system->copy($filepath, $public_filepath . '/' . basename($filepath), FileSystemInterface::EXISTS_REPLACE);
                    }

                    // create a new file entity
                    $public_file = File::create([
                      'filename' => basename($filepath),
                      'uri' => $public_filepath . '/' . basename($filepath),
                      'status' => 1,
                      'uid' => 1,
                    ]);

                    // save the file entity
                    $public_file->save();

                    // Add the public file to the public_images field
                    $public_images[] = ['target_id' => $public_file->id()];
                  } catch (\Throwable $th) {
                    echo "Error2: " . $th->getMessage() . "\n";
                    \Drupal::logger('playboy_public_image_saver')->error($th->getMessage());
                  }
                }
              }
            }
          }
        } else {

          // take the main image as the first image
          $first_media = $gallery->get('field_image')->referencedEntities();
          if (count($first_media) > 0) {
            $first_file = $first_media[0]->get('field_media_image')->entity;
            if ($first_file instanceof \Drupal\file\Entity\File) {

              // get full URI of file
              $first_filepath = $first_file->getFileUri();

              // prepare file system and folder
              $file_system->prepareDirectory($public_filepath, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);

              // copy the file to the public folder as is for the first image
              $file_system->copy($first_filepath, $public_filepath . '/' . basename($first_filepath), FileSystemInterface::EXISTS_REPLACE);

              // create a new file entity
              $public_file = File::create([
                'filename' => basename($first_filepath),
                'uri' => $public_filepath . '/' . basename($first_filepath),
                'status' => 1,
                'uid' => 1,
              ]);

              // save the file entity
              $public_file->save();

              // Add the public file to the public_images field
              $public_images[] = ['target_id' => $public_file->id()];
            }
          }
        }
      } else {

        // take the main image of the girl info as the first image
        $first_media = $girl_info->get('main_images')->referencedEntities();
        if (count($first_media) > 0) {
          $first_file = $first_media[0]->get('field_media_image')->entity;
          if ($first_file instanceof \Drupal\file\Entity\File) {

            // get full URI of file
            $first_filepath = $first_file->getFileUri();

            // prepare file system and folder
            $file_system->prepareDirectory($public_filepath, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);

            // copy the file to the public folder as is for the first image
            $file_system->copy($first_filepath, $public_filepath . '/' . basename($first_filepath), FileSystemInterface::EXISTS_REPLACE);

            // create a new file entity
            $public_file = File::create([
              'filename' => basename($first_filepath),
              'uri' => $public_filepath . '/' . basename($first_filepath),
              'status' => 1,
              'uid' => 1,
            ]);

            // save the file entity
            $public_file->save();

            // Add the public file to the public_images field
            $public_images[] = ['target_id' => $public_file->id()];
          }
        }
      }

      if (!empty($public_images)) {
        // Update the entity with the new public images
        $girl_info->set('field_public_images', $public_images);
        $girl_info->save();
      }
    }
  }
}
