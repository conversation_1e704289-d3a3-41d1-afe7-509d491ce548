<?php

use Dr<PERSON>al\playboy_public_image_saver\PlayboyPublicImageSaver;


/**
 * @file
 * Primary module hooks for Playboy Public Image Saver module.
 *
 * @DCG
 * This file is no longer required in Drupal 8.
 * @see https://www.drupal.org/node/2217931
 */

/**
 * Implements hook_entity_insert() and hook_entity_update().
 */
function playboy_public_image_saver_entity_insert(Drupal\Core\Entity\EntityInterface $entity)
{
  playboy_public_image_saver_update_entity_name($entity);
}

function playboy_public_image_saver_entity_update(Drupal\Core\Entity\EntityInterface $entity)
{
  playboy_public_image_saver_update_entity_name($entity);
}

/**
 * Updates the name of the entity.
 */
function playboy_public_image_saver_update_entity_name(Drupal\Core\Entity\EntityInterface $entity)
{
  // check for saves to the girl info entity
  if ($entity->getEntityTypeId() == 'girl_info' && $entity->bundle() == 'girl_info') {
    $girl_info = \Drupal::entityTypeManager()->getStorage('girl_info')->load($entity->id());
    $handler = new PlayboyPublicImageSaver();
    $handler->handle($girl_info);
  }
}
