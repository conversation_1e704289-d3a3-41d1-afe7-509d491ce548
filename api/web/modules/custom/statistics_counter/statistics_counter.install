<?php

/**
 * @file
 * Statistics Counter.
 */

use \Drupal\Core\Database\Database;

/**
 * Implements hook_install().
 */
function statistics_counter_install() {
  $db = Database::getConnection();
  $transaction = $db->startTransaction();

  $weekcount = [
    'type' => 'int',
    'unsigned' => TRUE,
    'not null' => TRUE,
    'default' => 0,
    'size' => 'medium',
    'description' => 'The total number of times the {node} has been viewed this week.',
  ];
  $monthcount = [
    'type' => 'int',
    'unsigned' => TRUE,
    'not null' => TRUE,
    'default' => 0,
    'size' => 'medium',
    'description' => 'The total number of times the {node} has been viewed this month.',
  ];
  $yearcount = [
    'type' => 'int',
    'unsigned' => TRUE,
    'not null' => TRUE,
    'default' => 0,
    'size' => 'medium',
    'description' => 'The total number of times the {node} has been viewed this year.',
  ];

  try {
    $schema = $db->schema();
    $schema->addField('node_counter', 'weekcount', $weekcount);
    $schema->addField('node_counter', 'monthcount', $monthcount);
    $schema->addField('node_counter', 'yearcount', $yearcount);

    // Ignore slave server temporarily to give time for the
    // saved block to be propagated to the slave.
    // db_ignore_slave();
  } catch (\Exception $e) {
    $transaction->rollback();
    watchdog_exception('php', $e);
    Drupal::messenger()->addError(t('Cannot create new fields'));
  }
}

/**
 * Implements hook_uninstall().
 */
function statistics_counter_uninstall() {
  $db = Database::getConnection();
  $transaction = $db->startTransaction();

  try {
    $schema = $db->schema();
    $schema->dropField('node_counter', 'weekcount');
    $schema->dropField('node_counter', 'monthcount');
    $schema->dropField('node_counter', 'yearcount');
    // Ignore slave server temporarily to give time for the
    // saved block to be propagated to the slave.
    // db_ignore_slave();
  } catch (\Exception $e) {
    $transaction->rollback();
    watchdog_exception('php', $e);
    Drupal::messenger()->addError(t('Cannot drop fields'));
  }
}

/**
 * Implements hook_enable().
 */
function statistics_counter_enable() {
  Database::getConnection()->update('node_counter')
    ->fields([
      'weekcount' => 'daycount',
      'monthcount' => 'daycount',
      'yearcount' => 'daycount'
    ])->execute();
}
