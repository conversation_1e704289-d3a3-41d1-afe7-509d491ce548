<?php

/**
 * @file
 * Statistics Counter.
 */

/**
 * Implements hook_cron().
 */
function statistics_counter_cron() {
  $config = Drupal::configFactory()->getEditable('statistics_counter.settings');
  // Get timestamps.
  $timestamp = $config->get('timestamp', 0);

  // Get date.
  $week = date('W') | 0;
  $month = date('n') | 0;
  $year = date('Y') | 0;

  $last_week = date('W', $timestamp) | 0;
  $last_month = date('n', $timestamp) | 0;
  $last_year = date('Y', $timestamp) | 0;

  $fields = [];

  if ($week != $last_week || $year != $last_year) {
    // Reset week counts.
    $fields['weekcount'] = 0;
  }

  if ($month != $last_month || $year != $last_year) {
    // Reset month counts.
    $fields['monthcount'] = 0;
  }

  if ($year != $last_year) {
    // Reset year counts.
    $fields['yearcount'] = 0;
  }

  if (!empty($fields)) {
    // Reset year counts.
    // TODO: Drupal Rector Notice: Please delete the following comment after you've made any necessary changes.
    // You will need to use `\Drupal\core\Database\Database::getConnection()` if you do not yet have access to the container here.
    Drupal::database()->update('node_counter')
      ->fields($fields)
      ->execute();
  }

  $config->set('timestamp', Drupal::time()->getRequestTime())->save();
}

/**
 * Implements hook_entity_property_info_alter().
 */
function statistics_counter_entity_property_info_alter(&$info) {
  $info['node']['properties']['weekcount'] = [
    'type' => 'integer',
    'label' => t('Week node view counter'),
    'description' => t('The total number of times the node has been viewed this week.'),
    'sanitized' => TRUE,
    'getter callback' => 'statistics_counter_weekcount_getter_callback',
  ];
  $info['node']['properties']['monthcount'] = [
    'type' => 'integer',
    'label' => t('Month node view counter'),
    'description' => t('The total number of times the node has been viewed this month.'),
    'sanitized' => TRUE,
    'getter callback' => 'statistics_counter_monthcount_getter_callback',
  ];
  $info['node']['properties']['yearcount'] = [
    'type' => 'integer',
    'label' => t('Year node view counter'),
    'description' => t('The total number of times the node has been viewed this year.'),
    'sanitized' => TRUE,
    'getter callback' => 'statistics_counter_yearcount_getter_callback',
  ];
}

/**
 * Getter callback for weekcount property.
 *
 * @param object $item
 *   A node object.
 *
 * @return int
 *   View counter.
 *
 * @see _statistics_counter_getter_callback()
 */
function statistics_counter_weekcount_getter_callback($item) {
  return _statistics_counter_getter_callback($item->nid, 'weekcount');
}

/**
 * Getter callback for monthcount property.
 *
 * @param object $item
 *   A node object.
 *
 * @return int
 *   View counter.
 *
 * @see _statistics_counter_getter_callback()
 */
function statistics_counter_monthcount_getter_callback($item) {
  return _statistics_counter_getter_callback($item->nid, 'monthcount');
}

/**
 * Getter callback for monthcount property.
 *
 * @param object $item
 *   A node object.
 *
 * @return int
 *   View counter.
 *
 * @see _statistics_counter_getter_callback()
 */
function statistics_counter_yearcount_getter_callback($item) {
  return _statistics_counter_getter_callback($item->nid, 'yearcount');
}

/**
 * Get node statistic counter.
 *
 * @param int $nid
 *   The node id.
 * @param string $counter
 *   Counter name.
 *
 * @return int
 *   Counter.
 */
function _statistics_counter_getter_callback($nid, $counter) {
  // TODO: Drupal Rector Notice: Please delete the following comment after you've made any necessary changes.
  // You will need to use `\Drupal\core\Database\Database::getConnection()` if you do not yet have access to the container here.
  return Drupal::database()->select('node_counter', 'nc')
    ->fields('nc', array($counter))
    ->condition('nid', $nid)
    ->execute()
    ->fetchField() | 0;
}
