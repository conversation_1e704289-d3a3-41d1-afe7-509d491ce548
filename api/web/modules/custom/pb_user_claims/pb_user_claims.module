<?php

/**
 * @file
 * Primary module hooks for pb_user_claims module.
 *
 * @DCG
 * This file is no longer required in Drupal 8.
 * @see https://www.drupal.org/node/2217931
 */

function pb_user_claims_openid_connect_claims_alter(array &$claims)
{
  $claims['active_subscription_name'] = [
    'scope' => 'profile',
    'title' => 'Active Subscription Name',
    'type' => 'string',
    'description' => 'The name of the active subscription',
  ];
  $claims['active_subscription_start_date'] = [
    'scope' => 'profile',
    'title' => 'Active Subscription start date',
    'type' => 'string',
    'description' => 'The start date of the active subscription',
  ];
  $claims['active_subscription_cancel_date'] = [
    'scope' => 'profile',
    'title' => 'Active Subscription end date',
    'type' => 'string',
    'description' => 'The end date of the active subscription',
  ];
  $claims['active_subscription_uuid'] = [
    'scope' => 'profile',
    'title' => 'Active Subscription UUID',
    'type' => 'string',
    'description' => 'The UUID of the active subscription',
  ];
  $claims['active_subscription_payment_method'] = [
    'scope' => 'profile',
    'title' => 'Active Subscription payment Method',
    'type' => 'string',
    'description' => 'The payment Method of the active subscription',
  ];
  $claims['subscription_data'] = [
    'scope' => 'profile',
    'title' => 'All subscription data',
    'type' => 'string',
    'description' => 'Contains all active and past subscriptions in a JSON format',
  ];
  $claims['login_data'] = [
    'scope' => 'profile',
    'title' => 'Last login\'s data',
    'type' => 'string',
    'description' => 'Contains the latest data transferred through the login request',
  ];
}

/**
 * Alter the user information provided by the identity provider.
 *
 * This hook is called after the user information has been fetched from the
 * identity provider's userinfo endpoint, and before authorization or
 * connecting a user takes place.
 *
 * Popular use cases for this hook are providing additional user information,
 * or 'translating' information from a format used by the identity provider
 * to a format that can be used by the OpenID Connect claim mapping.
 *
 * @param array $userinfo
 *   Array of returned user information from the identity provider.
 * @param array $context
 *   An associative array with context information:
 *   - tokens:      An array of tokens.
 *   - user_data:   An array of user and session information from the ID token.
 *   - plugin_id:   The plugin identifier.
 *
 * @ingroup openid_connect_api
 */
function pb_user_claims_openid_connect_userinfo_alter(array &$userinfo, array $context)
{
  // Add some custom information.
  if ($context['plugin_id'] == 'keycloak') {
    $subscriptions = $userinfo['subscriptions'];

    $userinfo['active_subscription_uuid'] = isset($userinfo['uuid']) ? $userinfo['uuid'] : '';
    $userinfo['login_data'] = json_encode($userinfo);

    if (!empty($subscriptions)) {

      try {

        foreach ($userinfo['subscriptions'] as $key => $info_subscription) {
          if (is_array($info_subscription)) {
            if (count($info_subscription) > 1) {
              $info_subscription['name'] = $info_subscription['period'] . ($info_subscription['period'] > 1 ? ' Monate' : ' Monat');
              $userinfo['subscriptions'][$key] = [
                'legacy-subscription' => $info_subscription
              ];
            }
          } else {
            unset($userinfo['subscriptions'][$key]);
          }
        }


        // enrich subscription data if available
        if (!isset($userinfo['uuid'])) {
          \Drupal::logger('pb_token')->error("No UUID found in response. Can't fetch additional information.");
          \Drupal::logger('pb_token')->error("User mail: " . $userinfo['email']);
        } else {
          $client = \Drupal::httpClient();

          $response = $client->post('https://webservices.covernet.de:8443/mex/wrd/ecattrinfo', [
            'headers' => [
              'C_BENUTZER_ID' => 'IMS4WS',
              'C_PASSWORT' => 'KNF4ZN83z0RT',
              'C_TRANSFER_ID' => 'COVERNET2_KM',
              'C_MANDANT' => 'KM',
              'C_ANW' => 'V',
              'C_KUNDE' => 'MEX',
              'Content-Type' => 'application/json'
            ],
            'body' => '{"attr_info":{"transaction":{"timestamp":"20180306192355","transaction_id":"PUBL00000556647","ext_system_type":"WEBSHOP","ext_system_id":"magento2.publisher.comwrap.host","ext_system_name":"COVER-Web-Shop.de"},"extsyscustomer":{"ext_system_id":"http://cover.staging2.comwrap.host","ext_customer_nr":"14928"},"address":{"timestamp":"20180306192355","ext_system_id":"magento2.publisher.comwrap.host","uuid":"' . $userinfo['uuid'] . '"},"customer_attributes":[{"timestamp":"20180306192355","attribute_status":"REQUEST","attribute_type":"SUBSCRARC","attribute_id":"*"}]}}'
          ]);

          $raw = $response->getBody()->getContents();
          $content = json_decode($raw, true);

          // first check if valid response
          if ($response->getStatusCode() !== 200) {
            \Drupal::logger('pb_token')->notice("Cover UUID " . $userinfo['uuid']);
            \Drupal::logger('pb_token')->notice("Response code: " . $response->getStatusCode());
            \Drupal::logger('pb_token')->error("Response: " . $raw);
          } else {

            // check if response contains the right data
            if (!isset($content['attr_info']['customer_attributes']) || count($content['attr_info']['customer_attributes']) < 1) {
              \Drupal::logger('pb_token')->error("No subscriptions found in response");
              \Drupal::logger('pb_token')->error(json_encode($content));
              \Drupal::logger('pb_token')->error(json_encode($userinfo));
              \Drupal::logger('pb_token')->error("END UUID: " . $userinfo['uuid']);
            } else {
              foreach ($content['attr_info']['customer_attributes'] as $info_subscription) {
                foreach ($userinfo['subscriptions'] as $key1 => $openid_subscription) {
                  foreach ($openid_subscription as $key2 => $openid_inner_subscription) {
                    if (
                      isset($info_subscription['uuid_orderpos'], $openid_inner_subscription['uuid'], $info_subscription['attribute_next_debit_amount'], $info_subscription['attribute_invoice_end_date'])
                      && $info_subscription['uuid_orderpos'] === $openid_inner_subscription['uuid']
                    ) {
                      $userinfo['subscriptions'][$key1][$key2]['amount'] = $info_subscription['attribute_next_debit_amount'];
                      $userinfo['subscriptions'][$key1][$key2]['invoice_end_date'] = $info_subscription['attribute_invoice_end_date'];
                    }
                  }
                }
              }
            }
          }
        }
      } catch (\Throwable $th) {
        \Drupal::logger('pb_token')->error("General error: " . json_encode($th));
      }

      $userinfo['subscription_data'] = isset($userinfo['subscriptions']) ? json_encode($userinfo['subscriptions']) : '';
    }
  }
}
