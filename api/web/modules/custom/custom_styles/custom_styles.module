<?php

use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_page_attachments_alter().
 */
function custom_styles_page_attachments_alter(array &$attachments) {
  // Check if the current page is an admin page.
  if (\Drupal::service('router.admin_context')->isAdminRoute()) {
    // Attach the admin_styles library.
    $attachments['#attached']['library'][] = 'custom_styles/admin_styles';
  }
}