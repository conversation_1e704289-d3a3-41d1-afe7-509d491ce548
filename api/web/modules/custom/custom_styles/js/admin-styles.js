
jQuery(document).ready(function ($) {
  let timeout = null;

  //custom magazine
  $('body').on('keyup', '.magazine-field-wrap .form-element--type-text', function () {
    const value = $(this).val();

    var thisEle = $(this);

    clearTimeout(timeout);
    timeout = setTimeout(function () {
      $('#gallery-dropdown').remove();
      const newDiv = $('<div></div>').attr('id', 'gallery-dropdown');
      $.get("https://www.playboy.de/api/v1/issues?title=" + value, { dataType: 'json' })
        .done(function (data) {
          console.log(data);
          data.forEach(function (item) {
            //const subDiv = $('<a></a>').prop('href', 'javascript:void;').text(item.name.replace('&amp;', '&').replace('&#039;', '\'') + ' (' + (item.descriptor_month < 10 ? '0' : '') + item.descriptor_month + '/' + item.descriptor_year + ') [Info:' + item.girl_info_id + '] [Girl:' + item.girl_id + ']').addClass('gallery-girl');

            var thisText = item.title + ' (' + item.nid + ')';
            var subDiv = $('<a href="javascript:void;" class="custom-magazin">'+thisText+'</a>');

            newDiv.append(subDiv);
          });
        })
        .catch(function (error) {
          const subDiv = $('<a></a>').prop('target', '_blank').prop('href', 'https://login.playboy.de/?return=https://www.playboy.de/').text('Please login to playboy.de').addClass('custom-magazin no-add');
          newDiv.append(subDiv);
        });

      $(thisEle).after(newDiv);
    }, 500);
  });

  $('body').on('click', '.custom-magazin:not(.no-add)', function () {
    const value = $(this).text();

    var thisEle = $(this).closest('td').find('.form-element--type-text');
    thisEle.val(value);
    $('#gallery-dropdown').remove();
  });

  //custom-girl-info
  $('body').on('keyup', '.custom-girl-info .form-element--type-text', function () {
    const value = $(this).val();

    var thisEle = $(this);

    //xxx stopped here
    $('body').addClass('hide-ui-suggestions');

    clearTimeout(timeout);
    timeout = setTimeout(function () {
      $('#gallery-dropdown').remove();
      const newDiv = $('<div></div>').attr('id', 'gallery-dropdown');
      $.ajax("/api/v1/public/girl/infos?name=" + value + "&id=" + value + "&year=" + value, { dataType: 'json', xhrFields: { withCredentials: true } })
        .done(function (data) {
          console.log(data);
          var counter = 0;
          data.forEach(function (item) {
            if (counter < 10) {
              //const subDiv = $('<a></a>').prop('href', 'javascript:void;').text(item.name.replace('&amp;', '&').replace('&#039;', '\'') + ' (' + (item.descriptor_month < 10 ? '0' : '') + item.descriptor_month + '/' + item.descriptor_year + ') [Info:' + item.girl_info_id + '] [Girl:' + item.girl_id + ']').addClass('gallery-girl');

              var thisText = item.name.replace('&amp;', '&').replace('&#039;', '\'') + ' (' + (item.descriptor_month < 10 ? '0' : '') + item.descriptor_month + '/' + item.descriptor_year + ') [Info:' + item.girl_info_id + ']';
              var subDiv = $('<a href="javascript:void;" class="gallery-girl-secondary">'+thisText+'</a>');
              subDiv.attr('data-text', item.name.replace('&amp;', '&').replace('&#039;', '\''));
              subDiv.attr('data-id', item.girl_info_id);

              newDiv.append(subDiv);
              counter++;
            }
          });
        })
        .catch(function (error) {
          const subDiv = $('<a></a>').prop('target', '_blank').prop('href', 'https://login.playboy.de/?return=https%3A%2F%2Fapi.premium.playboy.de%2Ffrontend').text('Please login to Premium Admin').addClass('gallery-girl-secondary no-add');
          newDiv.append(subDiv);
        });

      $(thisEle).after(newDiv);
    }, 500);
  });

  $('body').on('click', '.gallery-girl-secondary:not(.no-add)', function () {
    const value = $(this).attr('data-text') + ' (' + $(this).attr('data-id') + ')';

    var thisEle = $(this).closest('td').find('.form-element--type-text');

    thisEle.val(value);
    thisEle.trigger('change');
    $(document).click();
    $('#gallery-dropdown').remove();
  });

  $(document).on('click', function (event) {
    $('body').removeClass('hide-ui-suggestions');
  });
});

//ui-menu ui-widget ui-widget-content ui-autocomplete ui-front


/*
jQuery(document).ready(function ($) {
  let timeout = null;
  $('body').on('keyup', '.field--name-field-gallery-shooting .form-element--type-text', function () {
    const value = $(this).val();
    clearTimeout(timeout);
    timeout = setTimeout(function () {
      $('#gallery-dropdown').remove();
      const newDiv = $('<div></div>').attr('id', 'gallery-dropdown');
      $.ajax("https://api.premium.playboy.de/api/v1/public/girl/infos?name=" + value + "&id=" + value + "&year=" + value, { dataType: 'json', xhrFields: { withCredentials: true } })
        .done(function (data) {
          data.forEach(function (item) {
            const subDiv = $('<a></a>').prop('href', 'javascript:void;').text(item.name.replace('&amp;', '&').replace('&#039;', '\'') + ' (' + (item.descriptor_month < 10 ? '0' : '') + item.descriptor_month + '/' + item.descriptor_year + ') [Info:' + item.girl_info_id + '] [Girl:' + item.girl_id + ']').addClass('gallery-girl');
            newDiv.append(subDiv);
          });
        })
        .catch(function (error) {
          const subDiv = $('<a></a>').prop('target', '_blank').prop('href', 'https://login.playboy.de/?return=https%3A%2F%2Fapi.premium.playboy.de%2Ffrontend').text('Please login to Premium Admin').addClass('gallery-girl no-add');
          newDiv.append(subDiv);
        });
      $('.field--name-field-gallery-shooting .form-element--type-text').after(newDiv);
    }, 500);
  });

  $('body').on('click', '.gallery-girl:not(.no-add)', function () {
    const value = $(this).text();
    $('.field--name-field-gallery-shooting .form-element--type-text').val(value);
    $('#gallery-dropdown').remove();
  });
});*/
