pb_token.refresh:
  path: '/user/token/refresh'
  defaults:
    _controller: '\Drupal\pb_token\Controller\PbTokenController::refresh'
  options:
    no_cache: 'TRUE'
  methods: [GET]
  requirements:
    _access: 'TRUE'
pb_token.new:
  path: '/user/token/new'
  defaults:
    _controller: '\Drupal\pb_token\Controller\PbTokenController::new'
  options:
    no_cache: 'TRUE'
  methods: [GET]
  requirements:
    _access: 'TRUE'
pb_token.logout:
  path: '/user/token/logout'
  defaults:
    _controller: '\Drupal\pb_token\Controller\PbTokenController::logout'
  options:
    no_cache: 'TRUE'
  methods: [GET]
  requirements:
    _access: 'TRUE'
pb_token.image:
  path: '/image/token'
  defaults:
    _controller: '\Drupal\pb_token\Controller\PbTokenController::image'
  options:
    no_cache: 'TRUE'
  methods: [GET]
  requirements:
    _access: 'TRUE'
pb_token.image_json:
  path: '/image/token/json'
  defaults:
    _controller: '\Drupal\pb_token\Controller\PbTokenController::sign'
  options:
    no_cache: 'TRUE'
  methods: [GET]
  requirements:
    _access: 'TRUE'
pb_token.user_cancel:
  path: '/user/token/cancel'
  defaults:
    _controller: '\Drupal\pb_token\Controller\PbTokenController::cancel'
  options:
    no_cache: 'TRUE'
  methods: [GET]
  requirements:
    _role: subscriber
