<?php

/**
 * @file
 * Install, update and uninstall functions for the gallery_image_checker module.
 */

/**
 * Implements hook_schema().
 */
function gallery_image_checker_schema() {
  $schema['gallery_checked'] = [
    'description' => 'Tracks which galleries have been checked',
    'fields' => [
      'gallery_id' => [
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'Gallery media entity ID',
      ],
      'checked_timestamp' => [
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'Unix timestamp when gallery was checked',
      ],
    ],
    'primary key' => ['gallery_id'],
    'indexes' => [
      'checked_timestamp' => ['checked_timestamp'],
    ],
  ];

  return $schema;
}

/**
 * Implements hook_install().
 */
function gallery_image_checker_install() {
  \Drupal::messenger()->addMessage(t('Gallery Image Checker module has been installed. The gallery_checked table has been created.'));
}

/**
 * Implements hook_uninstall().
 */
function gallery_image_checker_uninstall() {
  // The schema will be automatically removed by Drupal
  \Drupal::messenger()->addMessage(t('Gallery Image Checker module has been uninstalled. The gallery_checked table has been removed.'));
}
