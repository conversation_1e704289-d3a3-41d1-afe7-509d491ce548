<?php

namespace Drupal\gallery_image_checker\Commands;

use Drush\Commands\DrushCommands;
use <PERSON><PERSON><PERSON>\gallery_image_checker\Service\GalleryImageCheckerService;

/**
 * Drush commands for Gallery Image Checker.
 */
class GalleryImageCheckerCommands extends DrushCommands {

  /**
   * @var \Drupal\gallery_image_checker\Service\GalleryImageCheckerService
   */
  protected $checkerService;

  /**
   * Constructor.
   *
   * @param \Drupal\gallery_image_checker\Service\GalleryImageCheckerService $checkerService
   *   The gallery image checker service.
   */
  public function __construct(GalleryImageCheckerService $checkerService) {
    parent::__construct();
    $this->checkerService = $checkerService;
  }

  /**
   * Run the gallery image checker process.
   *
   * @param array $options
   *   An associative array of options whose values come from cli, aliases, config, etc.
   * @option limit
   *   Number of galleries to process (default: 10).
   *
   * @command gallery:check
   * @aliases gic
   */
  public function check($options = ['limit' => 10]) {
    $limit = (int) $options['limit'];

    $this->output()->writeln("Starting gallery image checker (limit: {$limit})...");

    $result = $this->checkerService->checkGalleries($limit);

    // Print result to Drush output.
    $this->output()->writeln("Gallery image checker completed!");
    $this->output()->writeln("Processed galleries: " . $result['processed_galleries']);
    $this->output()->writeln("Total images logged: " . $result['total_images']);

    if (!empty($result['errors'])) {
      $this->output()->writeln("Errors encountered:");
      foreach ($result['errors'] as $error) {
        $this->output()->writeln("  - " . $error);
      }
    }

    $this->io()->success("Gallery image checker completed successfully.");
  }


  /**
   * Reset checked status for all galleries (for testing).
   *
   * @command gallery:reset
   * @aliases gir
   */
  public function reset() {
    $result = $this->checkerService->resetCheckedStatus();
    $this->output()->writeln("Reset checked status for {$result} galleries.");
    $this->io()->success("Gallery checked status reset completed.");
  }

  /**
   * Simple test command to check Drush discovery.
   *
   * @command gallery:test
   */
  public function test() {
    $this->output()->writeln('✅ Gallery Image Checker Drush command works!');
  }

}
