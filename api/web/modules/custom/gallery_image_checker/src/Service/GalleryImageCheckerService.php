<?php

namespace Drupal\gallery_image_checker\Service;

use <PERSON><PERSON>al\Core\Database\Connection;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use <PERSON>upal\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\file\Entity\File;

class GalleryImageCheckerService {

  protected $database;
  protected $fileSystem;
  protected $logger;
  protected $entityTypeManager;

  public function __construct(Connection $database, FileSystemInterface $fileSystem, LoggerChannelFactoryInterface $logger_factory, EntityTypeManagerInterface $entity_type_manager) {
    $this->database = $database;
    $this->fileSystem = $fileSystem;
    $this->logger = $logger_factory->get('gallery_image_checker');
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * Run the checker: mark galleries and log missing images.
   *
   * @param int $limit
   *   Maximum number of galleries to process (default: 10).
   *
   * @return array
   *   Summary of processing results.
   */
  public function checkGalleries($limit = 10) {
    // Ensure the gallery_checked table exists
    $this->ensureGalleryCheckedTable();

    // Get unchecked galleries ordered by changed date (most recent first)
    $query = $this->database->select('media_field_data', 'm')
      ->fields('m', ['mid', 'name', 'changed'])
      ->condition('m.bundle', 'gallery')
      ->condition('m.status', 1); // Only published galleries

    // Left join with our tracking table to find unchecked galleries
    $query->leftJoin('gallery_checked', 'gc', 'm.mid = gc.gallery_id');
    $query->isNull('gc.gallery_id');

    $query->orderBy('m.changed', 'DESC')
      ->range(0, $limit);

    $result = $query->execute()->fetchAll();

    $processed_count = 0;
    $total_images = 0;
    $errors = [];

    $this->logger->info('Found @count unchecked galleries to process', ['@count' => count($result)]);

    foreach ($result as $gallery_data) {
      $gallery_id = $gallery_data->mid;
      $gallery_name = $gallery_data->name;

      $this->logger->info('Processing gallery: @name (ID: @id)', [
        '@name' => $gallery_name,
        '@id' => $gallery_id
      ]);

      try {
        // Load the gallery entity
        $gallery = $this->entityTypeManager->getStorage('media')->load($gallery_id);
        if (!$gallery) {
          $error_msg = "Could not load gallery with ID: {$gallery_id}";
          $this->logger->error($error_msg);
          $errors[] = $error_msg;
          continue;
        }

        // Get images from the gallery's field_media_slideshow
        $slideshow_items = $gallery->get('field_media_slideshow')->referencedEntities();
        $image_count = count($slideshow_items);

        $this->logger->info('Gallery @name has @count images', [
          '@name' => $gallery_name,
          '@count' => $image_count
        ]);

        // Log each image URL
        foreach ($slideshow_items as $media_item) {
          $this->logImageUrl($media_item, $gallery_id, $gallery_name);
        }

        // Mark gallery as checked
        $this->markGalleryAsChecked($gallery_id);

        $processed_count++;
        $total_images += $image_count;

        $this->logger->info('Completed processing gallery: @name (ID: @id)', [
          '@name' => $gallery_name,
          '@id' => $gallery_id
        ]);

      } catch (\Exception $e) {
        $error_msg = "Error processing gallery {$gallery_id}: " . $e->getMessage();
        $this->logger->error($error_msg);
        $errors[] = $error_msg;
      }
    }

    $summary = [
      'processed_galleries' => $processed_count,
      'total_images' => $total_images,
      'errors' => $errors,
    ];

    $this->logger->info('Finished processing @processed galleries with @images total images. @errors errors occurred.', [
      '@processed' => $processed_count,
      '@images' => $total_images,
      '@errors' => count($errors)
    ]);

    return $summary;
  }

  /**
   * Ensure the gallery_checked table exists.
   */
  protected function ensureGalleryCheckedTable() {
    $schema = $this->database->schema();

    if (!$schema->tableExists('gallery_checked')) {
      $table_definition = [
        'description' => 'Tracks which galleries have been checked',
        'fields' => [
          'gallery_id' => [
            'type' => 'int',
            'unsigned' => TRUE,
            'not null' => TRUE,
            'description' => 'Gallery media entity ID',
          ],
          'checked_timestamp' => [
            'type' => 'int',
            'unsigned' => TRUE,
            'not null' => TRUE,
            'description' => 'Unix timestamp when gallery was checked',
          ],
        ],
        'primary key' => ['gallery_id'],
        'indexes' => [
          'checked_timestamp' => ['checked_timestamp'],
        ],
      ];

      $schema->createTable('gallery_checked', $table_definition);
      $this->logger->info('Created gallery_checked table');
    }
  }

  /**
   * Log image URL for a media item.
   */
  protected function logImageUrl($media_item, $gallery_id, $gallery_name) {
    if (!$media_item) {
      return;
    }

    // Get the file from the media item
    $file_field = $media_item->get('field_media_image');
    if ($file_field->isEmpty()) {
      $this->logger->warning('Media item @mid has no file attached', ['@mid' => $media_item->id()]);
      return;
    }

    $file = $file_field->entity;
    if (!$file instanceof File) {
      $this->logger->warning('Could not load file for media item @mid', ['@mid' => $media_item->id()]);
      return;
    }

    $file_uri = $file->getFileUri();
    $file_url = $file->createFileUrl();

    $message = sprintf(
      'Gallery: %s (ID: %d) | Media ID: %d | File URI: %s | File URL: %s',
      $gallery_name,
      $gallery_id,
      $media_item->id(),
      $file_uri,
      $file_url
    );

    $this->logIssue($message);
  }

  /**
   * Mark a gallery as checked.
   */
  protected function markGalleryAsChecked($gallery_id) {
    $this->database->merge('gallery_checked')
      ->key(['gallery_id' => $gallery_id])
      ->fields([
        'gallery_id' => $gallery_id,
        'checked_timestamp' => time(),
      ])
      ->execute();

    $this->logger->info('Marked gallery @id as checked', ['@id' => $gallery_id]);
  }

  /**
   * Reset checked status for all galleries (for testing purposes).
   *
   * @return int
   *   Number of galleries that had their checked status reset.
   */
  public function resetCheckedStatus() {
    $this->ensureGalleryCheckedTable();

    $count = $this->database->select('gallery_checked', 'gc')
      ->countQuery()
      ->execute()
      ->fetchField();

    $this->database->truncate('gallery_checked')->execute();

    $this->logger->info('Reset checked status for @count galleries', ['@count' => $count]);

    return $count;
  }

  /**
   * Log both to Drupal logger and a custom file.
   */
  protected function logIssue($message) {
    $this->logger->info($message);

    $directory = 'public://';
    $this->fileSystem->prepareDirectory($directory, FileSystemInterface::CREATE_DIRECTORY);

    $file_path = $directory . 'gallery_image_checker_logs.log';
    file_put_contents($file_path, $message . "\n", FILE_APPEND | LOCK_EX);
  }

}
