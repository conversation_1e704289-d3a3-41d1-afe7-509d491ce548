# Gallery Image Checker

This module provides functionality to check galleries and log image URLs for verification purposes.

## Features

- Processes galleries ordered by modified date (most recent first)
- Tracks which galleries have been checked to avoid duplicate processing
- Logs file URLs for each image in the gallery
- Provides Drush commands for easy execution

## Database Schema

The module creates a `gallery_checked` table with the following structure:
- `gallery_id` (int): Gallery media entity ID (primary key)
- `checked_timestamp` (int): Unix timestamp when gallery was checked

## Drush Commands

### Check Galleries
```bash
drush gallery:check
# or
drush gic
```

Options:
- `--limit=N`: Number of galleries to process (default: 10)

Example:
```bash
drush gallery:check --limit=5
```

### Reset Checked Status
```bash
drush gallery:reset
# or
drush gir
```

This command clears all checked status records, allowing galleries to be processed again.

### Test Command
```bash
drush gallery:test
```

Simple test command to verify Drush integration is working.

## How It Works

1. The service queries for media entities with bundle 'gallery' that haven't been checked yet
2. Orders results by the `changed` timestamp (most recent first)
3. For each gallery:
   - Loads the gallery entity
   - Gets all images from the `field_media_slideshow` field
   - Logs the file URI and URL for each image
   - Marks the gallery as checked in the tracking table

## Logging

The module logs to both:
- Drupal's logger system (channel: 'gallery_image_checker')
- A custom log file: `public://gallery_image_checker_logs.log`

## Installation

1. Enable the module: `drush en gallery_image_checker`
2. The database table will be created automatically
3. Run the checker: `drush gallery:check`

## Uninstallation

When uninstalling the module, the `gallery_checked` table will be automatically removed.
