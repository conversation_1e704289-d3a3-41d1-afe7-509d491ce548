{"name": "drupal/pb_girl", "type": "drupal-module", "description": "Playboy Girl.", "keywords": ["drupal", "web", "ui"], "license": "GPL-2.0+", "minimum-stability": "dev", "prefer-stable": true, "authors": [{"name": "<PERSON>", "email": "christian.w<PERSON><PERSON>@key-tec.de"}], "require-dev": {"composer/installers": "^1.2", "cweagans/composer-patches": "~1.4", "drupal/console": "~1.0", "drupal/core-composer-scaffold": "^8.8 || ^9", "drupal/core-dev": "^8.8 || ^9", "drupal/core-recommended": "~8.9", "drupal/devel_entity_updates": "^3.0", "drupal/token": "~1", "drupal/twig_tweak": "^2.6", "drush/drush": "~10", "openeuropa/task-runner": "~1.0-beta3", "phpro/grumphp": "~0.14"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}], "autoload": {"psr-4": {"Drupal\\pb_girl\\": "./src"}}, "autoload-dev": {"psr-4": {"Drupal\\Tests\\pb_girl\\": "./tests/src"}}, "scripts": {"post-install-cmd": "./vendor/bin/run drupal:site-setup", "post-update-cmd": "./vendor/bin/run drupal:site-setup"}, "extra": {"enable-patching": true, "composer-exit-on-patch-failure": true, "drupal-scaffold": {"locations": {"web-root": "build/"}}, "installer-paths": {"build/core": ["type:drupal-core"], "build/modules/contrib/{$name}": ["type:drupal-module"], "build/profiles/contrib/{$name}": ["type:drupal-profile"], "build/themes/contrib/{$name}": ["type:drupal-theme"]}}, "config": {"sort-packages": true}, "require": {"drupal/devel_entity_updates": "^3.0", "drupal/migrate_file": "^1.1", "drupal/migrate_plus": "^5.1", "drupal/migrate_tools": "^5.0", "drupal/votingapi_reaction": "^1.1"}}