{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "dc7807285e29d94d3653b92913157b56", "packages": [{"name": "asm89/stack-cors", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "b9c31def6a83f84b4d4a40d35996d375755f0e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/b9c31def6a83f84b4d4a40d35996d375755f0e08", "reference": "b9c31def6a83f84b4d4a40d35996d375755f0e08", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/http-foundation": "~2.7|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^4.8.10", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/Asm89/Stack/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "time": "2019-12-24T22:41:47+00:00"}, {"name": "composer/semver", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "c6bea70230ef4dd483e6bbcab6005f682ed3a8de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/c6bea70230ef4dd483e6bbcab6005f682ed3a8de", "reference": "c6bea70230ef4dd483e6bbcab6005f682ed3a8de", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.5 || ^5.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "time": "2020-01-13T12:06:48+00:00"}, {"name": "doctrine/annotations", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "54cacc9b81758b14e3ce750f205a393d52339e97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/54cacc9b81758b14e3ce750f205a393d52339e97", "reference": "54cacc9b81758b14e3ce750f205a393d52339e97", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "php": "^5.6 || ^7.0"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^5.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2017-02-24T16:22:25+00:00"}, {"name": "doctrine/cache", "version": "v1.6.2", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "eb152c5100571c7a45470ff2a35095ab3f3b900b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/eb152c5100571c7a45470ff2a35095ab3f3b900b", "reference": "eb152c5100571c7a45470ff2a35095ab3f3b900b", "shasum": ""}, "require": {"php": "~5.5|~7.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"phpunit/phpunit": "~4.8|~5.0", "predis/predis": "~1.0", "satooshi/php-coveralls": "~0.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Caching library offering an object-oriented API for many cache backends", "homepage": "http://www.doctrine-project.org", "keywords": ["cache", "caching"], "time": "2017-07-22T12:49:21+00:00"}, {"name": "doctrine/collections", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "1a4fb7e902202c33cce8c55989b945612943c2ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/1a4fb7e902202c33cce8c55989b945612943c2ba", "reference": "1a4fb7e902202c33cce8c55989b945612943c2ba", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"doctrine/coding-standard": "~0.1@dev", "phpunit/phpunit": "^5.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Collections\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Collections Abstraction library", "homepage": "http://www.doctrine-project.org", "keywords": ["array", "collections", "iterator"], "time": "2017-01-03T10:49:41+00:00"}, {"name": "doctrine/common", "version": "v2.7.3", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "4acb8f89626baafede6ee5475bc5844096eba8a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/4acb8f89626baafede6ee5475bc5844096eba8a9", "reference": "4acb8f89626baafede6ee5475bc5844096eba8a9", "shasum": ""}, "require": {"doctrine/annotations": "1.*", "doctrine/cache": "1.*", "doctrine/collections": "1.*", "doctrine/inflector": "1.*", "doctrine/lexer": "1.*", "php": "~5.6|~7.0"}, "require-dev": {"phpunit/phpunit": "^5.4.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common Library for Doctrine projects", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "collections", "eventmanager", "persistence", "spl"], "time": "2017-07-22T08:35:12+00:00"}, {"name": "doctrine/inflector", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "e11d84c6e018beedd929cff5220969a3c6d1d462"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/e11d84c6e018beedd929cff5220969a3c6d1d462", "reference": "e11d84c6e018beedd929cff5220969a3c6d1d462", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Common String Manipulations with regard to casing and singular/plural rules.", "homepage": "http://www.doctrine-project.org", "keywords": ["inflection", "pluralize", "singularize", "string"], "time": "2017-07-22T12:18:28+00:00"}, {"name": "doctrine/lexer", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "1febd6c3ef84253d7c815bed85fc622ad207a9f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/1febd6c3ef84253d7c815bed85fc622ad207a9f8", "reference": "1febd6c3ef84253d7c815bed85fc622ad207a9f8", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "^4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "time": "2019-06-08T11:03:04+00:00"}, {"name": "drupal/core", "version": "8.9.1", "source": {"type": "git", "url": "https://github.com/drupal/core.git", "reference": "e8ee964c562870381876e85d3f5eaaf8c8ecc9ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core/zipball/e8ee964c562870381876e85d3f5eaaf8c8ecc9ee", "reference": "e8ee964c562870381876e85d3f5eaaf8c8ecc9ee", "shasum": ""}, "require": {"asm89/stack-cors": "^1.1", "composer/semver": "^1.0", "doctrine/annotations": "^1.4", "doctrine/common": "^2.7", "easyrdf/easyrdf": "^0.9", "egulias/email-validator": "^2.0", "ext-date": "*", "ext-dom": "*", "ext-filter": "*", "ext-gd": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-simplexml": "*", "ext-spl": "*", "ext-tokenizer": "*", "ext-xml": "*", "guzzlehttp/guzzle": "^6.3", "laminas/laminas-diactoros": "^1.8", "laminas/laminas-feed": "^2.12", "masterminds/html5": "^2.1", "pear/archive_tar": "^1.4.9", "php": ">=7.0.8", "psr/log": "^1.0", "stack/builder": "^1.0", "symfony-cmf/routing": "^1.4", "symfony/class-loader": "~3.4.0", "symfony/console": "~3.4.0", "symfony/dependency-injection": "~3.4.26", "symfony/event-dispatcher": "~3.4.0", "symfony/http-foundation": "~3.4.35", "symfony/http-kernel": "~3.4.14", "symfony/polyfill-iconv": "^1.0", "symfony/process": "~3.4.0", "symfony/psr-http-message-bridge": "^1.1.2", "symfony/routing": "~3.4.0", "symfony/serializer": "~3.4.0", "symfony/translation": "~3.4.0", "symfony/validator": "~3.4.0", "symfony/yaml": "~3.4.5", "twig/twig": "^1.38.2", "typo3/phar-stream-wrapper": "^3.1.3"}, "conflict": {"drupal/pathauto": "<1.6", "drush/drush": "<8.1.10"}, "replace": {"drupal/action": "self.version", "drupal/aggregator": "self.version", "drupal/automated_cron": "self.version", "drupal/ban": "self.version", "drupal/bartik": "self.version", "drupal/basic_auth": "self.version", "drupal/big_pipe": "self.version", "drupal/block": "self.version", "drupal/block_content": "self.version", "drupal/block_place": "self.version", "drupal/book": "self.version", "drupal/breakpoint": "self.version", "drupal/ckeditor": "self.version", "drupal/claro": "self.version", "drupal/classy": "self.version", "drupal/color": "self.version", "drupal/comment": "self.version", "drupal/config": "self.version", "drupal/config_translation": "self.version", "drupal/contact": "self.version", "drupal/content_moderation": "self.version", "drupal/content_translation": "self.version", "drupal/contextual": "self.version", "drupal/core-annotation": "self.version", "drupal/core-assertion": "self.version", "drupal/core-bridge": "self.version", "drupal/core-class-finder": "self.version", "drupal/core-datetime": "self.version", "drupal/core-dependency-injection": "self.version", "drupal/core-diff": "self.version", "drupal/core-discovery": "self.version", "drupal/core-event-dispatcher": "self.version", "drupal/core-file-cache": "self.version", "drupal/core-file-security": "self.version", "drupal/core-filesystem": "self.version", "drupal/core-gettext": "self.version", "drupal/core-graph": "self.version", "drupal/core-http-foundation": "self.version", "drupal/core-php-storage": "self.version", "drupal/core-plugin": "self.version", "drupal/core-proxy-builder": "self.version", "drupal/core-render": "self.version", "drupal/core-serialization": "self.version", "drupal/core-transliteration": "self.version", "drupal/core-utility": "self.version", "drupal/core-uuid": "self.version", "drupal/core-version": "self.version", "drupal/datetime": "self.version", "drupal/datetime_range": "self.version", "drupal/dblog": "self.version", "drupal/dynamic_page_cache": "self.version", "drupal/editor": "self.version", "drupal/entity_reference": "self.version", "drupal/field": "self.version", "drupal/field_layout": "self.version", "drupal/field_ui": "self.version", "drupal/file": "self.version", "drupal/filter": "self.version", "drupal/forum": "self.version", "drupal/hal": "self.version", "drupal/help": "self.version", "drupal/help_topics": "self.version", "drupal/history": "self.version", "drupal/image": "self.version", "drupal/inline_form_errors": "self.version", "drupal/jsonapi": "self.version", "drupal/language": "self.version", "drupal/layout_builder": "self.version", "drupal/layout_discovery": "self.version", "drupal/link": "self.version", "drupal/locale": "self.version", "drupal/media": "self.version", "drupal/media_library": "self.version", "drupal/menu_link_content": "self.version", "drupal/menu_ui": "self.version", "drupal/migrate": "self.version", "drupal/migrate_drupal": "self.version", "drupal/migrate_drupal_multilingual": "self.version", "drupal/migrate_drupal_ui": "self.version", "drupal/minimal": "self.version", "drupal/node": "self.version", "drupal/options": "self.version", "drupal/page_cache": "self.version", "drupal/path": "self.version", "drupal/path_alias": "self.version", "drupal/quickedit": "self.version", "drupal/rdf": "self.version", "drupal/responsive_image": "self.version", "drupal/rest": "self.version", "drupal/search": "self.version", "drupal/serialization": "self.version", "drupal/settings_tray": "self.version", "drupal/seven": "self.version", "drupal/shortcut": "self.version", "drupal/simpletest": "self.version", "drupal/standard": "self.version", "drupal/stark": "self.version", "drupal/statistics": "self.version", "drupal/syslog": "self.version", "drupal/system": "self.version", "drupal/taxonomy": "self.version", "drupal/telephone": "self.version", "drupal/text": "self.version", "drupal/toolbar": "self.version", "drupal/tour": "self.version", "drupal/tracker": "self.version", "drupal/update": "self.version", "drupal/user": "self.version", "drupal/views": "self.version", "drupal/views_ui": "self.version", "drupal/workflows": "self.version", "drupal/workspaces": "self.version"}, "type": "drupal-core", "extra": {"drupal-scaffold": {"file-mapping": {"[project-root]/.editorconfig": "assets/scaffold/files/editorconfig", "[project-root]/.gitattributes": "assets/scaffold/files/gitattributes", "[web-root]/.csslintrc": "assets/scaffold/files/csslintrc", "[web-root]/.eslintignore": "assets/scaffold/files/eslintignore", "[web-root]/.eslintrc.json": "assets/scaffold/files/eslintrc.json", "[web-root]/.ht.router.php": "assets/scaffold/files/ht.router.php", "[web-root]/.htaccess": "assets/scaffold/files/htaccess", "[web-root]/example.gitignore": "assets/scaffold/files/example.gitignore", "[web-root]/index.php": "assets/scaffold/files/index.php", "[web-root]/INSTALL.txt": "assets/scaffold/files/drupal.INSTALL.txt", "[web-root]/README.txt": "assets/scaffold/files/drupal.README.txt", "[web-root]/robots.txt": "assets/scaffold/files/robots.txt", "[web-root]/update.php": "assets/scaffold/files/update.php", "[web-root]/web.config": "assets/scaffold/files/web.config", "[web-root]/sites/README.txt": "assets/scaffold/files/sites.README.txt", "[web-root]/sites/development.services.yml": "assets/scaffold/files/development.services.yml", "[web-root]/sites/example.settings.local.php": "assets/scaffold/files/example.settings.local.php", "[web-root]/sites/example.sites.php": "assets/scaffold/files/example.sites.php", "[web-root]/sites/default/default.services.yml": "assets/scaffold/files/default.services.yml", "[web-root]/sites/default/default.settings.php": "assets/scaffold/files/default.settings.php", "[web-root]/modules/README.txt": "assets/scaffold/files/modules.README.txt", "[web-root]/profiles/README.txt": "assets/scaffold/files/profiles.README.txt", "[web-root]/themes/README.txt": "assets/scaffold/files/themes.README.txt"}}}, "autoload": {"psr-4": {"Drupal\\Core\\": "lib/Drupal/Core", "Drupal\\Component\\": "lib/Drupal/Component", "Drupal\\Driver\\": "../drivers/lib/Drupal/Driver"}, "classmap": ["lib/Drupal.php", "lib/Drupal/Component/Utility/Timer.php", "lib/Drupal/Component/Utility/Unicode.php", "lib/Drupal/Core/Database/Database.php", "lib/Drupal/Core/DrupalKernel.php", "lib/Drupal/Core/DrupalKernelInterface.php", "lib/Drupal/Core/Site/Settings.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal is an open source content management platform powering millions of websites and applications.", "time": "2020-06-17T17:57:48+00:00"}, {"name": "drupal/devel", "version": "4.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/devel.git", "reference": "4.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/devel-4.0.0.zip", "reference": "4.0.0", "shasum": "6c01ef8dd4e926e63b08fe28dea5357c7caa49c9"}, "require": {"doctrine/common": "^2.7", "drupal/core": "^8.8 || ^9", "symfony/var-dumper": "^4 || ^5"}, "conflict": {"kint-php/kint": "<3"}, "require-dev": {"composer/installers": "^1", "cweagans/composer-patches": "~1.0", "drupal/core-composer-scaffold": "^8.0", "drupal/core-dev": "^8.0", "drupal/core-recommended": "^8.0", "drush/drush": "^10", "mglaman/phpstan-drupal": "^0.12", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-deprecation-rules": "^0.12", "zaporylie/composer-drupal-optimizations": "^1.0"}, "suggest": {"kint-php/kint": "Kint provides an informative display of arrays/objects. Useful for debugging and developing."}, "type": "drupal-module", "extra": {"drupal": {"version": "4.0.0", "datestamp": "1592918748", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/{$name}": ["type:drupal-profile"], "web/themes/{$name}": ["type:drupal-theme"], "drush/{$name}": ["type:drupal-drush"]}, "drupal-scaffold": {"locations": {"web-root": "web/"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "autoload": {"classmap": [".spoons/ScriptHandler.php"]}, "notification-url": "https://packages.drupal.org/8/downloads", "scripts": {"si": ["drush si -v --db-url=${SIMPLETEST_DB:-mysql://root:password@mariadb/db}"], "phpcs": ["phpcs --runtime-set ignore_warnings_on_exit 1 --runtime-set ignore_errors_on_exit 1 web/modules/custom"], "lint": ["parallel-lint --exclude web --exclude vendor ."], "webserver": ["cd web && php -S 0.0.0.0:8888 .ht.router.php"], "chromedriver": ["chromedriver --port=9515 --verbose --whitelisted-ips --log-path=/tmp/chromedriver.log --no-sandbox"], "unit": ["phpunit --verbose web/modules/custom"], "phpstan": ["phpstan analyse web/modules/custom"], "stylelint": ["yarn --silent --cwd web/core stylelint --formatter verbose --config ./.stylelintrc.json ../modules/custom/**/*.css"], "eslint": ["yarn --silent --cwd web/core eslint -c ./.eslintrc.json ../modules/custom"], "post-update-cmd": ["Spoons\\ScriptHandler::createSymlinks"]}, "license": ["GPL-2.0-or-later"], "authors": [{"name": "drupalspoons", "homepage": "https://www.drupal.org/user/3647684"}, {"name": "moshe weitzman", "homepage": "https://www.drupal.org/user/23"}], "description": "Various blocks, pages, and functions for developers.", "homepage": "https://www.drupal.org/project/devel", "support": {"source": "https://gitlab.com/drupalspoons/devel", "issues": "https://gitlab.com/drupalspoons/devel/-/issues", "slack": "https://drupal.slack.com/archives/C012WAW1MH6"}}, {"name": "drupal/devel_entity_updates", "version": "3.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/devel_entity_updates.git", "reference": "3.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/devel_entity_updates-3.0.0.zip", "reference": "3.0.0", "shasum": "2fcbde815745117b11170598de0211b31dd2321e"}, "require": {"drupal/core": "^8.7.7 || ^9", "drupal/devel": "*"}, "conflict": {"drupal/core": "<8.7", "drush/drush": ">=9.0 <9.6"}, "require-dev": {"drupal/devel": "^2 || ^3"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON> (plach)", "homepage": "https://www.drupal.org/u/plach", "role": "Maintainer"}, {"name": "plach", "homepage": "https://www.drupal.org/user/183211"}], "description": "Provides developers an API and drush command to perform automatic entity updates.", "homepage": "http://drupal.org/project/devel_entity_updates", "support": {"source": "https://cgit.drupalcode.org/devel_entity_updates", "issues": "https://drupal.org/project/issues/devel_entity_updates", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/migrate_file", "version": "1.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_file.git", "reference": "8.x-1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_file-8.x-1.1.zip", "reference": "8.x-1.1", "shasum": "4a24edc577541b5aa67682a4f89c8dfd7c0788d5"}, "require": {"drupal/core": "^8"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "d<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/823702"}], "description": "Additional support for migrating files including downloading remote files and using remote uris (without download)", "homepage": "https://www.drupal.org/project/migrate_file", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/migrate_file", "issues": "https://www.drupal.org/project/issues/migrate_file"}}, {"name": "drupal/migrate_plus", "version": "5.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_plus.git", "reference": "8.x-5.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_plus-8.x-5.1.zip", "reference": "8.x-5.1", "shasum": "1257427ab0c64459c3c1e42bb2a98d3114b77163"}, "require": {"drupal/core": "^8.8 || ^9", "php": ">=7.1"}, "require-dev": {"drupal/migrate_example_advanced_setup": "*", "drupal/migrate_example_setup": "*"}, "suggest": {"ext-soap": "*", "sainsburys/guzzle-oauth2-plugin": "3.0 required for the OAuth2 authentication plugin"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-5.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mikeryan", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/heddn", "role": "Maintainer"}], "description": "Enhancements to core migration support.", "homepage": "https://www.drupal.org/project/migrate_plus", "support": {"source": "https://git.drupalcode.org/project/migrate_plus", "issues": "https://www.drupal.org/project/issues/migrate_plus", "slack": "#migrate"}}, {"name": "drupal/migrate_tools", "version": "5.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_tools.git", "reference": "8.x-5.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_tools-8.x-5.0.zip", "reference": "8.x-5.0", "shasum": "b7c91aa6f7de9d6d548f65f83c8736e47e5926a1"}, "require": {"drupal/core": "^8.8 | ^9", "drupal/migrate_plus": "^5", "php": ">=7.1"}, "require-dev": {"drupal/migrate_plus": "^5", "drupal/migrate_source_csv": "^3", "drush/drush": "^10"}, "suggest": {"drush/drush": "^9 || ^10"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-5.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mikeryan", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/heddn", "role": "Maintainer"}], "description": "Tools to assist in developing and running migrations.", "homepage": "http://drupal.org/project/migrate_tools", "support": {"source": "https://git.drupalcode.org/project/migrate_tools", "issues": "https://www.drupal.org/project/issues/migrate_tools", "slack": "#migrate"}}, {"name": "drupal/<PERSON>api", "version": "3.0.0-beta2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/votingapi.git", "reference": "8.x-3.0-beta2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/votingapi-8.x-3.0-beta2.zip", "reference": "8.x-3.0-beta2", "shasum": "1fe15da49cc1f2126f29ae39349a199eb1360a32"}, "require": {"drupal/core": "^8.7.7 || ^9"}, "require-dev": {"drupal/token": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.0-beta2", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "eaton", "homepage": "https://www.drupal.org/user/16496"}, {"name": "ped<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/218937"}, {"name": "pifagor", "homepage": "https://www.drupal.org/user/2375692"}, {"name": "torotil", "homepage": "https://www.drupal.org/user/865256"}], "description": "Voting API", "homepage": "http://drupal.org/project/votingapi", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/votingapi", "issues": "http://drupal.org/project/issues/votingapi"}}, {"name": "drupal/votingapi_reaction", "version": "1.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/votingapi_reaction.git", "reference": "8.x-1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/votingapi_reaction-8.x-1.1.zip", "reference": "8.x-1.1", "shasum": "84688a81b487317d1a002e41939d9c37c7921318"}, "require": {"drupal/core": "^8.7.7 || ^9", "drupal/votingapi": "^3.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON><PERSON> (elaman)", "homepage": "https://www.drupal.org/u/elaman", "role": "Maintainer"}], "description": "A flexible field based on Voting API module.", "homepage": "https://www.drupal.org/project/votingapi_reaction", "support": {"source": "https://cgit.drupalcode.org/votingapi_reaction", "issues": "https://www.drupal.org/project/issues/votingapi_reaction"}}, {"name": "easyrdf/easyrdf", "version": "0.9.1", "source": {"type": "git", "url": "https://github.com/easyrdf/easyrdf.git", "reference": "acd09dfe0555fbcfa254291e433c45fdd4652566"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/easyrdf/easyrdf/zipball/acd09dfe0555fbcfa254291e433c45fdd4652566", "reference": "acd09dfe0555fbcfa254291e433c45fdd4652566", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-pcre": "*", "php": ">=5.2.8"}, "require-dev": {"phpunit/phpunit": "~3.5", "sami/sami": "~1.4", "squizlabs/php_codesniffer": "~1.4.3"}, "suggest": {"ml/json-ld": "~1.0"}, "type": "library", "autoload": {"psr-0": {"EasyRdf_": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.aelius.com/njh/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "EasyRdf is a PHP library designed to make it easy to consume and produce RDF.", "homepage": "http://www.easyrdf.org/", "keywords": ["Linked Data", "RDF", "Semantic Web", "Turtle", "rdfa", "sparql"], "time": "2015-02-27T09:45:49+00:00"}, {"name": "egulias/email-validator", "version": "2.1.17", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ade6887fd9bd74177769645ab5c474824f8a418a", "reference": "ade6887fd9bd74177769645ab5c474824f8a418a", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "EmailValidator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "time": "2020-02-13T22:36:52+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.4", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a4a1b6930528a8f7ee03518e6442ec7a44155d9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a4a1b6930528a8f7ee03518e6442ec7a44155d9d", "reference": "a4a1b6930528a8f7ee03518e6442ec7a44155d9d", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2020-05-25T19:35:05+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2019-07-01T23:21:34+00:00"}, {"name": "laminas/laminas-diactoros", "version": "1.8.7p2", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diactoros.git", "reference": "6991c1af7c8d2c8efee81b22ba97024781824aaa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diactoros/zipball/6991c1af7c8d2c8efee81b22ba97024781824aaa", "reference": "6991c1af7c8d2c8efee81b22ba97024781824aaa", "shasum": ""}, "require": {"laminas/laminas-zendframework-bridge": "^1.0", "php": "^5.6 || ^7.0", "psr/http-message": "^1.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "replace": {"zendframework/zend-diactoros": "~*******"}, "require-dev": {"ext-dom": "*", "ext-libxml": "*", "laminas/laminas-coding-standard": "~1.0", "php-http/psr7-integration-tests": "dev-master", "phpunit/phpunit": "^5.7.16 || ^6.0.8 || ^7.2.7"}, "type": "library", "extra": {"branch-alias": {"dev-release-1.8": "1.8.x-dev"}}, "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/marshal_uri_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php", "src/functions/create_uploaded_file.legacy.php", "src/functions/marshal_headers_from_sapi.legacy.php", "src/functions/marshal_method_from_sapi.legacy.php", "src/functions/marshal_protocol_version_from_sapi.legacy.php", "src/functions/marshal_uri_from_sapi.legacy.php", "src/functions/normalize_server.legacy.php", "src/functions/normalize_uploaded_files.legacy.php", "src/functions/parse_cookie_header.legacy.php"], "psr-4": {"Laminas\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-7"], "time": "2020-03-23T15:28:28+00:00"}, {"name": "laminas/laminas-escaper", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-escaper.git", "reference": "25f2a053eadfa92ddacb609dcbbc39362610da70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-escaper/zipball/25f2a053eadfa92ddacb609dcbbc39362610da70", "reference": "25f2a053eadfa92ddacb609dcbbc39362610da70", "shasum": ""}, "require": {"laminas/laminas-zendframework-bridge": "^1.0", "php": "^5.6 || ^7.0"}, "replace": {"zendframework/zend-escaper": "self.version"}, "require-dev": {"laminas/laminas-coding-standard": "~1.0.0", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6.x-dev", "dev-develop": "2.7.x-dev"}}, "autoload": {"psr-4": {"Laminas\\Escaper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "homepage": "https://laminas.dev", "keywords": ["escaper", "laminas"], "time": "2019-12-31T16:43:30+00:00"}, {"name": "laminas/laminas-feed", "version": "2.12.2", "source": {"type": "git", "url": "https://github.com/laminas/laminas-feed.git", "reference": "8a193ac96ebcb3e16b6ee754ac2a889eefacb654"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-feed/zipball/8a193ac96ebcb3e16b6ee754ac2a889eefacb654", "reference": "8a193ac96ebcb3e16b6ee754ac2a889eefacb654", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "laminas/laminas-escaper": "^2.5.2", "laminas/laminas-stdlib": "^3.2.1", "laminas/laminas-zendframework-bridge": "^1.0", "php": "^5.6 || ^7.0"}, "replace": {"zendframework/zend-feed": "^2.12.0"}, "require-dev": {"laminas/laminas-cache": "^2.7.2", "laminas/laminas-coding-standard": "~1.0.0", "laminas/laminas-db": "^2.8.2", "laminas/laminas-http": "^2.7", "laminas/laminas-servicemanager": "^2.7.8 || ^3.3", "laminas/laminas-validator": "^2.10.1", "phpunit/phpunit": "^5.7.27 || ^6.5.14 || ^7.5.20", "psr/http-message": "^1.0.1"}, "suggest": {"laminas/laminas-cache": "Laminas\\Cache component, for optionally caching feeds between requests", "laminas/laminas-db": "Laminas\\Db component, for use with PubSubHubbub", "laminas/laminas-http": "Laminas\\Http for PubSubHubbub, and optionally for use with Laminas\\Feed\\Reader", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component, for easily extending ExtensionManager implementations", "laminas/laminas-validator": "Laminas\\Validator component, for validating email addresses used in Atom feeds and entries when using the Writer subcomponent", "psr/http-message": "PSR-7 ^1.0.1, if you wish to use Laminas\\Feed\\Reader\\Http\\Psr7ResponseDecorator"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.12.x-dev", "dev-develop": "2.13.x-dev"}}, "autoload": {"psr-4": {"Laminas\\Feed\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides functionality for consuming RSS and Atom feeds", "homepage": "https://laminas.dev", "keywords": ["feed", "laminas"], "time": "2020-03-29T12:36:29+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "2b18347625a2f06a1a485acfbc870f699dbe51c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/2b18347625a2f06a1a485acfbc870f699dbe51c6", "reference": "2b18347625a2f06a1a485acfbc870f699dbe51c6", "shasum": ""}, "require": {"laminas/laminas-zendframework-bridge": "^1.0", "php": "^5.6 || ^7.0"}, "replace": {"zendframework/zend-stdlib": "self.version"}, "require-dev": {"laminas/laminas-coding-standard": "~1.0.0", "phpbench/phpbench": "^0.13", "phpunit/phpunit": "^5.7.27 || ^6.5.8 || ^7.1.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev", "dev-develop": "3.3.x-dev"}}, "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "time": "2019-12-31T17:51:15+00:00"}, {"name": "laminas/laminas-zendframework-bridge", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/laminas/laminas-zendframework-bridge.git", "reference": "fcd87520e4943d968557803919523772475e8ea3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-zendframework-bridge/zipball/fcd87520e4943d968557803919523772475e8ea3", "reference": "fcd87520e4943d968557803919523772475e8ea3", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^7.5 || ^8.1", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev", "dev-develop": "1.1.x-dev"}, "laminas": {"module": "Laminas\\ZendFrameworkBridge"}}, "autoload": {"files": ["src/autoload.php"], "psr-4": {"Laminas\\ZendFrameworkBridge\\": "src//"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Alias legacy ZF class names to Laminas Project equivalents.", "keywords": ["ZendFramework", "autoloading", "laminas", "zf"], "time": "2020-05-20T16:45:56+00:00"}, {"name": "masterminds/html5", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "2c37c6c520b995b761674de3be8455a381679067"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/2c37c6c520b995b761674de3be8455a381679067", "reference": "2c37c6c520b995b761674de3be8455a381679067", "shasum": ""}, "require": {"ext-libxml": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.*", "sami/sami": "~2.0", "satooshi/php-coveralls": "1.0.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "time": "2017-09-04T12:26:28+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2018-07-02T15:55:56+00:00"}, {"name": "pear/archive_tar", "version": "1.4.9", "source": {"type": "git", "url": "https://github.com/pear/Archive_Tar.git", "reference": "c5b00053770e1d72128252c62c2c1a12c26639f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Archive_Tar/zipball/c5b00053770e1d72128252c62c2c1a12c26639f0", "reference": "c5b00053770e1d72128252c62c2c1a12c26639f0", "shasum": ""}, "require": {"pear/pear-core-minimal": "^1.10.0alpha2", "php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-bz2": "Bz2 compression support.", "ext-xz": "Lzma2 compression support.", "ext-zlib": "Gzip compression support."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Archive_Tar": ""}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Tar file management class with compression support (gzip, bzip2, lzma2)", "homepage": "https://github.com/pear/Archive_Tar", "keywords": ["archive", "tar"], "time": "2019-12-04T10:17:28+00:00"}, {"name": "pear/console_getopt", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/pear/Console_Getopt.git", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Console_Getopt/zipball/a41f8d3e668987609178c7c4a9fe48fecac53fa0", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Console": "./"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "Stig Bakken", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Helper"}], "description": "More info available on: http://pear.php.net/package/Console_<PERSON>opt", "time": "2019-11-20T18:27:48+00:00"}, {"name": "pear/pear-core-minimal", "version": "v1.10.10", "source": {"type": "git", "url": "https://github.com/pear/pear-core-minimal.git", "reference": "625a3c429d9b2c1546438679074cac1b089116a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/pear-core-minimal/zipball/625a3c429d9b2c1546438679074cac1b089116a7", "reference": "625a3c429d9b2c1546438679074cac1b089116a7", "shasum": ""}, "require": {"pear/console_getopt": "~1.4", "pear/pear_exception": "~1.0"}, "replace": {"rsky/pear-core-min": "self.version"}, "type": "library", "autoload": {"psr-0": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["src/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}], "description": "Minimal set of PEAR core files to be used as composer dependency", "time": "2019-11-19T19:00:24+00:00"}, {"name": "pear/pear_exception", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/pear/PEAR_Exception.git", "reference": "dbb42a5a0e45f3adcf99babfb2a1ba77b8ac36a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/PEAR_Exception/zipball/dbb42a5a0e45f3adcf99babfb2a1ba77b8ac36a7", "reference": "dbb42a5a0e45f3adcf99babfb2a1ba77b8ac36a7", "shasum": ""}, "require": {"php": ">=4.4.0"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "class", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["PEAR/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["."], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PEAR Exception base class.", "homepage": "https://github.com/pear/PEAR_Exception", "keywords": ["exception"], "time": "2019-12-10T10:24:42+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2020-03-23T09:12:05+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "stack/builder", "version": "v1.0.5", "source": {"type": "git", "url": "https://github.com/stackphp/builder.git", "reference": "fb3d136d04c6be41120ebf8c0cc71fe9507d750a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stackphp/builder/zipball/fb3d136d04c6be41120ebf8c0cc71fe9507d750a", "reference": "fb3d136d04c6be41120ebf8c0cc71fe9507d750a", "shasum": ""}, "require": {"php": ">=5.3.0", "symfony/http-foundation": "~2.1|~3.0|~4.0", "symfony/http-kernel": "~2.1|~3.0|~4.0"}, "require-dev": {"silex/silex": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Stack": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Builder for stack middlewares based on HttpKernelInterface.", "keywords": ["stack"], "time": "2017-11-18T14:57:29+00:00"}, {"name": "symfony-cmf/routing", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/symfony-cmf/routing.git", "reference": "fb1e7f85ff8c6866238b7e73a490a0a0243ae8ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony-cmf/routing/zipball/fb1e7f85ff8c6866238b7e73a490a0a0243ae8ac", "reference": "fb1e7f85ff8c6866238b7e73a490a0a0243ae8ac", "shasum": ""}, "require": {"php": "^5.3.9|^7.0", "psr/log": "1.*", "symfony/http-kernel": "^2.2|3.*", "symfony/routing": "^2.2|3.*"}, "require-dev": {"friendsofsymfony/jsrouting-bundle": "^1.1", "symfony-cmf/testing": "^1.3", "symfony/config": "^2.2|3.*", "symfony/dependency-injection": "^2.0.5|3.*", "symfony/event-dispatcher": "^2.1|3.*"}, "suggest": {"symfony/event-dispatcher": "DynamicRouter can optionally trigger an event at the start of matching. Minimal version (~2.1)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Cmf\\Component\\Routing\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony CMF Community", "homepage": "https://github.com/symfony-cmf/Routing/contributors"}], "description": "Extends the Symfony2 routing component for dynamic routes and chaining several routers", "homepage": "http://cmf.symfony.com", "keywords": ["database", "routing"], "time": "2017-05-09T08:10:41+00:00"}, {"name": "symfony/class-loader", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/class-loader.git", "reference": "e4636a4f23f157278a19e5db160c63de0da297d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/class-loader/zipball/e4636a4f23f157278a19e5db160c63de0da297d8", "reference": "e4636a4f23f157278a19e5db160c63de0da297d8", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "require-dev": {"symfony/finder": "~2.8|~3.0|~4.0", "symfony/polyfill-apcu": "~1.1"}, "suggest": {"symfony/polyfill-apcu": "For using ApcClassLoader on HHVM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\ClassLoader\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony ClassLoader Component", "homepage": "https://symfony.com", "time": "2020-03-15T09:38:08+00:00"}, {"name": "symfony/console", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "bfe29ead7e7b1cc9ce74c6a40d06ad1f96fced13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/bfe29ead7e7b1cc9ce74c6a40d06ad1f96fced13", "reference": "bfe29ead7e7b1cc9ce74c6a40d06ad1f96fced13", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/debug": "~2.8|~3.0|~4.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~3.3|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/lock": "~3.4|~4.0", "symfony/process": "~3.3|~4.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2020-05-30T18:58:05+00:00"}, {"name": "symfony/debug", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "518c6a00d0872da30bd06aee3ea59a0a5cf54d6d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/518c6a00d0872da30bd06aee3ea59a0a5cf54d6d", "reference": "518c6a00d0872da30bd06aee3ea59a0a5cf54d6d", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/http-kernel": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2020-05-22T18:25:20+00:00"}, {"name": "symfony/dependency-injection", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "e39380b7104b0ec538a075ae919f00c7e5267bac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/e39380b7104b0ec538a075ae919f00c7e5267bac", "reference": "e39380b7104b0ec538a075ae919f00c7e5267bac", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/container": "^1.0"}, "conflict": {"symfony/config": "<3.3.7", "symfony/finder": "<3.3", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"psr/container-implementation": "1.0"}, "require-dev": {"symfony/config": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DependencyInjection Component", "homepage": "https://symfony.com", "time": "2020-05-30T21:06:01+00:00"}, {"name": "symfony/event-dispatcher", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "14d978f8e8555f2de719c00eb65376be7d2e9081"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/14d978f8e8555f2de719c00eb65376be7d2e9081", "reference": "14d978f8e8555f2de719c00eb65376be7d2e9081", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/dependency-injection": "<3.3"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2020-05-05T15:06:23+00:00"}, {"name": "symfony/http-foundation", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "fbd216d2304b1a3fe38d6392b04729c8dd356359"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/fbd216d2304b1a3fe38d6392b04729c8dd356359", "reference": "fbd216d2304b1a3fe38d6392b04729c8dd356359", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php70": "~1.6"}, "require-dev": {"symfony/expression-language": "~2.8|~3.0|~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpFoundation Component", "homepage": "https://symfony.com", "time": "2020-05-16T13:15:54+00:00"}, {"name": "symfony/http-kernel", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "e4e4ed6c008c983645b4eee6b67d8f258cde54df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/e4e4ed6c008c983645b4eee6b67d8f258cde54df", "reference": "e4e4ed6c008c983645b4eee6b67d8f258cde54df", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0", "symfony/debug": "^3.3.3|~4.0", "symfony/event-dispatcher": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~3.4.12|~4.0.12|^4.1.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php56": "~1.8"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4.10|<4.0.10,>=4", "symfony/var-dumper": "<3.3", "twig/twig": "<1.34|<2.4,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "~2.8|~3.0|~4.0", "symfony/class-loader": "~2.8|~3.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/console": "~2.8|~3.0|~4.0", "symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "^3.4.10|^4.0.10", "symfony/dom-crawler": "~2.8|~3.0|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0", "symfony/routing": "~3.4|~4.0", "symfony/stopwatch": "~2.8|~3.0|~4.0", "symfony/templating": "~2.8|~3.0|~4.0", "symfony/translation": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": "", "symfony/finder": "", "symfony/var-dumper": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony HttpKernel Component", "homepage": "https://symfony.com", "time": "2020-05-31T05:14:17+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "e94c8b1bbe2bc77507a1056cdb06451c75b427f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/e94c8b1bbe2bc77507a1056cdb06451c75b427f9", "reference": "e94c8b1bbe2bc77507a1056cdb06451c75b427f9", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "c4de7601eefbf25f9d47190abe07f79fe0a27424"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/c4de7601eefbf25f9d47190abe07f79fe0a27424", "reference": "c4de7601eefbf25f9d47190abe07f79fe0a27424", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/3bff59ea7047e925be6b7f2059d60af31bb46d6a", "reference": "3bff59ea7047e925be6b7f2059d60af31bb46d6a", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fa79b11539418b02fc5e1897267673ba2c19419c", "reference": "fa79b11539418b02fc5e1897267673ba2c19419c", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "e3c8c138280cdfe4b81488441555583aa1984e23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/e3c8c138280cdfe4b81488441555583aa1984e23", "reference": "e3c8c138280cdfe4b81488441555583aa1984e23", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/polyfill-util": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php56\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php70", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php70.git", "reference": "82225c2d7d23d7e70515496d249c0152679b468e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php70/zipball/82225c2d7d23d7e70515496d249c0152679b468e", "reference": "82225c2d7d23d7e70515496d249c0152679b468e", "shasum": ""}, "require": {"paragonie/random_compat": "~1.0|~2.0|~9.99", "php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php70\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "f048e612a3905f34931127360bdd2def19a5e582"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/f048e612a3905f34931127360bdd2def19a5e582", "reference": "f048e612a3905f34931127360bdd2def19a5e582", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-05-12T16:47:27+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.17.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "4a5b6bba3259902e386eb80dd1956181ee90b5b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/4a5b6bba3259902e386eb80dd1956181ee90b5b2", "reference": "4a5b6bba3259902e386eb80dd1956181ee90b5b2", "shasum": ""}, "require": {"php": ">=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2020-06-06T08:46:27+00:00"}, {"name": "symfony/polyfill-util", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-util.git", "reference": "4afb4110fc037752cf0ce9869f9ab8162c4e20d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-util/zipball/4afb4110fc037752cf0ce9869f9ab8162c4e20d7", "reference": "4afb4110fc037752cf0ce9869f9ab8162c4e20d7", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Util\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony utilities for portability of PHP codes", "homepage": "https://symfony.com", "keywords": ["compat", "compatibility", "polyfill", "shim"], "time": "2020-05-12T16:14:59+00:00"}, {"name": "symfony/process", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "8a895f0c92a7c4b10db95139bcff71bdf66d4d21"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/8a895f0c92a7c4b10db95139bcff71bdf66d4d21", "reference": "8a895f0c92a7c4b10db95139bcff71bdf66d4d21", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2020-05-23T17:05:51+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "a33352af16f78a5ff4f9d90811536abf210df12b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/a33352af16f78a5ff4f9d90811536abf210df12b", "reference": "a33352af16f78a5ff4f9d90811536abf210df12b", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "psr/http-message": "^1.0", "symfony/http-foundation": "^2.3.42 || ^3.4 || ^4.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4 || ^4.0"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "time": "2019-04-03T17:09:40+00:00"}, {"name": "symfony/routing", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "e0d43b6f9417ad59ecaa8e2f799b79eef417387f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/e0d43b6f9417ad59ecaa8e2f799b79eef417387f", "reference": "e0d43b6f9417ad59ecaa8e2f799b79eef417387f", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "conflict": {"symfony/config": "<3.3.1", "symfony/dependency-injection": "<3.3", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "psr/log": "~1.0", "symfony/config": "^3.3.1|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Routing Component", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "time": "2020-05-30T19:50:06+00:00"}, {"name": "symfony/serializer", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "0db90db012b1b0a04fbb2d64ae9160871cad9d4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/0db90db012b1b0a04fbb2d64ae9160871cad9d4f", "reference": "0db90db012b1b0a04fbb2d64ae9160871cad9d4f", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/type-resolver": "<0.2.1", "symfony/dependency-injection": "<3.2", "symfony/property-access": ">=3.0,<3.0.4|>=2.8,<2.8.4", "symfony/property-info": "<3.1", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0", "symfony/cache": "~3.1|~4.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.2|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/property-access": "~2.8|~3.0|~4.0", "symfony/property-info": "^3.4.13|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Serializer Component", "homepage": "https://symfony.com", "time": "2020-05-30T18:58:05+00:00"}, {"name": "symfony/translation", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "b0cd62ef0ff7ec31b67d78d7fc818e2bda4e844f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/b0cd62ef0ff7ec31b67d78d7fc818e2bda4e844f", "reference": "b0cd62ef0ff7ec31b67d78d7fc818e2bda4e844f", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.8", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.4|~4.0", "symfony/finder": "~2.8|~3.0|~4.0", "symfony/http-kernel": "~3.4|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/var-dumper": "~3.4|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2020-05-30T18:58:05+00:00"}, {"name": "symfony/validator", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "5fb88120a11a75e17b602103a893dd8b27804529"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/5fb88120a11a75e17b602103a893dd8b27804529", "reference": "5fb88120a11a75e17b602103a893dd8b27804529", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/translation": "~2.8|~3.0|~4.0"}, "conflict": {"doctrine/lexer": "<1.0.2", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.3", "symfony/http-kernel": "<3.3.5", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "~1.7", "doctrine/cache": "~1.0", "egulias/email-validator": "^2.1.10", "symfony/cache": "~3.1|~4.0", "symfony/config": "~2.8|~3.0|~4.0", "symfony/dependency-injection": "~3.3|~4.0", "symfony/expression-language": "~2.8|~3.0|~4.0", "symfony/http-foundation": "~2.8|~3.0|~4.0", "symfony/http-kernel": "^3.3.5|~4.0", "symfony/intl": "^2.8.18|^3.2.5|~4.0", "symfony/property-access": "~2.8|~3.0|~4.0", "symfony/var-dumper": "~3.3|~4.0", "symfony/yaml": "~3.4|~4.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Validator Component", "homepage": "https://symfony.com", "time": "2020-05-30T18:43:38+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.10", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "56b3aa5eab0ac6720dcd559fd1d590ce301594ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/56b3aa5eab0ac6720dcd559fd1d590ce301594ac", "reference": "56b3aa5eab0ac6720dcd559fd1d590ce301594ac", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.4-dev"}}, "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony mechanism for exploring and dumping PHP variables", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2020-05-30T20:06:45+00:00"}, {"name": "symfony/yaml", "version": "v3.4.41", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "7233ac2bfdde24d672f5305f2b3f6b5d741ef8eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/7233ac2bfdde24d672f5305f2b3f6b5d741ef8eb", "reference": "7233ac2bfdde24d672f5305f2b3f6b5d741ef8eb", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "~3.4|~4.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2020-05-11T07:51:54+00:00"}, {"name": "twig/twig", "version": "v1.42.5", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "87b2ea9d8f6fd014d0621ca089bb1b3769ea3f8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/87b2ea9d8f6fd014d0621ca089bb1b3769ea3f8e", "reference": "87b2ea9d8f6fd014d0621ca089bb1b3769ea3f8e", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/polyfill-ctype": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.42-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "time": "2020-02-11T05:59:23+00:00"}, {"name": "typo3/phar-stream-wrapper", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/TYPO3/phar-stream-wrapper.git", "reference": "e0c1b495cfac064f4f5c4bcb6bf67bb7f345ed04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/phar-stream-wrapper/zipball/e0c1b495cfac064f4f5c4bcb6bf67bb7f345ed04", "reference": "e0c1b495cfac064f4f5c4bcb6bf67bb7f345ed04", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.0"}, "require-dev": {"ext-xdebug": "*", "phpunit/phpunit": "^6.5"}, "suggest": {"ext-fileinfo": "For PHP builtin file type guessing, otherwise uses internal processing"}, "type": "library", "extra": {"branch-alias": {"dev-master": "v3.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\PharStreamWrapper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Interceptors for PHP's native phar:// stream handling", "homepage": "https://typo3.org/", "keywords": ["phar", "php", "security", "stream-wrapper"], "time": "2019-12-10T11:53:27+00:00"}], "packages-dev": [{"name": "alchemy/zippy", "version": "0.4.9", "source": {"type": "git", "url": "https://github.com/alchemy-fr/Zippy.git", "reference": "59fbeefb9a249122867ef25e53addfcce31850d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alchemy-fr/Zippy/zipball/59fbeefb9a249122867ef25e53addfcce31850d7", "reference": "59fbeefb9a249122867ef25e53addfcce31850d7", "shasum": ""}, "require": {"doctrine/collections": "~1.0", "php": ">=5.5", "symfony/filesystem": "^2.0.5 || ^3.0 || ^4.0", "symfony/polyfill-mbstring": "^1.3", "symfony/process": "^2.1 || ^3.0 || ^4.0"}, "require-dev": {"ext-zip": "*", "guzzle/guzzle": "~3.0", "guzzlehttp/guzzle": "^6.0", "phpunit/phpunit": "^4.0 || ^5.0", "symfony/finder": "^2.0.5 || ^3.0 || ^4.0"}, "suggest": {"ext-zip": "To use the ZipExtensionAdapter", "guzzle/guzzle": "To use the GuzzleTeleporter with Guzzle 3", "guzzlehttp/guzzle": "To use the GuzzleTeleporter with Guzzle 6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.4.x-dev"}}, "autoload": {"psr-4": {"Alchemy\\Zippy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Alchemy", "email": "<EMAIL>", "homepage": "http://www.alchemy.fr/"}], "description": "<PERSON><PERSON><PERSON>, the archive manager companion", "keywords": ["bzip", "compression", "tar", "zip"], "time": "2018-02-22T13:58:36+00:00"}, {"name": "behat/mink", "version": "v1.8.1", "source": {"type": "git", "url": "https://github.com/minkphp/Mink.git", "reference": "07c6a9fe3fa98c2de074b25d9ed26c22904e3887"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/minkphp/Mink/zipball/07c6a9fe3fa98c2de074b25d9ed26c22904e3887", "reference": "07c6a9fe3fa98c2de074b25d9ed26c22904e3887", "shasum": ""}, "require": {"php": ">=5.3.1", "symfony/css-selector": "^2.7|^3.0|^4.0|^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20", "symfony/debug": "^2.7|^3.0|^4.0", "symfony/phpunit-bridge": "^3.4.38 || ^5.0.5"}, "suggest": {"behat/mink-browserkit-driver": "extremely fast headless driver for Symfony\\Kernel-based apps (Sf2, Silex)", "behat/mink-goutte-driver": "fast headless driver for any app without JS emulation", "behat/mink-selenium2-driver": "slow, but JS-enabled driver for any app (requires Selenium2)", "behat/mink-zombie-driver": "fast and JS-enabled headless driver for any app (requires node.js)", "dmore/chrome-mink-driver": "fast and JS-enabled driver for any app (requires chromium or google chrome)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8.x-dev"}}, "autoload": {"psr-4": {"Behat\\Mink\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Browser controller/emulator abstraction for PHP", "homepage": "http://mink.behat.org/", "keywords": ["browser", "testing", "web"], "time": "2020-03-11T15:45:53+00:00"}, {"name": "behat/mink-browserkit-driver", "version": "v1.3.4", "source": {"type": "git", "url": "https://github.com/minkphp/MinkBrowserKitDriver.git", "reference": "e3b90840022ebcd544c7b394a3c9597ae242cbee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/minkphp/MinkBrowserKitDriver/zipball/e3b90840022ebcd544c7b394a3c9597ae242cbee", "reference": "e3b90840022ebcd544c7b394a3c9597ae242cbee", "shasum": ""}, "require": {"behat/mink": "^1.7.1@dev", "php": ">=5.3.6", "symfony/browser-kit": "~2.3|~3.0|~4.0", "symfony/dom-crawler": "~2.3|~3.0|~4.0"}, "require-dev": {"mink/driver-testsuite": "dev-master", "symfony/debug": "^2.7|^3.0|^4.0", "symfony/http-kernel": "~2.3|~3.0|~4.0"}, "type": "mink-driver", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Behat\\Mink\\Driver\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Symfony2 BrowserKit driver for Mink framework", "homepage": "http://mink.behat.org/", "keywords": ["Mink", "Symfony2", "browser", "testing"], "time": "2020-03-11T09:49:45+00:00"}, {"name": "behat/mink-goutte-driver", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/minkphp/MinkGoutteDriver.git", "reference": "8b9ad6d2d95bc70b840d15323365f52fcdaea6ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/minkphp/MinkGoutteDriver/zipball/8b9ad6d2d95bc70b840d15323365f52fcdaea6ca", "reference": "8b9ad6d2d95bc70b840d15323365f52fcdaea6ca", "shasum": ""}, "require": {"behat/mink": "~1.6@dev", "behat/mink-browserkit-driver": "~1.2@dev", "fabpot/goutte": "~1.0.4|~2.0|~3.1", "php": ">=5.3.1"}, "require-dev": {"symfony/phpunit-bridge": "~2.7|~3.0"}, "type": "mink-driver", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Behat\\Mink\\Driver\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "<PERSON><PERSON><PERSON> driver for Mink framework", "homepage": "http://mink.behat.org/", "keywords": ["browser", "goutte", "headless", "testing"], "time": "2016-03-05T09:04:22+00:00"}, {"name": "behat/mink-selenium2-driver", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/minkphp/MinkSelenium2Driver.git", "reference": "312a967dd527f28980cce40850339cd5316da092"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/minkphp/MinkSelenium2Driver/zipball/312a967dd527f28980cce40850339cd5316da092", "reference": "312a967dd527f28980cce40850339cd5316da092", "shasum": ""}, "require": {"behat/mink": "~1.7@dev", "instaclick/php-webdriver": "~1.1", "php": ">=5.4"}, "require-dev": {"mink/driver-testsuite": "dev-master"}, "type": "mink-driver", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Behat\\Mink\\Driver\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/pete-otaqui"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}], "description": "Selenium2 (WebDriver) driver for Mink framework", "homepage": "http://mink.behat.org/", "keywords": ["ajax", "browser", "javascript", "selenium", "testing", "webdriver"], "time": "2020-03-11T14:43:21+00:00"}, {"name": "chi-teck/drupal-code-generator", "version": "1.32.0", "source": {"type": "git", "url": "https://github.com/Chi-teck/drupal-code-generator.git", "reference": "0e045f7a7e747af3d8f603156bf4d73be5768246"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Chi-teck/drupal-code-generator/zipball/0e045f7a7e747af3d8f603156bf4d73be5768246", "reference": "0e045f7a7e747af3d8f603156bf4d73be5768246", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.5.9", "symfony/console": "^3.4 || ^4.0", "symfony/filesystem": "^2.7 || ^3.4 || ^4.0", "twig/twig": "^1.41 || ^2.12"}, "bin": ["bin/dcg"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/bootstrap.php"], "psr-4": {"DrupalCodeGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal code generator", "time": "2020-04-16T06:45:06+00:00"}, {"name": "composer/ca-bundle", "version": "1.2.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "95c63ab2117a72f48f5a55da9740a3273d45b7fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/95c63ab2117a72f48f5a55da9740a3273d45b7fd", "reference": "95c63ab2117a72f48f5a55da9740a3273d45b7fd", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8", "psr/log": "^1.0", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "time": "2020-04-08T08:27:21+00:00"}, {"name": "composer/composer", "version": "1.10.8", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "56e0e094478f30935e9128552188355fa9712291"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/56e0e094478f30935e9128552188355fa9712291", "reference": "56e0e094478f30935e9128552188355fa9712291", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/semver": "^1.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^1.1", "justinrainbow/json-schema": "^5.2.10", "php": "^5.3.2 || ^7.0", "psr/log": "^1.0", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.0", "symfony/console": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/filesystem": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/finder": "^2.7 || ^3.0 || ^4.0 || ^5.0", "symfony/process": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "conflict": {"symfony/console": "2.8.38"}, "require-dev": {"phpspec/prophecy": "^1.10", "symfony/phpunit-bridge": "^4.2"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "time": "2020-06-24T19:23:30+00:00"}, {"name": "composer/installers", "version": "v1.9.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "b93bcf0fa1fccb0b7d176b0967d969691cd74cca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/b93bcf0fa1fccb0b7d176b0967d969691cd74cca", "reference": "b93bcf0fa1fccb0b7d176b0967d969691cd74cca", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || 2.0.*@dev", "composer/semver": "1.0.* || 2.0.*@dev", "phpunit/phpunit": "^4.8.36", "sebastian/comparator": "^1.2.4", "symfony/process": "^2.3"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "phpbb", "piwik", "ppi", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "typo3", "wordpress", "yawik", "zend", "zikula"], "time": "2020-04-07T06:57:05+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.3", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "0c3e51e1880ca149682332770e25977c70cf9dae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/0c3e51e1880ca149682332770e25977c70cf9dae", "reference": "0c3e51e1880ca149682332770e25977c70cf9dae", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "time": "2020-02-14T07:44:31+00:00"}, {"name": "composer/xdebug-handler", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51", "reference": "fa2aaf99e2087f013a14f7432c1cd2dd7d8f1f51", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "time": "2020-06-04T11:16:35+00:00"}, {"name": "consolidation/annotated-command", "version": "2.12.0", "source": {"type": "git", "url": "https://github.com/consolidation/annotated-command.git", "reference": "512a2e54c98f3af377589de76c43b24652bcb789"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/annotated-command/zipball/512a2e54c98f3af377589de76c43b24652bcb789", "reference": "512a2e54c98f3af377589de76c43b24652bcb789", "shasum": ""}, "require": {"consolidation/output-formatters": "^3.4", "php": ">=5.4.5", "psr/log": "^1", "symfony/console": "^2.8|^3|^4", "symfony/event-dispatcher": "^2.5|^3|^4", "symfony/finder": "^2.5|^3|^4"}, "require-dev": {"g1a/composer-test-scenarios": "^3", "php-coveralls/php-coveralls": "^1", "phpunit/phpunit": "^6", "squizlabs/php_codesniffer": "^2.7"}, "type": "library", "extra": {"scenarios": {"symfony4": {"require": {"symfony/console": "^4.0"}, "config": {"platform": {"php": "7.1.3"}}}, "symfony2": {"require": {"symfony/console": "^2.8"}, "require-dev": {"phpunit/phpunit": "^4.8.36"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.4.8"}}, "scenario-options": {"create-lockfile": "false"}}, "phpunit4": {"require-dev": {"phpunit/phpunit": "^4.8.36"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.4.8"}}}}, "branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\AnnotatedCommand\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Initialize Symfony Console commands from annotated command class methods.", "time": "2019-03-08T16:55:03+00:00"}, {"name": "consolidation/config", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/consolidation/config.git", "reference": "cac1279bae7efb5c7fb2ca4c3ba4b8eb741a96c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/config/zipball/cac1279bae7efb5c7fb2ca4c3ba4b8eb741a96c1", "reference": "cac1279bae7efb5c7fb2ca4c3ba4b8eb741a96c1", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "grasmash/expander": "^1", "php": ">=5.4.0"}, "require-dev": {"g1a/composer-test-scenarios": "^3", "php-coveralls/php-coveralls": "^1", "phpunit/phpunit": "^5", "squizlabs/php_codesniffer": "2.*", "symfony/console": "^2.5|^3|^4", "symfony/yaml": "^2.8.11|^3|^4"}, "suggest": {"symfony/yaml": "Required to use Consolidation\\Config\\Loader\\YamlConfigLoader"}, "type": "library", "extra": {"scenarios": {"symfony4": {"require-dev": {"symfony/console": "^4.0"}, "config": {"platform": {"php": "7.1.3"}}}, "symfony2": {"require-dev": {"symfony/console": "^2.8", "symfony/event-dispatcher": "^2.8", "phpunit/phpunit": "^4.8.36"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.4.8"}}}}, "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provide configuration services for a commandline tool.", "time": "2019-03-03T19:37:04+00:00"}, {"name": "consolidation/filter-via-dot-access-data", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/consolidation/filter-via-dot-access-data.git", "reference": "a53e96c6b9f7f042f5e085bf911f3493cea823c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/filter-via-dot-access-data/zipball/a53e96c6b9f7f042f5e085bf911f3493cea823c6", "reference": "a53e96c6b9f7f042f5e085bf911f3493cea823c6", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "php": ">=5.5.0"}, "require-dev": {"consolidation/robo": "^1.2.3", "g1a/composer-test-scenarios": "^3", "knplabs/github-api": "^2.7", "php-coveralls/php-coveralls": "^1", "php-http/guzzle6-adapter": "^1.1", "phpunit/phpunit": "^5", "squizlabs/php_codesniffer": "^2.8", "symfony/console": "^2.8|^3|^4"}, "type": "library", "extra": {"scenarios": {"phpunit5": {"require-dev": {"phpunit/phpunit": "^5.7.27"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.6.33"}}}}, "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Filter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "This project uses dflydev/dot-access-data to provide simple output filtering for applications built with annotated-command / Robo.", "time": "2019-01-18T06:05:07+00:00"}, {"name": "consolidation/log", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/consolidation/log.git", "reference": "b2e887325ee90abc96b0a8b7b474cd9e7c896e3a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/log/zipball/b2e887325ee90abc96b0a8b7b474cd9e7c896e3a", "reference": "b2e887325ee90abc96b0a8b7b474cd9e7c896e3a", "shasum": ""}, "require": {"php": ">=5.4.5", "psr/log": "^1.0", "symfony/console": "^2.8|^3|^4"}, "require-dev": {"g1a/composer-test-scenarios": "^3", "php-coveralls/php-coveralls": "^1", "phpunit/phpunit": "^6", "squizlabs/php_codesniffer": "^2"}, "type": "library", "extra": {"scenarios": {"symfony4": {"require": {"symfony/console": "^4.0"}, "config": {"platform": {"php": "7.1.3"}}}, "symfony2": {"require": {"symfony/console": "^2.8"}, "require-dev": {"phpunit/phpunit": "^4.8.36"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.4.8"}}}, "phpunit4": {"require-dev": {"phpunit/phpunit": "^4.8.36"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.4.8"}}}}, "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Improved Psr-3 / Psr\\Log logger based on Symfony Console components.", "time": "2019-01-01T17:30:51+00:00"}, {"name": "consolidation/output-formatters", "version": "3.5.0", "source": {"type": "git", "url": "https://github.com/consolidation/output-formatters.git", "reference": "99ec998ffb697e0eada5aacf81feebfb13023605"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/output-formatters/zipball/99ec998ffb697e0eada5aacf81feebfb13023605", "reference": "99ec998ffb697e0eada5aacf81feebfb13023605", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "php": ">=5.4.0", "symfony/console": "^2.8|^3|^4", "symfony/finder": "^2.5|^3|^4"}, "require-dev": {"g1a/composer-test-scenarios": "^3", "php-coveralls/php-coveralls": "^1", "phpunit/phpunit": "^5.7.27", "squizlabs/php_codesniffer": "^2.7", "symfony/var-dumper": "^2.8|^3|^4", "victorjonsson/markdowndocs": "^1.3"}, "suggest": {"symfony/var-dumper": "For using the var_dump formatter"}, "type": "library", "extra": {"scenarios": {"symfony4": {"require": {"symfony/console": "^4.0"}, "require-dev": {"phpunit/phpunit": "^6"}, "config": {"platform": {"php": "7.1.3"}}}, "symfony3": {"require": {"symfony/console": "^3.4", "symfony/finder": "^3.4", "symfony/var-dumper": "^3.4"}, "config": {"platform": {"php": "5.6.32"}}}, "symfony2": {"require": {"symfony/console": "^2.8"}, "require-dev": {"phpunit/phpunit": "^4.8.36"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.4.8"}}, "scenario-options": {"create-lockfile": "false"}}}, "branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\OutputFormatters\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Format text by applying transformations provided by plug-in formatters.", "time": "2019-05-30T23:16:01+00:00"}, {"name": "consolidation/robo", "version": "1.4.12", "source": {"type": "git", "url": "https://github.com/consolidation/Robo.git", "reference": "eb45606f498b3426b9a98b7c85e300666a968e51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/Robo/zipball/eb45606f498b3426b9a98b7c85e300666a968e51", "reference": "eb45606f498b3426b9a98b7c85e300666a968e51", "shasum": ""}, "require": {"consolidation/annotated-command": "^2.11.0|^4.1", "consolidation/config": "^1.2.1", "consolidation/log": "^1.1.1|^2", "consolidation/output-formatters": "^3.1.13|^4.1", "consolidation/self-update": "^1.1.5", "grasmash/yaml-expander": "^1.4", "league/container": "^2.4.1", "php": ">=5.5.0", "symfony/console": "^2.8|^3|^4", "symfony/event-dispatcher": "^2.5|^3|^4", "symfony/filesystem": "^2.5|^3|^4", "symfony/finder": "^2.5|^3|^4", "symfony/process": "^2.5|^3|^4"}, "replace": {"codegyre/robo": "< 1.0"}, "require-dev": {"g1a/composer-test-scenarios": "^3", "natxet/cssmin": "3.0.4", "patchwork/jsqueeze": "^2", "pear/archive_tar": "^1.4.4", "php-coveralls/php-coveralls": "^1", "phpunit/phpunit": "^5.7.27", "squizlabs/php_codesniffer": "^3"}, "suggest": {"henrikbjorn/lurker": "For monitoring filesystem changes in taskWatch", "natxet/CssMin": "For minifying CSS files in taskMinify", "patchwork/jsqueeze": "For minifying JS files in taskMinify", "pear/archive_tar": "Allows tar archives to be created and extracted in taskPack and taskExtract, respectively."}, "bin": ["robo"], "type": "library", "extra": {"scenarios": {"symfony4": {"require": {"symfony/console": "^4"}, "config": {"platform": {"php": "7.1.3"}}}, "symfony2": {"require": {"symfony/console": "^2.8"}, "require-dev": {"phpunit/phpunit": "^4.8.36"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.5.9"}}, "scenario-options": {"create-lockfile": "false"}}}, "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Robo\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Modern task runner", "time": "2020-02-18T17:31:26+00:00"}, {"name": "consolidation/self-update", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/consolidation/self-update.git", "reference": "dba6b2c0708f20fa3ba8008a2353b637578849b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/self-update/zipball/dba6b2c0708f20fa3ba8008a2353b637578849b4", "reference": "dba6b2c0708f20fa3ba8008a2353b637578849b4", "shasum": ""}, "require": {"php": ">=5.5.0", "symfony/console": "^2.8|^3|^4|^5", "symfony/filesystem": "^2.5|^3|^4|^5"}, "bin": ["scripts/release"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"SelfUpdate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provides a self:update command for Symfony Console applications.", "time": "2020-04-13T02:49:20+00:00"}, {"name": "consolidation/site-alias", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/consolidation/site-alias.git", "reference": "fd40a03f80f8fd4684b10bef8c8c4ec5a9a9bf26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-alias/zipball/fd40a03f80f8fd4684b10bef8c8c4ec5a9a9bf26", "reference": "fd40a03f80f8fd4684b10bef8c8c4ec5a9a9bf26", "shasum": ""}, "require": {"consolidation/config": "^1.2.1|^2", "php": ">=5.5.0"}, "require-dev": {"consolidation/robo": "^1.2.3|^2", "g1a/composer-test-scenarios": "^3", "knplabs/github-api": "^2.7", "php-coveralls/php-coveralls": "^2.2", "php-http/guzzle6-adapter": "^1.1", "phpunit/phpunit": "^6", "squizlabs/php_codesniffer": "^2.8", "symfony/yaml": "~2.3|^3|^4.4|^5"}, "type": "library", "extra": {"scenarios": {"phpunit5": {"require-dev": {"phpunit/phpunit": "^5.7.27"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.6.33"}}}}, "branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteAlias\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Manage alias records for local and remote sites.", "time": "2020-05-28T00:33:41+00:00"}, {"name": "consolidation/site-process", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/consolidation/site-process.git", "reference": "f3211fa4c60671c6f068184221f06f932556e443"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-process/zipball/f3211fa4c60671c6f068184221f06f932556e443", "reference": "f3211fa4c60671c6f068184221f06f932556e443", "shasum": ""}, "require": {"consolidation/config": "^1.2.1", "consolidation/site-alias": "^3", "php": ">=5.6.0", "symfony/process": "^3.4"}, "require-dev": {"consolidation/robo": "^1.3", "g1a/composer-test-scenarios": "^3", "knplabs/github-api": "^2.7", "php-coveralls/php-coveralls": "^1", "php-http/guzzle6-adapter": "^1.1", "phpunit/phpunit": "^6", "squizlabs/php_codesniffer": "^2.8"}, "type": "library", "extra": {"scenarios": {"phpunit5": {"require-dev": {"phpunit/phpunit": "^5.7.27"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.6.33"}}}}, "branch-alias": {"dev-master": "0.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteProcess\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A thin wrapper around the Symfony Process Component that allows applications to use the Site Alias library to specify the target for a remote call.", "time": "2019-09-10T17:56:24+00:00"}, {"name": "container-interop/container-interop", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/container-interop/container-interop.git", "reference": "79cbf1341c22ec75643d841642dd5d6acd83bdb8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/container-interop/container-interop/zipball/79cbf1341c22ec75643d841642dd5d6acd83bdb8", "reference": "79cbf1341c22ec75643d841642dd5d6acd83bdb8", "shasum": ""}, "require": {"psr/container": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Interop\\Container\\": "src/Interop/Container/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Promoting the interoperability of container objects (DIC, SL, etc.)", "homepage": "https://github.com/container-interop/container-interop", "abandoned": "psr/container", "time": "2017-02-14T19:40:03+00:00"}, {"name": "cweagans/composer-patches", "version": "1.6.7", "source": {"type": "git", "url": "https://github.com/cweagans/composer-patches.git", "reference": "2e6f72a2ad8d59cd7e2b729f218bf42adb14f590"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweagans/composer-patches/zipball/2e6f72a2ad8d59cd7e2b729f218bf42adb14f590", "reference": "2e6f72a2ad8d59cd7e2b729f218bf42adb14f590", "shasum": ""}, "require": {"composer-plugin-api": "^1.0", "php": ">=5.3.0"}, "require-dev": {"composer/composer": "~1.0", "phpunit/phpunit": "~4.6"}, "type": "composer-plugin", "extra": {"class": "cweagans\\Composer\\Patches"}, "autoload": {"psr-4": {"cweagans\\Composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a way to patch Composer packages.", "time": "2019-08-29T20:11:49+00:00"}, {"name": "dflydev/dot-access-configuration", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-configuration.git", "reference": "2e6eb0c8b8830b26bb23defcfc38d4276508fc49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-configuration/zipball/2e6eb0c8b8830b26bb23defcfc38d4276508fc49", "reference": "2e6eb0c8b8830b26bb23defcfc38d4276508fc49", "shasum": ""}, "require": {"dflydev/dot-access-data": "1.*", "dflydev/placeholder-resolver": "1.*", "php": ">=5.3.2"}, "require-dev": {"symfony/yaml": "~2.1"}, "suggest": {"symfony/yaml": "Required for using the YAML Configuration Builders"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Dflydev\\DotAccessConfiguration": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}], "description": "Given a deep data structure representing a configuration, access configuration by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-configuration", "keywords": ["config", "configuration"], "time": "2018-09-08T23:00:17+00:00"}, {"name": "dflydev/dot-access-data", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "3fbd874921ab2c041e899d044585a2ab9795df8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/3fbd874921ab2c041e899d044585a2ab9795df8a", "reference": "3fbd874921ab2c041e899d044585a2ab9795df8a", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Dflydev\\DotAccessData": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "time": "2017-01-20T21:14:22+00:00"}, {"name": "dflydev/placeholder-resolver", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-placeholder-resolver.git", "reference": "c498d0cae91b1bb36cc7d60906dab8e62bb7c356"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-placeholder-resolver/zipball/c498d0cae91b1bb36cc7d60906dab8e62bb7c356", "reference": "c498d0cae91b1bb36cc7d60906dab8e62bb7c356", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Dflydev\\PlaceholderResolver": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}], "description": "Given a data source representing key => value pairs, resolve placeholders like ${foo.bar} to the value associated with the 'foo.bar' key in the data source.", "homepage": "https://github.com/dflydev/dflydev-placeholder-resolver", "keywords": ["placeholder", "resolver"], "time": "2012-10-28T21:08:28+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/instantiator", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "f350df0268e904597e3bd9c4685c53e0e333feea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/f350df0268e904597e3bd9c4685c53e0e333feea", "reference": "f350df0268e904597e3bd9c4685c53e0e333feea", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-shim": "^0.11", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "time": "2020-05-29T17:27:14+00:00"}, {"name": "drupal/coder", "version": "8.3.9", "source": {"type": "git", "url": "https://git.drupalcode.org/project/coder.git", "reference": "d51e0b8c6561e21c0545d04b5410a7bed7ee7c6b"}, "require": {"ext-mbstring": "*", "php": ">=7.0.8", "squizlabs/php_codesniffer": "^3.5.5", "symfony/yaml": ">=2.0.5"}, "require-dev": {"phpstan/phpstan": "^0.12.5", "phpunit/phpunit": "^6.0 || ^7.0"}, "type": "phpcodesniffer-standard", "autoload": {"psr-4": {"Drupal\\": "coder_sniffer/<PERSON>up<PERSON>/", "DrupalPractice\\": "coder_sniffer/DrupalPractice/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "description": "Coder is a library to review Drupal code.", "homepage": "https://www.drupal.org/project/coder", "keywords": ["code review", "phpcs", "standards"], "time": "2020-05-08T10:20:59+00:00"}, {"name": "drupal/console", "version": "1.9.4", "source": {"type": "git", "url": "https://github.com/hechoendrupal/drupal-console.git", "reference": "04522b687b2149dc1f808599e716421a20d50a5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hechoendrupal/drupal-console/zipball/04522b687b2149dc1f808599e716421a20d50a5b", "reference": "04522b687b2149dc1f808599e716421a20d50a5b", "shasum": ""}, "require": {"alchemy/zippy": "~0.4", "composer/installers": "~1.0", "doctrine/annotations": "^1.2", "doctrine/collections": "^1.3", "drupal/console-core": "1.9.4", "drupal/console-extend-plugin": "~0", "php": "^5.5.9 || ^7.0", "psy/psysh": "0.6.* || ~0.8", "symfony/css-selector": "~2.8|~3.0", "symfony/dom-crawler": "~2.8|~3.0", "symfony/http-foundation": "~2.8|~3.0"}, "suggest": {"symfony/thanks": "Thank your favorite PHP projects on GitHub using the CLI", "vlucas/phpdotenv": "Loads environment variables from .env to getenv(), $_ENV and $_SERVER automagically"}, "bin": ["bin/drupal"], "type": "library", "autoload": {"psr-4": {"Drupal\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dmouse.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jmolivas.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://enzolutions.com/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Drupal Console Contributors", "homepage": "https://github.com/hechoendrupal/drupal-console/graphs/contributors"}], "description": "The Drupal CLI. A tool to generate boilerplate code, interact with and debug Drupal.", "homepage": "http://drupalconsole.com/", "keywords": ["console", "development", "drupal", "symfony"], "time": "2019-11-11T19:35:01+00:00"}, {"name": "drupal/console-core", "version": "1.9.4", "source": {"type": "git", "url": "https://github.com/hechoendrupal/drupal-console-core.git", "reference": "cc6f50c6ac8199140224347c862df75fd2d2f5ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hechoendrupal/drupal-console-core/zipball/cc6f50c6ac8199140224347c862df75fd2d2f5ed", "reference": "cc6f50c6ac8199140224347c862df75fd2d2f5ed", "shasum": ""}, "require": {"dflydev/dot-access-configuration": "^1.0", "drupal/console-en": "1.9.4", "guzzlehttp/guzzle": "~6.1", "php": "^5.5.9 || ^7.0", "stecman/symfony-console-completion": "~0.7", "symfony/config": "~2.8|~3.0", "symfony/console": "~2.8|~3.0", "symfony/debug": "~2.8|~3.0", "symfony/dependency-injection": "~2.8|~3.0", "symfony/event-dispatcher": "~2.8|~3.0", "symfony/filesystem": "~2.8|~3.0", "symfony/finder": "~2.8|~3.0", "symfony/process": "~2.8|~3.0", "symfony/translation": "~2.8|~3.0", "symfony/yaml": "~2.8|~3.0", "twig/twig": "^1.23.1", "webflo/drupal-finder": "^1.0", "webmozart/path-util": "^2.3"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Drupal\\Console\\Core\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dmouse.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jmolivas.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://enzolutions.com/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Drupal Console Contributors", "homepage": "https://github.com/hechoendrupal/DrupalConsole/graphs/contributors"}], "description": "Drupal Console Core", "homepage": "http://drupalconsole.com/", "keywords": ["console", "development", "drupal", "symfony"], "time": "2019-11-11T19:26:28+00:00"}, {"name": "drupal/console-en", "version": "1.9.4", "source": {"type": "git", "url": "https://github.com/hechoendrupal/drupal-console-en.git", "reference": "30813a832fdb1244e84cbcc012cd103d5e9d673d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hechoendrupal/drupal-console-en/zipball/30813a832fdb1244e84cbcc012cd103d5e9d673d", "reference": "30813a832fdb1244e84cbcc012cd103d5e9d673d", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dmouse.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jmolivas.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://enzolutions.com/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Drupal Console Contributors", "homepage": "https://github.com/hechoendrupal/DrupalConsole/graphs/contributors"}], "description": "<PERSON><PERSON><PERSON> Console English Language", "homepage": "http://drupalconsole.com/", "keywords": ["console", "development", "drupal", "symfony"], "time": "2019-10-07T23:45:30+00:00"}, {"name": "drupal/console-extend-plugin", "version": "0.9.3", "source": {"type": "git", "url": "https://github.com/hechoendrupal/drupal-console-extend-plugin.git", "reference": "ad8e52df34b2e78bdacfffecc9fe8edf41843342"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hechoendrupal/drupal-console-extend-plugin/zipball/ad8e52df34b2e78bdacfffecc9fe8edf41843342", "reference": "ad8e52df34b2e78bdacfffecc9fe8edf41843342", "shasum": ""}, "require": {"composer-plugin-api": "^1.0", "composer/installers": "^1.2", "symfony/finder": "~2.7|~3.0", "symfony/yaml": "~2.7|~3.0"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Console\\Composer\\Plugin\\Extender"}, "autoload": {"psr-4": {"Drupal\\Console\\Composer\\Plugin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON><PERSON>e Extend Plugin", "time": "2019-11-07T20:15:27+00:00"}, {"name": "drupal/core-composer-scaffold", "version": "9.0.1", "source": {"type": "git", "url": "https://github.com/drupal/core-composer-scaffold.git", "reference": "fb112a238577589311395099324ddec7fb4176d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-composer-scaffold/zipball/fb112a238577589311395099324ddec7fb4176d7", "reference": "fb112a238577589311395099324ddec7fb4176d7", "shasum": ""}, "require": {"composer-plugin-api": "^1 || ^2", "php": ">=7.3.0"}, "conflict": {"drupal-composer/drupal-scaffold": "*"}, "require-dev": {"composer/composer": "^1.8@stable"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\Scaffold\\Plugin", "branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\Scaffold\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A flexible Composer project scaffold builder.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "time": "2020-05-29T11:32:13+00:00"}, {"name": "drupal/core-dev", "version": "8.9.1", "source": {"type": "git", "url": "https://github.com/drupal/core-dev.git", "reference": "36370b3f42911c09ffb35f08fc72853d20e6efd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-dev/zipball/36370b3f42911c09ffb35f08fc72853d20e6efd7", "reference": "36370b3f42911c09ffb35f08fc72853d20e6efd7", "shasum": ""}, "require": {"behat/mink": "^1.8", "behat/mink-goutte-driver": "^1.2", "behat/mink-selenium2-driver": "^1.4", "composer/composer": "^1.9.1", "drupal/coder": "^8.3.7", "jcalderonzumba/gastonjs": "^1.0.2", "jcalderonzumba/mink-phantomjs-driver": "^0.3.1", "justinrainbow/json-schema": "^5.2", "mikey179/vfsstream": "^1.6.8", "phpspec/prophecy": "^1.7", "phpunit/phpunit": "^6.5 || ^7", "symfony/browser-kit": "^3.4.0", "symfony/css-selector": "^3.4.0", "symfony/debug": "^3.4.0", "symfony/dom-crawler": "^3.4.0 !=3.4.38", "symfony/filesystem": "~3.4.0", "symfony/finder": "~3.4.0", "symfony/lock": "~3.4.0", "symfony/phpunit-bridge": "^3.4.3"}, "conflict": {"webflo/drupal-core-require-dev": "*"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "require-dev dependencies from drupal/drupal; use in addition to drupal/core-recommended to run tests from drupal/core.", "time": "2020-05-09T07:53:22+00:00"}, {"name": "drupal/core-recommended", "version": "8.9.1", "source": {"type": "git", "url": "https://github.com/drupal/core-recommended.git", "reference": "41042f9eaa35b027e6b2c42fa03edcb85da54a06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-recommended/zipball/41042f9eaa35b027e6b2c42fa03edcb85da54a06", "reference": "41042f9eaa35b027e6b2c42fa03edcb85da54a06", "shasum": ""}, "require": {"asm89/stack-cors": "1.3.0", "composer/semver": "1.5.1", "doctrine/annotations": "v1.4.0", "doctrine/cache": "v1.6.2", "doctrine/collections": "v1.4.0", "doctrine/common": "v2.7.3", "doctrine/inflector": "v1.2.0", "doctrine/lexer": "1.0.2", "drupal/core": "8.9.1", "easyrdf/easyrdf": "0.9.1", "egulias/email-validator": "2.1.17", "guzzlehttp/guzzle": "6.5.4", "guzzlehttp/promises": "v1.3.1", "guzzlehttp/psr7": "1.6.1", "laminas/laminas-diactoros": "1.8.7p2", "laminas/laminas-escaper": "2.6.1", "laminas/laminas-feed": "2.12.2", "laminas/laminas-stdlib": "3.2.1", "laminas/laminas-zendframework-bridge": "1.0.4", "masterminds/html5": "2.3.0", "paragonie/random_compat": "v9.99.99", "pear/archive_tar": "1.4.9", "pear/console_getopt": "v1.4.3", "pear/pear-core-minimal": "v1.10.10", "pear/pear_exception": "v1.0.1", "psr/container": "1.0.0", "psr/http-message": "1.0.1", "psr/log": "1.1.3", "ralouphie/getallheaders": "3.0.3", "stack/builder": "v1.0.5", "symfony-cmf/routing": "1.4.1", "symfony/class-loader": "v3.4.41", "symfony/console": "v3.4.41", "symfony/debug": "v3.4.41", "symfony/dependency-injection": "v3.4.41", "symfony/event-dispatcher": "v3.4.41", "symfony/http-foundation": "v3.4.41", "symfony/http-kernel": "v3.4.41", "symfony/polyfill-ctype": "v1.17.0", "symfony/polyfill-iconv": "v1.17.0", "symfony/polyfill-intl-idn": "v1.17.0", "symfony/polyfill-mbstring": "v1.17.0", "symfony/polyfill-php56": "v1.17.0", "symfony/polyfill-php70": "v1.17.0", "symfony/polyfill-php72": "v1.17.0", "symfony/polyfill-util": "v1.17.0", "symfony/process": "v3.4.41", "symfony/psr-http-message-bridge": "v1.1.2", "symfony/routing": "v3.4.41", "symfony/serializer": "v3.4.41", "symfony/translation": "v3.4.41", "symfony/validator": "v3.4.41", "symfony/yaml": "v3.4.41", "twig/twig": "v1.42.5", "typo3/phar-stream-wrapper": "v3.1.4"}, "conflict": {"webflo/drupal-core-strict": "*"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Locked core dependencies; require this project INSTEAD OF drupal/core.", "time": "2020-06-17T17:57:48+00:00"}, {"name": "drupal/token", "version": "1.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/token.git", "reference": "8.x-1.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/token-8.x-1.7.zip", "reference": "8.x-1.7", "shasum": "c7e3a3757282e4c94e3c1fff08d01e22155cb853"}, "require": {"drupal/core": "^8.8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/53892"}, {"name": "eaton", "homepage": "https://www.drupal.org/user/16496"}, {"name": "fago", "homepage": "https://www.drupal.org/user/16747"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Provides a user interface for the Token API, some missing core tokens.", "homepage": "https://www.drupal.org/project/token", "support": {"source": "https://git.drupalcode.org/project/token"}}, {"name": "drupal/twig_tweak", "version": "2.6.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/twig_tweak.git", "reference": "8.x-2.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/twig_tweak-8.x-2.6.zip", "reference": "8.x-2.6", "shasum": "a877de315bbf8603f146b55e7ca345d8fe70e234"}, "require": {"drupal/core": "^8.7 || ^9.0", "twig/twig": "^1.41 || ^2.12"}, "suggest": {"symfony/var-dumper": "Better dump() function for debugging Twig variables"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/556138"}], "description": "A Twig extension with some useful functions and filters for Drup<PERSON> development.", "homepage": "https://www.drupal.org/project/twig_tweak", "keywords": ["<PERSON><PERSON><PERSON>", "Twig"], "support": {"source": "https://git.drupalcode.org/project/twig_tweak", "issues": "https://www.drupal.org/project/issues/twig_tweak"}}, {"name": "drush/drush", "version": "10.3.1", "source": {"type": "git", "url": "https://github.com/drush-ops/drush.git", "reference": "aad2b17ad34801d9b55cff903e3e7db65d754b80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drush-ops/drush/zipball/aad2b17ad34801d9b55cff903e3e7db65d754b80", "reference": "aad2b17ad34801d9b55cff903e3e7db65d754b80", "shasum": ""}, "require": {"chi-teck/drupal-code-generator": "^1.30.5", "composer/semver": "^1.4", "consolidation/config": "^1.2", "consolidation/filter-via-dot-access-data": "^1", "consolidation/robo": "^1.4.11 || ^2", "consolidation/site-alias": "^3.0.0@stable", "consolidation/site-process": "^2.1 || ^4", "ext-dom": "*", "grasmash/yaml-expander": "^1.1.1", "league/container": "~2", "php": ">=7.1.3", "psr/log": "~1.0", "psy/psysh": "~0.6", "symfony/event-dispatcher": "^3.4 || ^4.0", "symfony/finder": "^3.4 || ^4.0", "symfony/var-dumper": "^3.4 || ^4.0 || ^5.0", "symfony/yaml": "^3.4 || ^4.0", "webflo/drupal-finder": "^1.2", "webmozart/path-util": "^2.1.0"}, "require-dev": {"composer/installers": "^1.7", "cweagans/composer-patches": "~1.0", "david-garcia/phpwhois": "4.3.0", "drupal/alinks": "1.0.0", "drupal/core-recommended": "^8.8", "g1a/composer-test-scenarios": "^3", "lox/xhprof": "dev-master", "phpunit/phpunit": "^4.8.36 || ^6.1", "squizlabs/php_codesniffer": "^2.7 || ^3", "vlucas/phpdotenv": "^2.4"}, "bin": ["drush"], "type": "library", "extra": {"installer-paths": {"sut/core": ["type:drupal-core"], "sut/libraries/{$name}": ["type:drupal-library"], "sut/modules/unish/{$name}": ["drupal/devel"], "sut/themes/unish/{$name}": ["drupal/empty_theme"], "sut/modules/contrib/{$name}": ["type:drupal-module"], "sut/profiles/contrib/{$name}": ["type:drupal-profile"], "sut/themes/contrib/{$name}": ["type:drupal-theme"], "sut/drush/contrib/{$name}": ["type:drupal-drush"]}, "branch-alias": {"dev-master": "10.x-dev"}}, "autoload": {"psr-4": {"Drush\\": "src/", "Drush\\Internal\\": "src/internal-forks"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "j<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Drush is a command line shell and scripting interface for <PERSON><PERSON><PERSON>, a veritable Swiss Army knife designed to make life easier for those of us who spend some of our working hours hacking away at the command prompt.", "homepage": "http://www.drush.org", "time": "2020-06-30T19:43:45+00:00"}, {"name": "fabpot/goutte", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/Goutte.git", "reference": "3f0eaf0a40181359470651f1565b3e07e3dd31b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/Goutte/zipball/3f0eaf0a40181359470651f1565b3e07e3dd31b8", "reference": "3f0eaf0a40181359470651f1565b3e07e3dd31b8", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0", "php": ">=5.5.0", "symfony/browser-kit": "~2.1|~3.0|~4.0", "symfony/css-selector": "~2.1|~3.0|~4.0", "symfony/dom-crawler": "~2.1|~3.0|~4.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.3 || ^4"}, "type": "application", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"psr-4": {"Goutte\\": "<PERSON><PERSON><PERSON>"}, "exclude-from-classmap": ["Goutte/Tests"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A simple PHP Web Scraper", "homepage": "https://github.com/FriendsOfPHP/Goutte", "keywords": ["scraper"], "time": "2018-06-29T15:13:57+00:00"}, {"name": "gitonomy/gitlib", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/gitonomy/gitlib.git", "reference": "718ca021c67f3ea8f6a5fa5d231ec49675068868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gitonomy/gitlib/zipball/718ca021c67f3ea8f6a5fa5d231ec49675068868", "reference": "718ca021c67f3ea8f6a5fa5d231ec49675068868", "shasum": ""}, "require": {"ext-pcre": "*", "php": "^5.6 || ^7.0", "symfony/polyfill-mbstring": "^1.7", "symfony/process": "^3.4|^4.0|^5.0"}, "require-dev": {"phpunit/phpunit": "^5.7|^6.5|^7.0", "psr/log": "^1.0"}, "suggest": {"ext-fileinfo": "Required to determine the mimetype of a blob", "psr/log": "Required to use loggers for reporting of execution"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Gitonomy\\Git\\": "src/Gitonomy/Git/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Library for accessing git", "time": "2020-03-23T12:43:44+00:00"}, {"name": "grasmash/expander", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/grasmash/expander.git", "reference": "95d6037344a4be1dd5f8e0b0b2571a28c397578f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/expander/zipball/95d6037344a4be1dd5f8e0b0b2571a28c397578f", "reference": "95d6037344a4be1dd5f8e0b0b2571a28c397578f", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "php": ">=5.4"}, "require-dev": {"greg-1-anderson/composer-test-scenarios": "^1", "phpunit/phpunit": "^4|^5.5.4", "satooshi/php-coveralls": "^1.0.2|dev-master", "squizlabs/php_codesniffer": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\Expander\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Expands internal property references in PHP arrays file.", "time": "2017-12-21T22:14:55+00:00"}, {"name": "grasmash/yaml-expander", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/grasmash/yaml-expander.git", "reference": "3f0f6001ae707a24f4d9733958d77d92bf9693b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/yaml-expander/zipball/3f0f6001ae707a24f4d9733958d77d92bf9693b1", "reference": "3f0f6001ae707a24f4d9733958d77d92bf9693b1", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "php": ">=5.4", "symfony/yaml": "^2.8.11|^3|^4"}, "require-dev": {"greg-1-anderson/composer-test-scenarios": "^1", "phpunit/phpunit": "^4.8|^5.5.4", "satooshi/php-coveralls": "^1.0.2|dev-master", "squizlabs/php_codesniffer": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\YamlExpander\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Expands internal property references in a yaml file.", "time": "2017-12-16T16:06:03+00:00"}, {"name": "instaclick/php-webdriver", "version": "1.4.7", "source": {"type": "git", "url": "https://github.com/instaclick/php-webdriver.git", "reference": "b5f330e900e9b3edfc18024a5ec8c07136075712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/instaclick/php-webdriver/zipball/b5f330e900e9b3edfc18024a5ec8c07136075712", "reference": "b5f330e900e9b3edfc18024a5ec8c07136075712", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "^4.8", "satooshi/php-coveralls": "^1.0||^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"WebDriver": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Fork Maintainer"}], "description": "PHP WebDriver for Selenium 2", "homepage": "http://instaclick.com/", "keywords": ["browser", "selenium", "webdriver", "webtest"], "time": "2019-09-25T09:05:11+00:00"}, {"name": "jakeasmith/http_build_url", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/jakeasmith/http_build_url.git", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jakeasmith/http_build_url/zipball/93c273e77cb1edead0cf8bcf8cd2003428e74e37", "reference": "93c273e77cb1edead0cf8bcf8cd2003428e74e37", "shasum": ""}, "type": "library", "autoload": {"files": ["src/http_build_url.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality for http_build_url() to environments without pecl_http.", "time": "2017-05-01T15:36:40+00:00"}, {"name": "jcalderonzumba/gastonjs", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/jcalderonzumba/gastonjs.git", "reference": "575a9c18d8b87990c37252e8d9707b29f0a313f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jcalderonzumba/gastonjs/zipball/575a9c18d8b87990c37252e8d9707b29f0a313f3", "reference": "575a9c18d8b87990c37252e8d9707b29f0a313f3", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~5.0|~6.0", "php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "~4.6", "silex/silex": "~1.2", "symfony/phpunit-bridge": "~2.7", "symfony/process": "~2.1"}, "type": "phantomjs-api", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Zumba\\GastonJS\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/jcalderonzumba"}], "description": "PhantomJS API based server for webpage automation", "homepage": "https://github.com/jcalderonzumba/gastonjs", "keywords": ["api", "automation", "browser", "headless", "phantom<PERSON>s"], "time": "2017-03-31T07:31:47+00:00"}, {"name": "jcal<PERSON><PERSON><PERSON>mba/mink-phantomjs-driver", "version": "v0.3.3", "source": {"type": "git", "url": "https://github.com/jcalderonzumba/MinkPhantomJSDriver.git", "reference": "008f43670e94acd39273d15add1e7348eb23848d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jcalderonzumba/MinkPhantomJSDriver/zipball/008f43670e94acd39273d15add1e7348eb23848d", "reference": "008f43670e94acd39273d15add1e7348eb23848d", "shasum": ""}, "require": {"behat/mink": "~1.7", "jcalderonzumba/gastonjs": "~1.0", "php": ">=5.4", "twig/twig": "~1.20|~2.0"}, "require-dev": {"mink/driver-testsuite": "dev-master", "phpunit/phpunit": "~4.6"}, "type": "mink-driver", "extra": {"branch-alias": {"dev-master": "0.4.x-dev"}}, "autoload": {"psr-4": {"Zumba\\Mink\\Driver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/jcalderonzumba"}], "description": "PhantomJS driver for Mink framework", "homepage": "http://mink.behat.org/", "keywords": ["ajax", "browser", "headless", "javascript", "phantom<PERSON>s", "testing"], "time": "2016-12-01T10:57:30+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.10", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b", "reference": "2ba9c8c862ecd5510ed16c6340aa9f6eadb4f31b", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2020-05-27T16:41:55+00:00"}, {"name": "league/container", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/thephpleague/container.git", "reference": "43f35abd03a12977a60ffd7095efd6a7808488c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/container/zipball/43f35abd03a12977a60ffd7095efd6a7808488c0", "reference": "43f35abd03a12977a60ffd7095efd6a7808488c0", "shasum": ""}, "require": {"container-interop/container-interop": "^1.2", "php": "^5.4.0 || ^7.0"}, "provide": {"container-interop/container-interop-implementation": "^1.2", "psr/container-implementation": "^1.0"}, "replace": {"orno/di": "~2.0"}, "require-dev": {"phpunit/phpunit": "4.*"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev", "dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Container\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.philipobenito.com", "role": "Developer"}], "description": "A fast and intuitive dependency injection container.", "homepage": "https://github.com/thephpleague/container", "keywords": ["container", "dependency", "di", "injection", "league", "provider", "service"], "time": "2017-05-10T09:20:27+00:00"}, {"name": "mikey179/vfsstream", "version": "v1.6.8", "source": {"type": "git", "url": "https://github.com/bovigo/vfsStream.git", "reference": "231c73783ebb7dd9ec77916c10037eff5a2b6efe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bovigo/vfsStream/zipball/231c73783ebb7dd9ec77916c10037eff5a2b6efe", "reference": "231c73783ebb7dd9ec77916c10037eff5a2b6efe", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.5|^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"org\\bovigo\\vfs\\": "src/main/php"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://frankkleine.de/", "role": "Developer"}], "description": "Virtual file system to mock the real file system in unit tests.", "homepage": "http://vfs.bovigo.org/", "time": "2019-10-30T15:31:00+00:00"}, {"name": "monolog/monolog", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "38914429aac460e8e4616c8cb486ecb40ec90bb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/38914429aac460e8e4616c8cb486ecb40ec90bb1", "reference": "38914429aac460e8e4616c8cb486ecb40ec90bb1", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^6.0", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "php-parallel-lint/php-parallel-lint": "^1.0", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2020-05-22T08:12:19+00:00"}, {"name": "myclabs/deep-copy", "version": "1.10.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "969b211f9a51aa1f6c01d1d2aef56d3bd91598e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/969b211f9a51aa1f6c01d1d2aef56d3bd91598e5", "reference": "969b211f9a51aa1f6c01d1d2aef56d3bd91598e5", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "time": "2020-06-29T13:22:24+00:00"}, {"name": "nikic/php-parser", "version": "v4.6.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "c346bbfafe2ff60680258b631afb730d186ed864"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/c346bbfafe2ff60680258b631afb730d186ed864", "reference": "c346bbfafe2ff60680258b631afb730d186ed864", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "0.0.5", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.3-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2020-07-02T17:12:47+00:00"}, {"name": "nuvoleweb/robo-config", "version": "0.2.2", "source": {"type": "git", "url": "https://github.com/nuvoleweb/robo-config.git", "reference": "ce4c3285f2cbc692d96f5f2d57eb2c5068aeadd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nuvoleweb/robo-config/zipball/ce4c3285f2cbc692d96f5f2d57eb2c5068aeadd2", "reference": "ce4c3285f2cbc692d96f5f2d57eb2c5068aeadd2", "shasum": ""}, "require": {"consolidation/robo": "~1"}, "require-dev": {"drupal/coder": "^8.2", "phpro/grumphp": "^0.11.4", "phpunit/phpunit": "^5"}, "type": "robo-tasks", "autoload": {"psr-4": {"NuvoleWeb\\Robo\\Task\\Config\\": "src", "NuvoleWeb\\Robo\\Tests\\": "tests"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "CI/developer-friendly configuration processor for Robo.", "time": "2018-08-22T06:38:44+00:00"}, {"name": "openeuropa/task-runner", "version": "1.0.0-beta6", "source": {"type": "git", "url": "https://github.com/openeuropa/task-runner.git", "reference": "0ee1c8f526bb3fc43bb6e6d209a7d9cf10acd270"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/openeuropa/task-runner/zipball/0ee1c8f526bb3fc43bb6e6d209a7d9cf10acd270", "reference": "0ee1c8f526bb3fc43bb6e6d209a7d9cf10acd270", "shasum": ""}, "require": {"consolidation/robo": "^1.4", "gitonomy/gitlib": "^1.0", "jakeasmith/http_build_url": "^1.0.1", "nuvoleweb/robo-config": "^0.2.1"}, "require-dev": {"openeuropa/code-review": "~1.0.0-beta3", "phpunit/phpunit": "~5.5||~6.0"}, "bin": ["bin/run"], "type": "library", "extra": {"enable-patching": true, "composer-exit-on-patch-failure": true}, "autoload": {"psr-4": {"OpenEuropa\\TaskRunner\\": "./src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["EUPL-1.2"], "description": "PHP task runner based on Robo, focused on extensibility.", "keywords": ["automation", "robo", "task-runner", "yaml"], "time": "2019-06-27T09:46:47+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "time": "2018-07-08T19:19:57+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e", "shasum": ""}, "require": {"ext-filter": "^7.1", "php": "^7.2", "phpdocumentor/reflection-common": "^2.0", "phpdocumentor/type-resolver": "^1.0", "webmozart/assert": "^1"}, "require-dev": {"doctrine/instantiator": "^1", "mockery/mockery": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "time": "2020-02-22T12:28:44+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "e878a14a65245fbe78f8080eba03b47c3b705651"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/e878a14a65245fbe78f8080eba03b47c3b705651", "reference": "e878a14a65245fbe78f8080eba03b47c3b705651", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "time": "2020-06-27T10:12:23+00:00"}, {"name": "phpro/grumphp", "version": "v0.18.1", "source": {"type": "git", "url": "https://github.com/phpro/grumphp.git", "reference": "d07e59ebfdd48cf41d12b2af3670abcd1a2b01ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpro/grumphp/zipball/d07e59ebfdd48cf41d12b2af3670abcd1a2b01ef", "reference": "d07e59ebfdd48cf41d12b2af3670abcd1a2b01ef", "shasum": ""}, "require": {"composer-plugin-api": "~1.0 || ~2.0", "doctrine/collections": "~1.2", "ext-json": "*", "gitonomy/gitlib": "^1.0.3", "monolog/monolog": "~1.16 || ^2.0", "php": "^7.2", "seld/jsonlint": "~1.1", "symfony/config": "~3.4 || ~4.0 || ~5.0", "symfony/console": "~3.4 || ~4.0 || ~5.0", "symfony/dependency-injection": "~3.4 || ~4.0 || ~5.0", "symfony/event-dispatcher": "~3.4 || ~4.0 || ~5.0", "symfony/filesystem": "~3.4 || ~4.0 || ~5.0", "symfony/finder": "~3.4 || ~4.0 || ~5.0", "symfony/options-resolver": "~3.4 || ~4.0 || ~5.0", "symfony/process": "~3.4 || ~4.0 || ~5.0", "symfony/yaml": "~3.4 || ~4.0 || ~5.0"}, "require-dev": {"brianium/paratest": "~3.1 || dev-master", "composer/composer": "~1.9 || ^2.0@dev", "ergebnis/composer-normalize": "~2.1", "jakub-onderka/php-parallel-lint": "~1.0", "nikic/php-parser": "~3.1", "phpspec/phpspec": "~6.1", "phpunit/phpunit": "^7.5.17", "sensiolabs/security-checker": "~6.0", "squizlabs/php_codesniffer": "~3.5"}, "suggest": {"atoum/atoum": "Lets GrumPHP run your unit tests.", "behat/behat": "Lets GrumPHP validate your project features.", "brianium/paratest": "Lets GrumPHP run PHPUnit in parallel.", "codeception/codeception": "Lets GrumPHP run your project's full stack tests", "codegyre/robo": "Lets GrumPHP run your automated PHP tasks.", "designsecurity/progpilot": "Lets GrumPHP be sure that there are no vulnerabilities in your code.", "doctrine/orm": "Lets GrumPHP validate your Doctrine mapping files.", "ergebnis/composer-normalize": "Lets GrumPHP tidy and normalize your composer.json file.", "friendsofphp/php-cs-fixer": "Lets GrumPHP automatically fix your codestyle.", "friendsoftwig/twigcs": "Lets GrumPHP check Twig coding standard.", "infection/infection": "Lets GrumPHP evaluate the quality your unit tests", "jakub-onderka/php-parallel-lint": "Lets GrumPHP quickly lint your entire code base.", "maglnet/composer-require-checker": "Lets GrumPHP analyze composer dependencies.", "malukenho/kawaii-gherkin": "Lets GrumPHP lint your Gherkin files.", "nikic/php-parser": "Lets GrumPHP run static analyses through your PHP files.", "phan/phan": "Lets GrumPHP unleash a static analyzer on your code", "phing/phing": "Lets GrumPHP run your automated PHP tasks.", "phpmd/phpmd": "Lets GrumPHP sort out the mess in your code", "phpspec/phpspec": "Lets GrumPHP spec your code.", "phpstan/phpstan": "Lets GrumPHP discover bugs in your code without running it.", "phpunit/phpunit": "Lets GrumPHP run your unit tests.", "povils/phpmnd": "Lets GrumPHP help you detect magic numbers in PHP code.", "roave/security-advisories": "Lets GrumPHP be sure that there are no known security issues.", "sebastian/phpcpd": "Lets GrumPHP find duplicated code.", "sensiolabs/security-checker": "Lets GrumPHP be sure that there are no known security issues.", "squizlabs/php_codesniffer": "Lets GrumPHP sniff on your code.", "sstalle/php7cc": "Lets GrumPHP check PHP 5.3 - 5.6 code compatibility with PHP 7.", "symfony/phpunit-bridge": "Lets GrumPHP run your unit tests with the phpunit-bridge of Symfony.", "symplify/easycodingstandard": "Lets GrumPHP check coding standard.", "vimeo/psalm": "Lets GrumPHP discover errors in your code without running it."}, "bin": ["bin/grumphp"], "type": "composer-plugin", "extra": {"class": "GrumPHP\\Composer\\GrumPHPPlugin"}, "autoload": {"psr-4": {"GrumPHP\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Toon Verwerft", "email": "<EMAIL>"}, {"name": "Community", "homepage": "https://github.com/phpro/grumphp/graphs/contributors"}], "description": "A composer plugin that enables source code quality checks.", "time": "2020-05-27T04:48:38+00:00"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/451c3cd1418cf640de218914901e51b064abb093", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2020-03-05T15:02:03+00:00"}, {"name": "phpunit/php-code-coverage", "version": "6.1.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.1", "phpunit/php-file-iterator": "^2.0", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1 || ^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-xdebug": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2018-10-31T16:06:48+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "050bedf145a257b1ff02746c31894800e5122946"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/050bedf145a257b1ff02746c31894800e5122946", "reference": "050bedf145a257b1ff02746c31894800e5122946", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2018-09-13T20:33:42+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/php-timer/zipball/1038454804406b0b5f5f520358e78c1c2f71501e", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2019-06-07T04:22:29+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-token-stream/zipball/995192df77f63a59e47f025390d2d1fdf8f425ff", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2019-09-17T06:23:10+00:00"}, {"name": "phpunit/phpunit", "version": "7.5.20", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/9467db479d1b0487c99733bb1e7944d32deded2c", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c", "shasum": ""}, "require": {"doctrine/instantiator": "^1.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.7", "phar-io/manifest": "^1.0.2", "phar-io/version": "^2.0", "php": "^7.1", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^6.0.7", "phpunit/php-file-iterator": "^2.0.1", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0", "sebastian/environment": "^4.0", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0", "sebastian/version": "^2.0.1"}, "conflict": {"phpunit/phpunit-mock-objects": "*"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2020-01-08T08:45:45+00:00"}, {"name": "psy/psysh", "version": "v0.10.4", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "a8aec1b2981ab66882a01cce36a49b6317dc3560"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/a8aec1b2981ab66882a01cce36a49b6317dc3560", "reference": "a8aec1b2981ab66882a01cce36a49b6317dc3560", "shasum": ""}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3", "php": "^8.0 || ^7.0 || ^5.5.9", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "3.17.*"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "time": "2020-05-03T19:32:03+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "shasum": ""}, "require": {"php": "^7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-07-12T15:12:46+00:00"}, {"name": "sebastian/diff", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "time": "2019-02-04T06:01:07+00:00"}, {"name": "sebastian/environment", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2019-11-20T08:46:58+00:00"}, {"name": "sebastian/exporter", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/68609e1261d215ea5b21b7987539cbfbe156ec3e", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e", "shasum": ""}, "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2019-09-14T09:02:43+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5", "shasum": ""}, "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "time": "2017-08-03T12:35:26+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "reference": "773f97c67f28de00d397be301821b06708fca0be", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "time": "2017-03-29T09:07:27+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2017-03-03T06:23:57+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "shasum": ""}, "require": {"php": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2018-10-04T04:07:39+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "seld/jsonlint", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1", "reference": "ff2aa5420bfbc296cf6a0bc785fa5b35736de7c1", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2020-04-30T19:05:18+00:00"}, {"name": "seld/phar-utils", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "8800503d56b9867d43d9c303b9cbcc26016e82f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/8800503d56b9867d43d9c303b9cbcc26016e82f0", "reference": "8800503d56b9867d43d9c303b9cbcc26016e82f0", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "time": "2020-02-14T15:25:33+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.5.5", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "73e2e7f57d958e7228fce50dc0c61f58f017f9f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/73e2e7f57d958e7228fce50dc0c61f58f017f9f6", "reference": "73e2e7f57d958e7228fce50dc0c61f58f017f9f6", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards"], "time": "2020-04-17T01:09:41+00:00"}, {"name": "stecman/symfony-console-completion", "version": "0.11.0", "source": {"type": "git", "url": "https://github.com/stecman/symfony-console-completion.git", "reference": "a9502dab59405e275a9f264536c4e1cb61fc3518"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stecman/symfony-console-completion/zipball/a9502dab59405e275a9f264536c4e1cb61fc3518", "reference": "a9502dab59405e275a9f264536c4e1cb61fc3518", "shasum": ""}, "require": {"php": ">=5.3.2", "symfony/console": "~2.3 || ~3.0 || ~4.0 || ~5.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36 || ~5.7 || ~6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Stecman\\Component\\Symfony\\Console\\BashCompletion\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Automatic BASH completion for Symfony Console Component based applications.", "time": "2019-11-24T17:03:06+00:00"}, {"name": "symfony/browser-kit", "version": "v3.4.42", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "1467e0c7cf0c5c2c08dc9b45ca0300ac3cd3a824"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/1467e0c7cf0c5c2c08dc9b45ca0300ac3cd3a824", "reference": "1467e0c7cf0c5c2c08dc9b45ca0300ac3cd3a824", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/dom-crawler": "~2.8|~3.0|~4.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0|~4.0", "symfony/process": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony BrowserKit Component", "homepage": "https://symfony.com", "time": "2020-04-27T06:55:12+00:00"}, {"name": "symfony/config", "version": "v3.4.42", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "cd61db31cbd19cbe4ba9f6968f13c9076e1372ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/cd61db31cbd19cbe4ba9f6968f13c9076e1372ab", "reference": "cd61db31cbd19cbe4ba9f6968f13c9076e1372ab", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/filesystem": "~2.8|~3.0|~4.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/dependency-injection": "<3.3", "symfony/finder": "<3.3"}, "require-dev": {"symfony/dependency-injection": "~3.3|~4.0", "symfony/event-dispatcher": "~3.3|~4.0", "symfony/finder": "~3.3|~4.0", "symfony/yaml": "~3.0|~4.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2020-05-22T10:56:48+00:00"}, {"name": "symfony/css-selector", "version": "v3.4.42", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "9ccf6e78077a3fc1596e6c7b5958008965a11518"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/9ccf6e78077a3fc1596e6c7b5958008965a11518", "reference": "9ccf6e78077a3fc1596e6c7b5958008965a11518", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony CssSelector Component", "homepage": "https://symfony.com", "time": "2020-03-16T08:31:04+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337", "reference": "dd99cb3a0aff6cadd2a8d7d7ed72c2161e218337", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "time": "2020-05-27T08:34:37+00:00"}, {"name": "symfony/dom-crawler", "version": "v3.4.42", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "c3086a58a66b2a519c0b7ac80539a3727609ea9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/c3086a58a66b2a519c0b7ac80539a3727609ea9c", "reference": "c3086a58a66b2a519c0b7ac80539a3727609ea9c", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "~2.8|~3.0|~4.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony DomCrawler Component", "homepage": "https://symfony.com", "time": "2020-05-22T19:35:43+00:00"}, {"name": "symfony/filesystem", "version": "v3.4.42", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "0f625d0cb1e59c8c4ba61abb170125175218ff10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/0f625d0cb1e59c8c4ba61abb170125175218ff10", "reference": "0f625d0cb1e59c8c4ba61abb170125175218ff10", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "symfony/polyfill-ctype": "~1.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2020-05-30T17:48:24+00:00"}, {"name": "symfony/finder", "version": "v3.4.42", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "5ec813ccafa8164ef21757e8c725d3a57da59200"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/5ec813ccafa8164ef21757e8c725d3a57da59200", "reference": "5ec813ccafa8164ef21757e8c725d3a57da59200", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2020-02-14T07:34:21+00:00"}, {"name": "symfony/lock", "version": "v3.4.42", "source": {"type": "git", "url": "https://github.com/symfony/lock.git", "reference": "c5374725a61b25cd24ec1615b0707a2aa0cefe5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/lock/zipball/c5374725a61b25cd24ec1615b0707a2aa0cefe5a", "reference": "c5374725a61b25cd24ec1615b0707a2aa0cefe5a", "shasum": ""}, "require": {"php": "^5.5.9|>=7.0.8", "psr/log": "~1.0", "symfony/polyfill-php70": "~1.0"}, "require-dev": {"predis/predis": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Lock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Lock Component", "homepage": "https://symfony.com", "keywords": ["cas", "flock", "locking", "mutex", "redlock", "semaphore"], "time": "2020-05-08T10:38:31+00:00"}, {"name": "symfony/options-resolver", "version": "v5.1.2", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "663f5dd5e14057d1954fe721f9709d35837f2447"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/663f5dd5e14057d1954fe721f9709d35837f2447", "reference": "663f5dd5e14057d1954fe721f9709d35837f2447", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-php80": "^1.15"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony OptionsResolver Component", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "time": "2020-05-23T13:08:13+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v3.4.42", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "ac355e1e9ebde4cf6ef5187f5cf4b43001f9a29f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/ac355e1e9ebde4cf6ef5187f5cf4b43001f9a29f", "reference": "ac355e1e9ebde4cf6ef5187f5cf4b43001f9a29f", "shasum": ""}, "require": {"php": ">=5.3.3"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0|<6.4,>=6.0|9.1.2"}, "suggest": {"symfony/debug": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "3.4-dev"}, "thanks": {"name": "phpunit/phpunit", "url": "https://github.com/sebastian<PERSON>mann/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PHPUnit Bridge", "homepage": "https://symfony.com", "time": "2020-06-04T15:36:03+00:00"}, {"name": "theseer/tokenizer", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/11336f6f84e16a720dae9d8e6ed5019efa85a0f9", "reference": "11336f6f84e16a720dae9d8e6ed5019efa85a0f9", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "time": "2019-06-13T22:48:21+00:00"}, {"name": "webflo/drupal-finder", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/webflo/drupal-finder.git", "reference": "123e248e14ee8dd3fbe89fb5a733a6cf91f5820e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webflo/drupal-finder/zipball/123e248e14ee8dd3fbe89fb5a733a6cf91f5820e", "reference": "123e248e14ee8dd3fbe89fb5a733a6cf91f5820e", "shasum": ""}, "require": {"ext-json": "*"}, "require-dev": {"mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^4.8"}, "type": "library", "autoload": {"classmap": ["src/DrupalFinder.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Helper class to locate a Drupal installation from a given path.", "time": "2019-08-02T08:06:18+00:00"}, {"name": "webmozart/assert", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/webmozart/assert.git", "reference": "9dc4f203e36f2b486149058bade43c851dd97451"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/assert/zipball/9dc4f203e36f2b486149058bade43c851dd97451", "reference": "9dc4f203e36f2b486149058bade43c851dd97451", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "time": "2020-06-16T10:16:42+00:00"}, {"name": "webmozart/path-util", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/webmozart/path-util.git", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/path-util/zipball/d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "shasum": ""}, "require": {"php": ">=5.3.3", "webmozart/assert": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\PathUtil\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A robust cross-platform utility for normalizing, comparing and modifying file paths.", "time": "2015-12-17T08:42:14+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": [], "prefer-stable": true, "prefer-lowest": false, "platform": [], "platform-dev": []}