<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="${drupal.root}/core/tests/bootstrap.php" backupGlobals="true" colors="true" >
  <php>
    <ini name="error_reporting" value="32767"/>
    <ini name="memory_limit" value="-1"/>
    <env name="SIMPLETEST_BASE_URL" value="${drupal.base_url}"/>
    <env name="SIMPLETEST_IGNORE_DIRECTORIES" value="${drupal.root}"/>
    <env name="SIMPLETEST_DB" value="mysql://${drupal.database.user}:${drupal.database.password}@${drupal.database.host}:${drupal.database.port}/${drupal.database.name}"/>
    <env name="MINK_DRIVER_ARGS_WEBDRIVER" value='["${selenium.browser}", null, "${selenium.host}/wd/hub"]'/>
  </php>
  <testsuites>
    <testsuite>
      <directory>./tests/</directory>
      <directory>./modules/*/tests/</directory>
    </testsuite>
  </testsuites>
</phpunit>
