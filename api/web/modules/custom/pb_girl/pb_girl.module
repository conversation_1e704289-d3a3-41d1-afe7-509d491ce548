<?php

/**
 * @file
 * Contains pb_girl.module.
 */

/**
 * Returns the image url from the image element.
 *
 * The image_copy plugin needs the image url
 * and it seems not to be possible to access sub items
 * via xpath. The method is a workaround for this.
 *
 * @see migrate_plus.migration.pb_girls.yml
 *
 * @param array $item
 *   The image item from the json endpoint.
 *
 * @return string
 *   The image url.
 */
function pb_girl_image_url_migrate_callback($item) {
  return $item['url'];
}

/**
 * Implements hook_theme().
 */
function pb_girl_theme($existing, $type, $theme, $path) {
  return [
    'girl' => [
      'render element' => 'elements',
    ],
  ];
}

/**
 * Implements hook_theme_suggestions_HOOK().
 */
function pb_girl_theme_suggestions_girl(array $variables) {
  $suggestions = [];
  $sanitized_view_mode = strtr($variables['elements']['#view_mode'], '.', '_');

  $suggestions[] = 'girl';
  $suggestions[] = 'girl__' . $sanitized_view_mode;

  return $suggestions;
}
