drupal:
  root: "build"
  base_url: "http://pb-girl.docksal"
  database:
    host: "db"
    port: "3306"
    name: "default"
    user: "root"
    password: "root"
  post_install:
    - "./vendor/bin/drush en -y pb_girl pb_girl_vote migrate_plus migrate_tools"
    - "./vendor/bin/drush cr"
  settings:
    config_directories:
      sync: "../config_sync"
    settings:
      container_yamls:
        - "/var/www/${drupal.root}/sites/development.services.yml"
      extension_discovery_scan_tests: true
      skip_permissions_hardening: true
      cache:
        bins:
          dynamic_page_cache: "cache.backend.null"
          page_cache: "cache.backend.null"
          render: "cache.backend.null"
      file_scan_ignore_directories:
        - "vendor"
        - "${drupal.root}"

selenium:
  host: "http://selenium:4444"
  browser: "chrome"

commands:
  drupal:site-setup:
    - { task: "symlink", from: "../../../../modules", to: "${drupal.root}/modules/custom/pb_girl/modules" }
    - { task: "symlink", from: "../../../../src", to: "${drupal.root}/modules/custom/pb_girl/src" }
    - { task: "symlink", from: "../../../../config", to: "${drupal.root}/modules/custom/pb_girl/config" }
    - { task: "symlink", from: "../../../../templates", to: "${drupal.root}/modules/custom/pb_girl/templates" }
    - { task: "symlink", from: "../../../../tests", to: "${drupal.root}/modules/custom/pb_girl/tests" }
    - { task: "symlink", from: "../../../../pb_girl.links.action.yml", to: "${drupal.root}/modules/custom/pb_girl/pb_girl.links.action.yml" }
    - { task: "symlink", from: "../../../../pb_girl.links.menu.yml", to: "${drupal.root}/modules/custom/pb_girl/pb_girl.links.menu.yml" }
    - { task: "symlink", from: "../../../../pb_girl.links.task.yml", to: "${drupal.root}/modules/custom/pb_girl/pb_girl.links.task.yml" }
    - { task: "symlink", from: "../../../../pb_girl.info.yml", to: "${drupal.root}/modules/custom/pb_girl/pb_girl.info.yml" }
    - { task: "symlink", from: "../../../../pb_girl.module", to: "${drupal.root}/modules/custom/pb_girl/pb_girl.module" }
    - { task: "symlink", from: "../../../../pb_girl.services.yml", to: "${drupal.root}/modules/custom/pb_girl/pb_girl.services.yml" }
    # Generate settings.testing.php, it will be used when running functional tests.
    - { task: "process-php", type: "write", config: "drupal.settings", source: "${drupal.root}/sites/default/default.settings.php", destination: "${drupal.root}/sites/default/settings.testing.php", override: true }
    - { task: "run", command: "drupal:drush-setup" }
    - { task: "run", command: "drupal:settings-setup" }
    - { task: "run", command: "setup:phpunit" }
  setup:phpunit:
    - { task: "process", source: "phpunit.xml.dist", destination: "phpunit.xml" }
