uuid: 9dc1461e-87d8-4a97-a8dc-353d37447a39
langcode: en
status: true
dependencies: {  }
_core:
  default_config_hash: oCHz7TjO3DEUU1aB_Z7xZ0ISTWgAM_4etSElmaxvnF8
id: pb_girls
class: null
field_plugin_method: null
cck_plugin_method: null
migration_tags: null
migration_group: pb_girl
label: 'Import girl entities'
source:
  plugin: url
  data_fetcher_plugin: http
  data_parser_plugin: json
  constants:
    file_destination: 'public://girls/'
  authentication:
    plugin: basic
    username: playboyexchange
    password: vUzgwXb4TfNeCbr2aV3U
  urls:
    - 'https://premium.playboy.de/exchange/gotd.json'
  item_selector: /
  fields:
    -
      name: girl_id
      label: 'Primary girl id'
      selector: girlinfo/id
    -
      name: title
      label: Title
      selector: girlinfo/title
    -
      name: firstname
      label: firstname
      selector: girlinfo/firstname
    -
      name: middlename
      label: middlename
      selector: girlinfo/middlename
    -
      name: lastname
      label: lastname
      selector: girlinfo/lastname
    -
      name: bustsize
      label: bustsize
      selector: girlinfo/bustsize
    -
      name: waistsize
      label: waistsize
      selector: girlinfo/waistsize
    -
      name: hipsize
      label: hipsize
      selector: girlinfo/hipsize
    -
      name: cupsize
      label: cupsize
      selector: girlinfo/cupsize
    -
      name: height
      label: height
      selector: girlinfo/height
    -
      name: weight
      label: weight
      selector: girlinfo/weight
    -
      name: haircolor
      label: haircolor
      selector: girlinfo/haircolor
    -
      name: eyecolor
      label: eyecolor
      selector: girlinfo/eyecolor
    -
      name: birthday
      label: birthday
      selector: girlinfo/birthday
    -
      name: hometown
      label: hometown
      selector: girlinfo/hometown
    -
      name: description
      label: description
      selector: girlinfo/description
    -
      name: images
      label: Images
      selector: images
  ids:
    girl_id:
      type: string
process:
  id: girl_id
  name: 
    plugin: concat
    source:
      - firstname
      - lastname
    delimiter: ' '
  firstname: firstname
  middlename: middlename
  lastname: lastname
  bustsize: bustsize
  waistsize: waistsize
  cupsize: cupsize
  height: height
  weight: weight
  haircolor: haircolor
  eyecolor: eyecolor
  hipsize: hipsize
  birthday: birthday
  hometown: hometown
  description: description
  images:
    -
      plugin: callback
      callable: pb_girl_image_url_migrate_callback
      source: images
    -
      plugin: image_import
      destination: constants/file_destination
      alt: '!title'
destination:
  plugin: 'entity:girl'
migration_dependencies: {  }
