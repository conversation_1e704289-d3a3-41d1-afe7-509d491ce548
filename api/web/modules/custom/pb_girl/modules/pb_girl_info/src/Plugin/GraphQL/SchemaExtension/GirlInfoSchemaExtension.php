<?php

namespace Drupal\pb_girl_info\Plugin\GraphQL\SchemaExtension;

use <PERSON><PERSON><PERSON>\graphql\GraphQL\ResolverBuilder;
use <PERSON><PERSON>al\graphql\GraphQL\ResolverRegistry;
use Drupal\graphql\GraphQL\ResolverRegistryInterface;
use Drupal\graphql\Plugin\GraphQL\SchemaExtension\SdlSchemaExtensionPluginBase;
// use Drupal\graphql_examples\Wrappers\QueryConnection;

/**
 * @SchemaExtension(
 *   id = "girl_info",
 *   name = "Girl info",
 *   description = "Schema extension for girl info entities and their media.",
 *   schema = "girl_info_schema"
 * )
 */
class GirlInfoSchemaExtension extends SchemaFieldResolverExtensionBase
{

  /**
   * {@inheritdoc}
   */
  public function registerResolvers(ResolverRegistryInterface $registry): void
  {
    $builder = new ResolverBuilder();

    $this->addQueryFields($registry, $builder);
    $this->addGirlInfoFields($registry, $builder);
    $this->addImageFields($registry, $builder);
    $this->addGalleryFields($registry, $builder);

    $this->resolveFields('Girl', $registry, $builder);
    $this->resolveFields('TagTerm', $registry, $builder);


    $this->addConnectionFields('GirlList', $registry, $builder);
    $this->addConnectionFields('GirlInfoList', $registry, $builder);
  }

  /**
   * Adds query fields.
   *
   * @param \Drupal\graphql\GraphQL\ResolverRegistry $registry
   * @param \Drupal\graphql\GraphQL\ResolverBuilder $builder
   */
  protected function addQueryFields(ResolverRegistry $registry, ResolverBuilder $builder): void
  {
    $registry->addFieldResolver(
      'Query',
      'girlInfo',
      $builder->produce('entity_load')
        ->map('type', $builder->fromValue('girl_info'))
        ->map('id', $builder->fromArgument('id'))
    );

    $registry->addFieldResolver(
      'Query',
      'girlInfoList',
      $builder->produce('entity_list_query')
        ->map('offset', $builder->fromArgument('offset'))
        ->map('limit', $builder->fromArgument('limit'))
        ->map('entity_type', $builder->fromValue('girl_info'))
    );

    $registry->addFieldResolver(
      'Query',
      'girl',
      $builder->produce('entity_load')
        ->map('type', $builder->fromValue('girl'))
        ->map('id', $builder->fromArgument('id'))
    );

    $registry->addFieldResolver(
      'Query',
      'girlList',
      $builder->produce('entity_list_query')
        ->map('offset', $builder->fromArgument('offset'))
        ->map('limit', $builder->fromArgument('limit'))
        ->map('entity_type', $builder->fromValue('girl'))
    );

    $registry->addFieldResolver(
      'Query',
      'gallery',
      $builder->produce('entity_load')
        ->map('type', $builder->fromValue('media'))
        ->map('id', $builder->fromArgument('id'))
        ->map('bundles', $builder->fromValue(['gallery']))
    );
  }

  /**
   * Adds girl info entity fields.
   *
   * @param \Drupal\graphql\GraphQL\ResolverRegistryInterface $registry
   * @param \Drupal\graphql\GraphQL\ResolverBuilder $builder
   */
  protected function addGirlInfoFields(ResolverRegistryInterface $registry, ResolverBuilder $builder): void
  {
    $this->resolveFields('GirlInfo', $registry, $builder, [
      self::RESOLVE_FIELDS_MULTI_VALUE => [
        'descriptors',
      ],
      self::RESOLVE_FIELDS_SINGLE_VALUE => [
        'firstname',
        'middlename',
        'lastname',
        'bustsize',
        'waistsize',
        'hipsize',
        'cupsize',
        'height',
        'weight',
        'haircolor',
        'eyecolor',
        'hometown',
        'city',
        'province',
        'country',
        'description',
        'description_original',
        'birthday',
        'release',
      ],
      self::RESOLVE_FIELDS_MULTI_REFERENCE => [
        'main_images',
        'non_nude_images',
        'tags',
        'galleries',
      ],
      self::RESOLVE_FIELDS_SINGLE_REFERENCE => [
        'girl',
      ]
    ], 'girl_info');
  }

  /**
   * Adds media image fields.
   *
   * @param \Drupal\graphql\GraphQL\ResolverRegistryInterface $registry
   * @param \Drupal\graphql\GraphQL\ResolverBuilder $builder
   */
  protected function addImageFields(ResolverRegistryInterface $registry, ResolverBuilder $builder)
  {
    $this->resolveFields('Image', $registry, $builder, [
      self::RESOLVE_FIELDS_SINGLE_VALUE => [
        'field_description',
        'field_fsk',
        'field_credit',
      ],
    ], 'media');
  }

  /**
   * Adds media gallery fields.
   *
   * @param \Drupal\graphql\GraphQL\ResolverRegistryInterface $registry
   * @param \Drupal\graphql\GraphQL\ResolverBuilder $builder
   */
  protected function addGalleryFields(ResolverRegistryInterface $registry, ResolverBuilder $builder)
  {
    $this->resolveFields('Gallery', $registry, $builder, [
      self::RESOLVE_FIELDS_SINGLE_VALUE => [
        'field_credit'
      ],
      self::RESOLVE_FIELDS_MULTI_REFERENCE => [
        'field_tags',
        'field_media_slideshow'
      ],
    ], 'media');
  }
}