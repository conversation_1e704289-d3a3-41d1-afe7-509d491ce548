<?php

namespace Drupal\pb_girl_info\Plugin\Field\FieldType;

use Drupal\Core\Field\FieldItemList;
use Drupal\Core\TypedData\ComputedItemListTrait;
use Drupal\pb_girl_info\GirlInfoCustomAccessRules;

/**
 * Full name computed field class.
 */
class AccessComputedField extends FieldItemList
{
  use ComputedItemListTrait;
  /**
   * Computes the values for an item list.
   */
  protected function computeValue()
  {
    $customAccessRules = new GirlInfoCustomAccessRules();
    $entity = $this->getEntity();
    $item = $this->createItem(0, $customAccessRules->checkAccess($entity));
    $this->list[0] = $item;
  }
}
