<?php

namespace Drupal\pb_girl_info;

use <PERSON><PERSON>al\Core\Entity\EntityAccessControlHandler;
use <PERSON>upal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\Access\AccessResult;

/**
 * Access controller for the Girl Info entity.
 *
 * @see \Drupal\pb_girl_info\Entity\GirlInfo.
 */
class GirlInfoAccessControlHandler extends EntityAccessControlHandler
{

  /**
   * {@inheritdoc}
   */
  protected function checkAccess(EntityInterface $entity, $operation, AccountInterface $account)
  {
    /** @var \Drupal\pb_girl\Entity\GirlInterface $entity */

    switch ($operation) {

      case 'view':

        if (!$entity->isPublished()) {
          return AccessResult::allowedIfHasPermission($account, 'view unpublished girl info entities');
        }

        // check if the publish date it withing the last three months
        $publish_date = $entity->get('publish_date')->value;
        if (strtotime($publish_date) > strtotime('-3 months')) {
          // return AccessResult::allowedIfHasPermission($account, 'view published plus girl info entities');
        }

        return AccessResult::allowedIfHasPermission($account, 'view published girl info entities');

      case 'update':

        return AccessResult::allowedIfHasPermission($account, 'edit girl info entities');

      case 'delete':

        return AccessResult::allowedIfHasPermission($account, 'delete girl info entities');
    }

    // Unknown operation, no opinion.
    return AccessResult::neutral();
  }

  /**
   * {@inheritdoc}
   */
  protected function checkCreateAccess(AccountInterface $account, array $context, $entity_bundle = NULL)
  {
    return AccessResult::allowedIfHasPermission($account, 'add girl info entities');
  }
}
