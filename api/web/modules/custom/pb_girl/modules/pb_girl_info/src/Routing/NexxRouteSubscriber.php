<?php

namespace Drupal\pb_girl_info\Routing;

use <PERSON><PERSON>al\Core\Routing\RouteSubscriberBase;
use Symfony\Component\Routing\RouteCollection;

/**
 * Alters the Nexx video route for debugging.
 */
class NexxRouteSubscriber extends RouteSubscriberBase {

  /**
   * {@inheritdoc}
   */
  protected function alterRoutes(RouteCollection $collection) {
    if ($route = $collection->get('nexx_integration.omnia_notification_gateway')) {
      $route->setDefault('_controller', '\Drupal\pb_girl_info\Controller\OmniaController::video');
    }
  }

}
