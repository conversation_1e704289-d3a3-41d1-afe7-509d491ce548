<?php

namespace Drupal\pb_girl_info\Commands;

use Drupal\Core\File\FileSystem;
use Drush\Commands\DrushCommands;

/**
 * Drush commands for pb girl info migration tasks.
 *
 * @package Drupal\pb_girl_info\Commands
 */
class PbGirlInfoMigrationCommand extends DrushCommands {

  /**
   * Constructs a new PbGirlInfoMigrationCommand object.
   */
  public function __construct() {
    parent::__construct();
  }

  /**
   * Converts girl info data files so that they can be used for migration.
   *
   * @command pbgi:process-info-files
   * @aliases pbgi:pif
   */
  public function convertInfoFiles() {
    $count = 0;
    $source_dir = 'public://migration/girl_info/data/infos/';
    $files = $this->getFileList($source_dir);
    foreach ($files as $url) {
      $content = json_decode(file_get_contents($url));
      if (!$content->processed) {
        foreach ($content->girlInfos as $key => $girl_info) {
          foreach ($girl_info->containers as $c_key => $container_id) {
            // Adding id key for migration selector.
            $content->girlInfos[$key]->containers[$c_key] = ['id' => $container_id];
          }
        }
        $count ++;
        $content->processed = 1;
        file_put_contents($url, json_encode($content));
      }
    }

    echo "Processed $count files in $source_dir\n";

    $this->arrangeJsonData('public://migration/girl_info/data/infos/', 'public://migration/girl_info/processed/girls/', 'girls', 'girls');
  }

  /**
   * Converts girl gallery data files so that they can be used for migration.
   *
   * @command pbgi:process-gallery-files
   * @aliases pbgi:pgf
   */
  public function convertGalleryFiles() {
    $count = 0;
    $source_dir = 'public://migration/girl_info/data/images/';
    $files = $this->getFileList($source_dir);
    foreach ($files as $url) {
      $content = json_decode(file_get_contents($url));
      if (!$content->processed) {
        // Set correct images order.
        if (!empty($content->orderImages)) {
          // Save list.
          $images_list = $content->images;
          // Reset content array.
          $content->images = [];
          foreach ($content->orderImages as $order => $image_id) {
            foreach ($images_list as $key => $image_data) {
              if ($image_data->id != $image_id) {
                // Skip unmatch id.
                continue;
              }

              // Save image data to array.
              $content->images[] = $image_data;
              unset($images_list[$key]);
              break;
            }
          }
          if (!empty($images_list)) {
            // Just in case there are more items in the image list than in the order list (shouldn't be the case).
            $content->images = array_merge($content->images, $images_list);
          }

          // Count this file as processed.
          $count ++;
        }

        // Mark this file as processed.
        $content->processed = 1;
        // Save content to file.
        file_put_contents($url, json_encode($content));
      }
    }

    echo "Processed $count files in $source_dir \n";

    $this->arrangeJsonData('public://migration/girl_info/data/images/', 'public://migration/girl_info/processed/galleries/', 'galleries', 'galleries');
  }

  /**
   * Converts user data files so that they can be used for migration.
   *
   * @command pbgi:process-user-files
   * @aliases pbgi:puf
   */
  public function convertUserFiles() {
    $count = 0;
    $source_dir = 'public://migration/girl_info/data/user/';
    $files = $this->getFileList($source_dir);
    foreach ($files as $url) {
      $content = json_decode(file_get_contents($url));
      if (!$content->processed) {
        foreach (['favorites', 'likes'] as $flag_type) {
          foreach ($content->{$flag_type} as $key => $flag) {
            // Add user id to flag item for select in migration.
            $content->{$flag_type}[$key]->user_uuid = $content->_uuid;
          }
        }
        $count ++;
        $content->processed = 1;
        file_put_contents($url, json_encode($content));
      }
    }

    echo "Processed $count files in $source_dir\n";

    $this->arrangeJsonData('public://migration/girl_info/data/user/', 'public://migration/girl_info/processed/user/', 'user', 'user');
  }

  /**
   * Generate flag data from user files so that they can be used for migration.
   *
   * @command pbgi:process-flag-files
   * @aliases pbgi:pff
   */
  public function convertFlagFiles() {
    // @todo
    $foo = true;
  }

  /**
   * Helper method to get a list of (json) files from a directory.
   *
   * @return array
   *   The files.
   */
  protected function getFileList($source_dir) {
    /** @var FileSystem $file_system */
    $file_system = \Drupal::service('file_system');
    $files = glob($file_system->realpath($source_dir . '*.json'));
    return $files;
  }

  /**
   * Bundles json data from files into merge file(s) and adds a wrapper element so it can be selected in the migration.
   *
   * @param string $source_dir
   *   The source directory with image json files.
   * @param string $target
   *   The target directory where to place the gallery json file.
   * @param string $file_name
   *   The result file name. Default: gallery.json
   * @param string $wrapper_item
   *   The result json wrapper item.
   * @param string $extension
   *   The result file extension. Default: json
   * @param int $items_per_file
   *   Items to insert per result file. Default: 100.
   */
  protected function arrangeJsonData($source_dir, $target, $file_name, $wrapper_item, $extension = 'json', $items_per_file = 100) {
    /** @var FileSystem $file_system */
    $file_system = \Drupal::service('file_system');
    @$file_system->mkdir($target, NULL, TRUE);
    $target_path = $file_system->realpath($target);

    $files = glob($file_system->realpath($source_dir . '*.json'));
    $chunks = array_chunk($files, $items_per_file);
    foreach ($chunks as $key => $chunk) {
      $delimiter = ",\n";
      $target_file = $target_path . '/' . $file_name . '_' . $key . '.' . $extension;
      file_put_contents($target_file, "{\n\"$wrapper_item\": [\n");

      foreach ($chunk as $i => $url) {
        if (count($chunk) === ++$i) {
          $delimiter = '';
        }
        file_put_contents($target_file, file_get_contents($file_system->realpath($url)) . $delimiter, FILE_APPEND);
      }

      file_put_contents($target_file, "\n]\n}\n", FILE_APPEND);
    }

    $chunks_count = count($chunks);
    echo "Created $chunks_count files in $target_path with files from $source_dir.\n";
  }

}
