<?php

namespace <PERSON><PERSON>al\pb_girl_info\Form;

use <PERSON><PERSON><PERSON>\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;

/**
 * Class GirlInfoSettingsForm.
 *
 * @ingroup pb_girl_info
 */
class GirlInfoSettingsForm extends FormBase {

  /**
   * Returns a unique string identifying the form.
   *
   * @return string
   *   The unique string identifying the form.
   */
  public function getFormId() {
    return 'girl_info_settings';
  }

  /**
   * Form submission handler.
   *
   * @param array $form
   *   An associative array containing the structure of the form.
   * @param \Drupal\Core\Form\FormStateInterface $form_state
   *   The current state of the form.
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // Empty implementation of the abstract submit class.
  }

  /**
   * Defines the settings form for Girl Info entities.
   *
   * @param array $form
   *   An associative array containing the structure of the form.
   * @param \Drupal\Core\Form\FormStateInterface $form_state
   *   The current state of the form.
   *
   * @return array
   *   Form definition array.
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['girl_info_settings']['#markup'] = 'Settings form for Girl Info entities. Manage field settings here.';
    return $form;
  }

}
