<?php

namespace Drupal\pb_girl_info\Controller;

use <PERSON><PERSON><PERSON>\media\MediaInterface;
use <PERSON><PERSON><PERSON>\nexx_integration\Controller\OmniaController as OmniaControllerBase;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class OmniaController.
 */
class OmniaController extends OmniaControllerBase {

  /**
   * Will add nexx video to gallery, if found.
   *
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   HTTP request.
   *
   * @return \Symfony\Component\HttpFoundation\JsonResponse
   *   JSON response.
   *
   * @throws \Exception
   */
  public function video(Request $request) {
    $response = parent::video($request);

    if (empty($this->videoData->itemData->general->refnr)) {
      return $response;
    }

    $mediaStorage = $this->entityTypeManager()->getStorage('media');
    $ids = $mediaStorage->getQuery()
      ->condition($this->getVideoFieldName() . '.item_id', $this->getVideoId())
      ->range(0, 1)
      ->sort('created', 'DESC')
      ->execute();

    if (empty($ids)) {
      return $response;
    }

    $video = $mediaStorage->load(current($ids));
    if (empty($video)) {
      return $response;
    }

    /** @var MediaInterface $gallery */
    $gallery = current($mediaStorage->loadByProperties([
      'bundle' => 'gallery',
      'uuid' => $this->videoData->itemData->general->refnr,
    ]));

    if (!$gallery || !$gallery->hasField('field_videos')) {
      return $response;
    }

    $referenced_entities = $gallery->field_videos->referencedEntities();
    $add_new = TRUE;
    foreach ($referenced_entities as $entity) {
      if ($entity->id() == $video->id()) {
        $add_new = FALSE;
      }
    }

    if ($add_new) {
      $gallery->field_videos->appendItem($video);
      $gallery->save();
    }

    //\Drupal::logger('PB NEXX')->debug('Nexx video request data: ' . json_encode($this->videoData));
    return $response;
  }

}
