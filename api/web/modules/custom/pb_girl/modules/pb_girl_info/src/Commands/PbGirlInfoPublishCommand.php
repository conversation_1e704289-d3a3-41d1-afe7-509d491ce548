<?php

namespace Drupal\pb_girl_info\Commands;

use Drupal\Core\File\FileSystem;
use Drush\Commands\DrushCommands;
use Drupal\Core\Datetime\DrupalDateTime;
use Drupal\datetime\Plugin\Field\FieldType\DateTimeItemInterface;
use Drupal\media\Entity\Media;
use Drupal\pb_girl\Entity\Girl;
use Drupal\pb_girl_info\Entity\GirlInfo;

/**
 * Drush commands for pb girl info migration tasks.
 *
 * @package Drupal\pb_girl_info\Commands
 */
class PbGirlInfoPublishCommand extends DrushCommands
{

  /**
   * Constructs a new PbGirlInfoMigrationCommand object.
   */
  public function __construct()
  {
    parent::__construct();
  }


  /**
   * Publishes entities which are due.
   *
   * @command pb:publish
   * @aliases pb:p
   */
  public function publish()
  {
    // get now date in the right format
    $date = new DrupalDateTime('now');
    $date->setTimezone(new \DateTimezone(DateTimeItemInterface::STORAGE_TIMEZONE));
    $formatted = $date->format(DateTimeItemInterface::DATETIME_STORAGE_FORMAT);

    $log = [
      'girls' => [
        'published' => [],
        'unpublished' => []
      ],
      'infos' => [
        'published' => [],
        'unpublished' => []
      ],
      'galleries' => [
        'published' => [],
        'unpublished' => []
      ],
    ];

    /* ##########
    GIRLS
    ############# */

    // check what entities to publish
    $girl_group = \Drupal::entityQuery('girl')
      ->orConditionGroup()
      ->condition('field_to', $formatted, '>')
      ->condition('field_to', null, 'IS');

    $publish_query = \Drupal::entityQuery('girl')
      ->condition('status', 0)
      ->condition('field_from', $formatted, '<=')
      ->condition($girl_group)
      ->execute();

    $publish_girls =  Girl::loadMultiple($publish_query);

    if (count($publish_girls) > 0) {
      foreach ($publish_girls as $girl) {
        array_push($log['girls']['published'], ['name' => $girl->getName(), 'id' => $girl->id()]);
        $girl->setPublished();
        $girl->save();
      }
    }

    // check what entities to publish
    $unpublish_query = \Drupal::entityQuery('girl')
      ->condition('status', 1)
      ->condition('field_to', $formatted, '<=')
      ->execute();

    $unpublish_girls =  Girl::loadMultiple($unpublish_query);

    if (count($unpublish_girls) > 0) {
      foreach ($unpublish_girls as $girl) {
        array_push($log['girls']['unpublished'], ['name' => $girl->getName(), 'id' => $girl->id()]);
        $girl->setUnpublished();
        $girl->save();
      }
    }





    /* ##########
    INFOS
    ############# */

    // check what entities to publish
    $infos_group = \Drupal::entityQuery('girl_info')
      ->orConditionGroup()
      ->condition('field_to', $formatted, '>')
      ->condition('field_to', null, 'IS');

    $publish_query = \Drupal::entityQuery('girl_info')
      ->condition('status', 0)
      ->condition('field_from', $formatted, '<=')
      ->condition($infos_group)
      ->execute();

    $publish_infos =  GirlInfo::loadMultiple($publish_query);

    if (count($publish_infos) > 0) {
      foreach ($publish_infos as $info) {
        array_push($log['infos']['published'], ['name' => $info->getName(), 'id' => $info->id()]);
        $info->setPublished();
        $info->save();
      }
    }

    // check what entities to publish
    $unpublish_query = \Drupal::entityQuery('girl_info')
      ->condition('status', 1)
      ->condition('field_to', $formatted, '<=')
      ->execute();

    $unpublish_infos =  GirlInfo::loadMultiple($unpublish_query);

    if (count($unpublish_infos) > 0) {
      foreach ($unpublish_infos as $info) {
        array_push($log['infos']['unpublished'], ['name' => $info->getName(), 'id' => $info->id()]);
        $info->setUnpublished();
        $info->save();
      }
    }




    /* ##########
    GALLERIES
    ############# */

    // check what entities to publish
    $gallery_group = \Drupal::entityQuery('media')
      ->orConditionGroup()
      ->condition('field_unpublish_date', $formatted, '>')
      ->condition('field_unpublish_date', null, 'IS');

    $publish_query = \Drupal::entityQuery('media')
      ->condition('status', 0)
      ->condition('bundle', 'gallery')
      ->condition('field_publish_date', $formatted, '<=')
      ->condition($gallery_group)
      ->execute();

    $publish_galleries =  Media::loadMultiple($publish_query);

    if (count($publish_galleries) > 0) {
      foreach ($publish_galleries as $gallery) {
        array_push($log['galleries']['published'], ['name' => $gallery->getName(), 'id' => $gallery->id()]);
        $gallery->setPublished();
        $gallery->save();
      }
    }

    // check what entities to publish
    $unpublish_query = \Drupal::entityQuery('media')
      ->condition('status', 1)
      ->condition('bundle', 'gallery')
      ->condition('field_unpublish_date', $formatted, '<=')
      ->execute();

    $unpublish_galleries =  Media::loadMultiple($unpublish_query);

    if (count($unpublish_galleries) > 0) {
      foreach ($unpublish_galleries as $gallery) {
        array_push($log['galleries']['unpublished'], ['name' => $gallery->getName(), 'id' => $gallery->id()]);
        $gallery->setUnpublished();
        $gallery->save();
      }
    }

    //update image-count from girl-infos
    // Get all gallery IDs from the log
    $gallery_ids = array_merge(
      array_column($log['galleries']['published'], 'id'),
      array_column($log['galleries']['unpublished'], 'id')
    );

    // Query to get all girl_info IDs that reference these gallery IDs
    if (!empty($gallery_ids)) {
      $girl_info_query = \Drupal::entityQuery('girl_info')
      ->condition('galleries.target_id', $gallery_ids, 'IN');
      $girl_info_ids = $girl_info_query->execute();

      // Log the girl_info IDs for debugging purposes
      \Drupal::logger('pb_girl_info')->notice('Girl Info IDs using published galleries: ' . json_encode(array_values($girl_info_ids)));

      if (!empty($girl_info_ids)) {
        $girl_infos = GirlInfo::loadMultiple($girl_info_ids);

        foreach ($girl_infos as $girl_info) {
          // Initialize counters for images and videos
          $count_images = 0;
          $count_videos = 0;

          // Get referenced galleries
          $galleries = $girl_info->get('galleries')->referencedEntities();
          if (count($galleries) > 0) {
            foreach ($galleries as $gallery) {
              // Check if the gallery is published
              if ($gallery->get('status')->value == 1) {
                $count_images += count($gallery->get('field_media_slideshow')->referencedEntities());
                $count_videos += count($gallery->get('field_videos')->referencedEntities());
              }
            }
          }

          // Get current values from the girl_info entity
          $image_current_value = $girl_info->get('field_image_count')->value;
          $video_current_value = $girl_info->get('field_video_count')->value;

          // Update the fields if the counts have changed
          if ($image_current_value != $count_images || $video_current_value != $count_videos) {
            $girl_info->set('field_image_count', $count_images);
            $girl_info->set('field_video_count', $count_videos);
            $girl_info->save();

            // Log the update for debugging
            \Drupal::logger('pb_girl_info')->notice('Updated Girl Info ID: ' . $girl_info->id() . ' with image count: ' . $count_images . ' and video count: ' . $count_videos);
          }
        }
      }
    }

    // Update girl info with the latest gallery release date
    if (!empty($log['galleries']['published'])) {
      foreach ($log['galleries']['published'] as $published_gallery) {
        // Load the gallery entity
        $gallery = Media::load($published_gallery['id']);
        if ($gallery) {
          // Get the publish date of the gallery
          $gallery_publish_date = $gallery->get('field_publish_date')->value;

          // Convert the publish date to a Unix timestamp
          $timestamp_publish_date = strtotime($gallery_publish_date);

          // Query to get all girl_info IDs that reference this gallery
          $girl_info_query = \Drupal::entityQuery('girl_info')
            ->condition('galleries.target_id', $gallery->id(), '=');
          $girl_info_ids = $girl_info_query->execute();

          if (!empty($girl_info_ids)) {
            $girl_infos = GirlInfo::loadMultiple($girl_info_ids);

            foreach ($girl_infos as $girl_info) {
              // Get the current value of field_latest_gallery_release
              $current_release_timestamp = $girl_info->get('field_latest_gallery_release')->value;

              // Update the field if the gallery's publish date is newer
              if (empty($current_release_timestamp) || $timestamp_publish_date > $current_release_timestamp) {
                $girl_info->set('field_latest_gallery_release', $timestamp_publish_date);
                $girl_info->save();

                // Log the update for debugging
                \Drupal::logger('pb_girl_info')->notice('Updated Girl Info ID: ' . $girl_info->id() . ' with latest gallery release timestamp: ' . $timestamp_publish_date);
              }
            }
          }
        }
      }
    }

    \Drupal::logger('pb_girl_info')->notice(json_encode($log));
  }
}
