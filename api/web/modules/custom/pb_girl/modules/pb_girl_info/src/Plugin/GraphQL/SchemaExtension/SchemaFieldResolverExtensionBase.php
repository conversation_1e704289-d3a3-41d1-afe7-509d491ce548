<?php

namespace Drupal\pb_girl_info\Plugin\GraphQL\SchemaExtension;

use <PERSON><PERSON><PERSON>\graphql\GraphQL\ResolverBuilder;
use <PERSON><PERSON>al\graphql\GraphQL\ResolverRegistry;
use Dr<PERSON>al\graphql\GraphQL\ResolverRegistryInterface;
use Drupal\graphql\Plugin\GraphQL\SchemaExtension\SdlSchemaExtensionPluginBase;
use Drupal\pb_girl_info\Wrappers\QueryConnection;

/**
 * Schema extension base class to provide streamlined field resolving.
 */
abstract class SchemaFieldResolverExtensionBase extends SdlSchemaExtensionPluginBase {

  const RESOLVE_FIELDS_SINGLE_VALUE = 'single_value_fields';
  const RESOLVE_FIELDS_MULTI_VALUE = 'multi_value_fields';
  const RESOLVE_FIELDS_SINGLE_REFERENCE = 'single_reference_fields';
  const RESOLVE_FIELDS_MULTI_REFERENCE = 'multi_reference_fields';

  /**
   * Resolves fields from an entity.
   *
   * @param string $schema_type
   *   The schema type name.
   * @param ResolverRegistryInterface $registry
   *   The resolver registry.
   * @param ResolverBuilder $builder
   *   The resolver builder.
   * @param array $fields_info
   *   List of entity specific field names to resolve, containing one or more of these keys:
   *     - SchemaFieldResolverExtensionBase::RESOLVE_FIELDS_SINGLE_VALUE: Array with single value property fields of the entity.
   *     - RESOLVE_FIELDS_MULTI_VALUE::RESOLVE_FIELDS_MULTI_VALUE: Array with multi value property fields of the entity.
   * @param string $entity_type
   *   The source entity type id. Required if $fields_info is set.
   */
  public function resolveFields($schema_type, ResolverRegistryInterface $registry, ResolverBuilder $builder, array $fields_info = [], $entity_type = NULL) {
    foreach ($fields_info as $field_type => $fields) {
      foreach ($fields as $field_name) {
        $resolve_field_name = str_replace('field_', '', $field_name);

        switch ($field_type) {
          case self::RESOLVE_FIELDS_SINGLE_VALUE:
            $registry->addFieldResolver($schema_type, $resolve_field_name,
              $builder->produce('property_path')
                ->map('type', $builder->fromValue('entity:' . $entity_type))
                ->map('value', $builder->fromParent())
                ->map('path', $builder->fromValue($field_name . '.value'))
            );
            break;

          case self::RESOLVE_FIELDS_MULTI_VALUE:
            $registry->addFieldResolver($schema_type, $resolve_field_name, $builder->compose(
              $builder->produce('property_path')
                ->map('type', $builder->fromValue('entity:' . $entity_type))
                ->map('value', $builder->fromParent())
                ->map('path', $builder->fromValue($field_name)),
              $builder->callback(function ($entity) {
                $list = [];
                foreach($entity as $item){
                  array_push($list, $item['value']);
                }
                return $list;
              })
            ));
            break;

          case self::RESOLVE_FIELDS_SINGLE_REFERENCE:
            $registry->addFieldResolver($schema_type, $resolve_field_name,
              $builder->produce('property_path')
                ->map('type', $builder->fromValue('entity:' . $entity_type))
                ->map('value', $builder->fromParent())
                ->map('path', $builder->fromValue($field_name . '.entity'))
            );
            break;

          case self::RESOLVE_FIELDS_MULTI_REFERENCE:
            $registry->addFieldResolver($schema_type, $resolve_field_name,
              $builder->produce('entity_reference')
                ->map('entity', $builder->fromParent())
                ->map('field', $builder->fromValue($field_name))
            );
            break;
        }
      }
    }

    // Resolves entity id.
    $registry->addFieldResolver($schema_type, 'id',
      $builder->produce('entity_id')->map('entity', $builder->fromParent())
    );

    // Resolves entity name if exist.
    $registry->addFieldResolver($schema_type, 'name',
      $builder->produce('entity_label')->map('entity', $builder->fromParent())
    );

    // Resolves entity created & changed fields if exist.
    $registry->addFieldResolver($schema_type, 'created',
      $builder->produce('entity_created')
        ->map('entity', $builder->fromParent())
    );
    $registry->addFieldResolver($schema_type, 'changed',
      $builder->produce('entity_changed')
        ->map('entity', $builder->fromParent())
    );

    // Adds the image url of an media entity.
    $registry->addFieldResolver($schema_type, 'image_url', $builder->compose(
      $builder->produce('property_path')
        ->map('type', $builder->fromValue('entity:media'))
        ->map('value', $builder->fromParent())
        ->map('path', $builder->fromValue('field_media_image.entity')),
      $builder->produce('image_url')->map('entity', $builder->fromParent())
    ));
  }

  /**
   * Resolves a list of entities from a query connection wrapper.
   *
   * @param string $type
   *   The schema type name.
   * @param ResolverRegistryInterface $registry
   *   The resolver registry.
   * @param ResolverBuilder $builder
   *   The resolver builder.
   */
  protected function addConnectionFields($type, ResolverRegistry $registry, ResolverBuilder $builder): void {
    $registry->addFieldResolver($type, 'total',
      $builder->callback(function (QueryConnection $connection) {
        return $connection->total();
      })
    );

    $registry->addFieldResolver($type, 'items',
      $builder->callback(function (QueryConnection $connection) {
        return $connection->items();
      })
    );
  }
}
