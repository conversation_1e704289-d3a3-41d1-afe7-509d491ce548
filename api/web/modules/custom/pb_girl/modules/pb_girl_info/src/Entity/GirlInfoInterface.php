<?php

namespace Drupal\pb_girl_info\Entity;

use <PERSON><PERSON>al\Core\Datetime\DrupalDateTime;
use <PERSON><PERSON>al\Core\Entity\ContentEntityInterface;
use Drupal\Core\Entity\EntityChangedInterface;
use <PERSON><PERSON>al\Core\Entity\EntityPublishedInterface;
use Dr<PERSON>al\media\MediaInterface;
use <PERSON><PERSON>al\pb_girl\Entity\GirlInterface;

/**
 * Provides an interface for defining Girl info entities.
 *
 * @ingroup pb_girl_info
 */
interface GirlInfoInterface extends ContentEntityInterface, EntityChangedInterface, EntityPublishedInterface
{

  /**
   * Gets the Girl entity name.
   *
   * @return string
   *   Name of the Girl entity.
   */
  public function getName();

  /**
   * Sets the Girl info entity name.
   *
   * @param string $name
   *   The Girl entity name.
   *
   * @return $this
   */
  public function setName($name);

  /**
   * Gets the first name.
   *
   * @return string
   *   The first name of the girl.
   */
  public function getFirstName();

  /**
   * Sets the first name.
   *
   * @param string $first_name
   *   The first name of the girl.
   *
   * @return $this
   */
  public function setFirstName($first_name);

  /**
   * Gets the middle name.
   *
   * @return string
   *   The middle name of the girl.
   */
  public function getMiddleName();

  /**
   * Sets the middle name.
   *
   * @param string $middle_name
   *   The middle name.
   *
   * @return $this
   */
  public function setMiddleName($middle_name);

  /**
   * Gets the last name.
   *
   * @return string
   *   The last name of the girl.
   */
  public function getLastName();

  /**
   * Sets the last name.
   *
   * @param string $last_name
   *   The last name of the girl.
   *
   * @return $this
   */
  public function setLastName($last_name);

  /**
   * Gets the bust size.
   *
   * @return int
   *   The bust size.
   */
  public function getBustSize();

  /**
   * Sets the bust size.
   *
   * @param int $size
   *   The bust size.
   *
   * @return $this
   */
  public function setBustSize($size);

  /**
   * Gets the waist size.
   *
   * @return int
   *   The waist size.
   */
  public function getWaistSize();

  /**
   * Sets the waist size.
   *
   * @param int $size
   *   The waist size.
   *
   * @return $this
   */
  public function setWaistSize($size);

  /**
   * Gets the hip size.
   *
   * @return int
   *   The hip size.
   */
  public function getHipSize();

  /**
   * Sets the hip size.
   *
   * @param int $size
   *   The hip size.
   *
   * @return $this
   */
  public function setHipSize($size);

  /**
   * Gets the cup size.
   *
   * @return int
   *   The cup size.
   */
  public function getCupSize();

  /**
   * Sets the cup size.
   *
   * @param int $size
   *   The cup size.
   *
   * @return $this
   */
  public function setCupSize($size);

  /**
   * Gets the height.
   *
   * @return int
   *   The height.
   */
  public function getHeight();

  /**
   * Sets the height.
   *
   * @param int $height
   *   The height.
   *
   * @return $this
   */
  public function setHeight($height);

  /**
   * Gets the weight size.
   *
   * @return int
   *   The weight size.
   */
  public function getWeight();

  /**
   * Sets the weight.
   *
   * @param int $weight
   *   The weight.
   *
   * @return $this
   */
  public function setWeight($weight);

  /**
   * Gets the hair color.
   *
   * @return string
   *   The hair color of the girl.
   */
  public function getHairColor();

  /**
   * Sets the hair color.
   *
   * @param string $color
   *   The hair color of the girl.
   *
   * @return $this
   */
  public function setHairColor($color);

  /**
   * Gets the eye color.
   *
   * @return string
   *   The eye color of the girl.
   */
  public function getEyeColor();

  /**
   * Sets the eye color.
   *
   * @param string $color
   *   The eye color of the girl.
   *
   * @return $this
   */
  public function setEyeColor($color);

  /**
   * Gets the description text.
   *
   * @return string
   *   The description text of the girl info entity.
   */
  public function getDescription();

  /**
   * Sets the description text.
   *
   * @param string $description
   *   The description text of the girl entity.
   *
   * @return $this
   */
  public function setDescription($description);

  /**
   * Gets the description original text.
   *
   * @return string
   *   The description original text of the girl info entity.
   */
  public function getDescriptionOriginal();

  /**
   * Sets the description original text.
   *
   * @param string $description_original
   *   The description original text of the girl entity.
   *
   * @return $this
   */
  public function setDescriptionOriginal($description_original);

  /**
   * Gets the home town.
   *
   * @return string
   *   The home town of the girl.
   */
  public function getHometown();

  /**
   * Sets the home town.
   *
   * @param string $home_town
   *   The home town of the girl.
   *
   * @return $this
   */
  public function setHometown($home_town);

  /**
   * Gets the city.
   *
   * @return string
   *   The city of the girl.
   */
  public function getCity();

  /**
   * Sets the city.
   *
   * @param string $city
   *   The city of the girl.
   *
   * @return $this
   */
  public function setCity($city);

  /**
   * Gets the province.
   *
   * @return string
   *   The province of the girl.
   */
  public function getProvince();

  /**
   * Sets the province.
   *
   * @param string $province
   *   The province of the girl.
   *
   * @return $this
   */
  public function setProvince($province);

  /**
   * Gets the country.
   *
   * @return string
   *   The country of the girl.
   */
  public function getCountry();

  /**
   * Sets the country.
   *
   * @param string $country
   *   The country of the girl.
   *
   * @return $this
   */
  public function setCountry($country);

  /**
   * Get the girl info release timestamp.
   *
   * @return int
   *   The girl info release timestamp.
   */
  public function getReleaseDate();

  /**
   * Sets the girl info release date.
   *
   * @param int $release_timestamp
   *   The girl info release timestamp.
   *
   * @return $this
   */
  public function setReleaseDate($release_timestamp);

  /**
   * Gets the Girl creation timestamp.
   *
   * @return int
   *   Creation timestamp of the Girl.
   */
  public function getCreatedTime();

  /**
   * Sets the Girl Info creation timestamp.
   *
   * @param int $timestamp
   *   The Girl Info creation timestamp.
   *
   * @return $this
   */
  public function setCreatedTime($timestamp);

  /**
   * Gets the referenced Girl entity.
   *
   * @return GirlInterface
   *   The girl entity.
   */
  public function getGirlEntity();

  /**
   * Gets the referenced galleries.
   *
   * @return MediaInterface[]
   *   The referenced gallery media entities.
   */
  public function getGalleries();

  /**
   * Get the birthday.
   *
   * @return \Drupal\Core\Datetime\DrupalDateTime
   *   The birthday date time.
   */
  public function getBirthday();

  /**
   * Sets the birthday.
   *
   * @param \Drupal\Core\Datetime\DrupalDateTime $birthday
   *   The birthday date time.
   *
   * @return $this
   */
  public function setBirthday(DrupalDateTime $birtday);
}