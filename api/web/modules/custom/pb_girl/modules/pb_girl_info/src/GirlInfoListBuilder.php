<?php

namespace Drupal\pb_girl_info;

use Dr<PERSON>al\Core\Entity\EntityInterface;
use Drupal\Core\Entity\EntityListBuilder;
use <PERSON>upal\Core\Link;

/**
 * Defines a class to build a listing of Girl Info entities.
 *
 * @ingroup pb_girl
 */
class GirlInfoListBuilder extends EntityListBuilder {

  /**
   * {@inheritdoc}
   */
  public function buildHeader() {
    $header['id'] = $this->t('Girl Info ID');
    $header['name'] = $this->t('Name');
    return $header + parent::buildHeader();
  }

  /**
   * {@inheritdoc}
   */
  public function buildRow(EntityInterface $entity) {
    /* @var \Drupal\pb_girl\Entity\Girl $entity */
    $row['id'] = $entity->id();
    $row['name'] = Link::createFromRoute(
      $entity->label(),
      'entity.girl_info.edit_form',
      ['girl_info' => $entity->id()]
    );
    return $row + parent::buildRow($entity);
  }

}
