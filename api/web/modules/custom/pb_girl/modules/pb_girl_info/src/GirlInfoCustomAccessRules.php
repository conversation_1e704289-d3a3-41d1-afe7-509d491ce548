<?php

namespace Drupal\pb_girl_info;


/**
 * Class to connect to Harvest
 */
class GirlInfoCustomAccessRules
{


  public function __construct() {}

  public function checkAccess($entity)
  {

    if (is_null($entity->get('descriptor_month')->value) || is_null($entity->get('descriptor_year')->value)) {
      return false;
    }

    $threeMonthsAgo = strtotime('FIRST DAY OF -3 MONTH');
    $issueDate = strtotime($entity->get('descriptor_month')->value . '/01/' . $entity->get('descriptor_year')->value);

    // check if the field descriptor_month and descriptor_year are within the last three months
    if ($issueDate < $threeMonthsAgo) {
      return false;
    }

    // check if the field category is not one of the following
    $category = $entity->get('field_category')->target_id;
    $allowedCategories = ['2245', '2264', '10199', '10198', '2274'];
    if (!in_array($category, $allowedCategories)) {
      return false;
    }

    return true;
  }
}
