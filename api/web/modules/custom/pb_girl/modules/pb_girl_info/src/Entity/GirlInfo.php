<?php

namespace Drupal\pb_girl_info\Entity;

use Drupal\Core\Datetime\DrupalDateTime;
use Drupal\Core\Field\BaseFieldDefinition;
use Drupal\Core\Entity\ContentEntityBase;
use Drupal\Core\Entity\EntityChangedTrait;
use Drupal\Core\Entity\EntityPublishedTrait;
use Drupal\Core\Entity\EntityTypeInterface;
use Drupal\Core\Field\FieldStorageDefinitionInterface;
use Drupal\datetime\Plugin\Field\FieldType\DateTimeItem;

/**
 * Defines the Girl Info entity.
 *
 * @ingroup pb_girl_info
 *
 * @ContentEntityType(
 *   id = "girl_info",
 *   label = @Translation("Girl Info"),
 *   handlers = {
 *     "view_builder" = "Drupal\Core\Entity\EntityViewBuilder",
 *     "list_builder" = "Drupal\pb_girl_info\GirlInfoListBuilder",
 *     "views_data" = "Drupal\pb_girl_info\Entity\GirlInfoViewsData",
 *     "translation" = "Drupal\pb_girl_info\GirlInfoTranslationHandler",
 *
 *     "form" = {
 *       "default" = "Drupal\pb_girl_info\Form\GirlInfoForm",
 *       "add" = "Drupal\pb_girl_info\Form\GirlInfoForm",
 *       "edit" = "Drupal\pb_girl_info\Form\GirlInfoForm",
 *       "delete" = "Drupal\pb_girl_info\Form\GirlInfoDeleteForm",
 *     },
 *     "route_provider" = {
 *       "html" = "Drupal\pb_girl_info\GirlInfoHtmlRouteProvider",
 *     },
 *     "access" = "Drupal\pb_girl_info\GirlInfoAccessControlHandler",
 *   },
 *   base_table = "girl_info",
 *   data_table = "girl_info_field_data",
 *   translatable = TRUE,
 *   admin_permission = "administer girl info entities",
 *   entity_keys = {
 *     "id" = "id",
 *     "label" = "name",
 *     "uuid" = "uuid",
 *     "langcode" = "langcode",
 *     "published" = "status",
 *   },
 *   links = {
 *     "canonical" = "/admin/structure/girl_info/{girl_info}",
 *     "add-form" = "/admin/structure/girl_info/add",
 *     "edit-form" = "/admin/structure/girl_info/{girl_info}/edit",
 *     "delete-form" = "/admin/structure/girl_info/{girl_info}/delete",
 *     "collection" = "/admin/structure/girl_info",
 *   },
 *   field_ui_base_route = "girl_info.settings"
 * )
 */
class GirlInfo extends ContentEntityBase implements GirlInfoInterface
{

  use EntityChangedTrait;
  use EntityPublishedTrait;

  /**
   * {@inheritdoc}
   */
  public function getName()
  {
    return $this->get('name')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setName($name)
  {
    $this->set('name', $name);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getCreatedTime()
  {
    return $this->get('created')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setCreatedTime($timestamp)
  {
    $this->set('created', $timestamp);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getFirstName()
  {
    return $this->get('firstname')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setFirstName($first_name)
  {
    $this->set('firstname', $first_name);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getMiddleName()
  {
    return $this->get('middlename')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setMiddleName($middle_name)
  {
    $this->set('middlename', $middle_name);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getLastName()
  {
    return $this->get('lastname')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setLastName($last_name)
  {
    $this->set('lastname', $last_name);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getBustSize()
  {
    return $this->get('bustsize')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setBustSize($size)
  {
    $this->set('bustsize', $size);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getWaistSize()
  {
    return $this->get('waistsize')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setWaistSize($size)
  {
    $this->set('waistsize', $size);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getHipSize()
  {
    return $this->get('hipsize')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setHipSize($size)
  {
    $this->set('hipsize', $size);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getCupSize()
  {
    return $this->get('cupsize')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setCupSize($size)
  {
    $this->set('cupsize', $size);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getHeight()
  {
    return $this->get('height')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setHeight($height)
  {
    $this->set('height', $height);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getWeight()
  {
    return $this->get('weight')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setWeight($weight)
  {
    $this->set('weight', $weight);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getHairColor()
  {
    return $this->get('haircolor')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setHairColor($color)
  {
    $this->set('haircolor', $color);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getEyeColor()
  {
    return $this->get('eyecolor')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setEyeColor($color)
  {
    $this->set('eyecolor', $color);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getDescription()
  {
    return $this->get('description')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setDescription($description)
  {
    $this->set('description', $description);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getDescriptionOriginal()
  {
    return $this->get('description_original')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setDescriptionOriginal($description_original)
  {
    $this->set('description_original', $description_original);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getHometown()
  {
    return $this->get('hometown')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setHometown($home_town)
  {
    $this->set('hometown', $home_town);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getCity()
  {
    return $this->get('city')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setCity($city)
  {
    $this->set('city', $city);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function setProvince($province)
  {
    $this->set('province', $province);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getProvince()
  {
    return $this->get('province')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setCountry($country)
  {
    $this->set('country', $country);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getCountry()
  {
    return $this->get('country')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function getReleaseDate()
  {
    return $this->get('release')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setReleaseDate($release_timestamp)
  {
    $this->set('release', $release_timestamp);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getGirlEntity()
  {
    return $this->get('girl')->entity;
  }

  /**
   * {@inheritdoc}
   */
  public function getGalleries()
  {
    return $this->get('galleries')->referencedEntities();
  }

  /**
   * {@inheritdoc}
   */
  public function getBirthday()
  {
    return new DrupalDateTime($this->get('birthday')->value);
  }

  /**
   * {@inheritdoc}
   */
  public function setBirthday(DrupalDateTime $birtday)
  {
    $this->get('birthday')->value = $birtday->format(DateTimeItem::DATETIME_STORAGE_FORMAT);
    return $this;
  }

  /**
   * Helper method to create an integer field.
   *
   * @param $label
   *   The field label.
   * @param $description
   *   The field description.
   * @param bool $required
   *   Set true if this field is required. Default: FALSE.
   *
   * @return BaseFieldDefinition
   *   The base field definition.
   */
  private static function createIntegerField($label, $description, $required = FALSE)
  {
    return BaseFieldDefinition::create('integer')
      ->setLabel($label)
      ->setDescription($description)
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'number',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired($required);
  }

  /**
   * Helper method to create a string base field.
   *
   * @param string $label
   *   The field label.
   * @param $description
   *   The field description.
   * @param int $cardinality
   *   The field cardinality. Default: 1.
   * @param int $max_length
   *   Max length of the field. Default: 255.
   * @param bool $required
   *   Set true if this field is required. Default: FALSE.
   *
   * @return BaseFieldDefinition
   *   The base field definition.
   */
  private static function createStringField($label, $description, $cardinality = 1, $max_length = 255, $required = FALSE)
  {
    return BaseFieldDefinition::create('string')
      ->setLabel($label)
      ->setDescription($description)
      ->setCardinality($cardinality)
      ->setSettings([
        'max_length' => $max_length,
        'text_processing' => 0,
      ])
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'string_textfield',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired($required);
  }

  /**
   * Helper method to create a text field.
   *
   * @param $label
   *   The field label.
   * @param $description
   *   The field description.
   * @param bool $required
   *   Set true if this field is required. Default: FALSE.
   *
   * @return BaseFieldDefinition
   *   The base field definition.
   */
  private static function createTextField($label, $description, $required = FALSE)
  {
    return BaseFieldDefinition::create('string_long')
      ->setLabel($label)
      ->setDescription($description)
      ->setSettings([
        'text_processing' => 0,
      ])
      ->setDisplayOptions('view', [
        'label' => 'hidden',
        'type' => 'text_default',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'text_textarea',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired($required);
  }

  /**
   * {@inheritdoc}
   */
  public static function baseFieldDefinitions(EntityTypeInterface $entity_type)
  {
    $fields = parent::baseFieldDefinitions($entity_type);

    // Add the published field.
    $fields += static::publishedBaseFieldDefinitions($entity_type);

    $fields['name'] = self::createStringField(t('Name'), t('The label/name of the girl info entity.'), 1, 255, TRUE);
    $fields['firstname'] = self::createStringField(t('Firstname'), t('The firstname of the Girl.'));
    $fields['middlename'] = self::createStringField(t('Middlename'), t('The middlename of the Girl.'));
    $fields['lastname'] = self::createStringField(t('Lastname'), t('The lastname of the Girl.'));
    $fields['bustsize'] = self::createIntegerField(t('Bustsize'), t('The bustsize of the Girl.'));
    $fields['waistsize'] = self::createIntegerField(t('Waistsize'), t('The waistsize of the Girl.'));
    $fields['hipsize'] = self::createIntegerField(t('Hipsize'), t('The hipsize of the Girl.'));
    $fields['height'] = self::createIntegerField(t('Height'), t('The height of the Girl.'));
    $fields['weight'] = self::createIntegerField(t('Weight'), t('The weight of the Girl.'));
    $fields['city'] = self::createStringField(t('City'), t('The city of the Girl.'));
    $fields['province'] = self::createStringField(t('Province'), t('The province of the Girl.'));
    $fields['country'] = self::createStringField(t('Country'), t('The country of the Girl.'));
    $fields['description'] = self::createTextField(t('Description'), t('The description text.'));
    $fields['description_original'] = self::createTextField(t('Description Original'), t('The description original text.'));
    $fields['descriptor_year'] = self::createIntegerField(t('Descriptor year'), t('The descriptor year.'));
    $fields['descriptor_month'] = self::createIntegerField(t('Descriptor month'), t('The descriptor month.'));
    $fields['descriptor_week'] = self::createIntegerField(t('Descriptor week'), t('The descriptor week.'));

    $fields['haircolor'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('Haircolor reference')
      ->setDescription('The haircolor.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['color']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['eyecolor'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('Eyecolor reference')
      ->setDescription('The eyecolor.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['color', 'eyecolor']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['city'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('City reference')
      ->setDescription('The city.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['city']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['hometown'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('Hometown reference')
      ->setDescription('The hometown.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['city']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['cupsize'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('Cupsize reference')
      ->setDescription('The cupsize.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['cups']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['country'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('Country reference')
      ->setDescription('The country.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['country']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['province'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('Province reference')
      ->setDescription('The province.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['province']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['descriptor_category'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('Descriptor category/title')
      ->setDescription('The descriptor title.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['category']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['descriptor_country_ref'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel('Descriptor country reference')
      ->setDescription('The descriptor country.')
      ->setCardinality(1)
      ->setSetting('target_type', 'taxonomy_term')
      ->setSetting('handler', 'default:taxonomy_term')
      ->setSetting('handler_settings', ['target_bundles' => ['country']])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['main_images'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel(t('Main images'))
      ->setDescription(t('Main images of the girl.'))
      ->setSettings([
        'target_type' => 'media',
        'handler_settings' => [
          'target_bundles' => [
            'image' => 'image',
          ],
        ],
      ])
      ->setCardinality(FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED)
      ->setDisplayOptions('form', [
        'type' => 'image_image',
        'weight' => -4
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['non_nude_images'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel(t('Non-nude images'))
      ->setDescription(t('Non-nude images of the girl.'))
      ->setSettings([
        'target_type' => 'media',
        'handler_settings' => [
          'target_bundles' => [
            'image' => 'image',
          ],
        ],
      ])
      ->setCardinality(FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED)
      ->setDisplayOptions('form', [
        'type' => 'image_image',
        'weight' => -4
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['release'] = BaseFieldDefinition::create('timestamp')
      ->setLabel(t('Release'))
      ->setDescription(t('The girl info release time.'))
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'timestamp',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'datetime_default',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['birthday'] = BaseFieldDefinition::create('datetime')
      ->setLabel(t('Birthday'))
      ->setDescription(t('The birthday of the girl.'))
      ->setSettings([
        'datetime_type' => 'date'
      ])
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'datetime_default',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['publish_date'] = BaseFieldDefinition::create('datetime')
      ->setLabel(t('Publish date'))
      ->setDescription(t('The entity publish date.'))
      ->setSettings([
        'datetime_type' => 'datetime'
      ])
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'datetime_default',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['unpublish_date'] = BaseFieldDefinition::create('datetime')
      ->setLabel(t('Unpublish date'))
      ->setDescription(t('The entity unpublish date.'))
      ->setSettings([
        'datetime_type' => 'datetime'
      ])
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'datetime_default',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['girl'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel(t('Girl'))
      ->setDescription(t('Reference to girl entity.'))
      ->setCardinality(1)
      ->setRequired(TRUE)
      ->setSetting('target_type', 'girl')
      ->setDisplayOptions('view', [
        'label' => 'hidden',
        'type' => 'entity_reference_entity_view',
        'weight' => 0,
      ])
      ->setDisplayOptions('form', [
        'type' => 'entity_reference_autocomplete',
        'weight' => 5,
        'settings' => [
          'match_operator' => 'CONTAINS',
          'size' => '60',
          'autocomplete_type' => 'tags',
          'placeholder' => '',
        ],
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE);

    $fields['tags'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel(t('Tags'))
      ->setDescription(t('References to taxonomy terms.'))
      ->setCardinality(FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED)
      ->setRequired(FALSE)
      ->setSetting('target_type', 'taxonomy_term')
      ->setDisplayOptions('view', [
        'label' => 'hidden',
        'type' => 'entity_reference_entity_view',
        'weight' => 0,
      ])
      ->setDisplayOptions('form', [
        'type' => 'entity_reference_autocomplete_tags',
        'weight' => 5,
        'settings' => [
          'match_operator' => 'CONTAINS',
          'size' => '60',
          'match_limit' => '10',
          'autocomplete_type' => 'tags',
          'placeholder' => '',
        ],
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE);

    $fields['galleries'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel(t('Galleries'))
      ->setDescription(t('The galleries of the girl.'))
      ->setCardinality(FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED)
      ->setSetting('target_type', 'media')
      ->setSetting('handler_settings', [
        'target_bundles' => [
          'gallery' => 'gallery',
        ]
      ])
      ->setDisplayOptions('view', [
        'label' => 'hidden',
        'type' => 'entity_reference_entity_view',
        'weight' => 0,
      ])
      ->setDisplayOptions('form', [
        'type' => 'entity_reference_autocomplete',
        'weight' => 5,
        'settings' => [
          'match_operator' => 'CONTAINS',
          'size' => '60',
          'autocomplete_type' => 'tags',
          'placeholder' => '',
        ],
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE);

    $fields['status']->setDescription(t('A boolean indicating whether the Girl is published.'))
      ->setDisplayOptions('form', [
        'type' => 'boolean_checkbox',
        'weight' => -2,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE);

    $fields['created'] = BaseFieldDefinition::create('created')
      ->setLabel(t('Created'))
      ->setDescription(t('The time that the entity was created.'));

    $fields['changed'] = BaseFieldDefinition::create('changed')
      ->setLabel(t('Changed'))
      ->setDescription(t('The time that the entity was last edited.'));

    return $fields;
  }
}