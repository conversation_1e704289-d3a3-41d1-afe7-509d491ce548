type GirlInfo {
  id: Int!
  firstname: String
  middlename: String
  lastname: String
  bustsize: Int
  waistsize: Int
  hipsize: Int
  cupsize: Int
  height: Int
  weight: Int
  haircolor: String
  eyecolor: String
  hometown: String
  description: String
  descriptors: [String]
  main_images: [Image]
  non_nude_images: [Image]
  release: Int
  birthday: String
  girl: Girl
  tags: [TagTerm]
  galleries: [Gallery]
  created: String
  changed: String
}

type Girl {
  id: Int
  name: String
}

type GirlInfoList {
  total: Int!
  items: [GirlInfo!]
}

type GirlList {
  total: Int!
  items: [Girl!]
}

type Gallery {
  id: Int
  name: String
  image_url: String
  media_slideshow: [Image]
  tags: [TagTerm]
  credit: String
}

type Image {
  id: Int
  name: String
  image_url: String
  description: String
  fsk: String
  credit: String
}

type TagTerm {
  id: Int
  name: String
}
