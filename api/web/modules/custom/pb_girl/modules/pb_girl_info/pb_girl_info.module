<?php

/**
 * @file
 * Contains pb_girl_info.module.
 */

use Drupal\migrate\MigrateSkipRowException;
use Dr<PERSON>al\migrate\Row;
use <PERSON><PERSON>al\migrate\Plugin\MigrationInterface;
use <PERSON><PERSON>al\migrate\Plugin\MigrateSourceInterface;
use <PERSON><PERSON>al\Core\Field\BaseFieldDefinition;
use Drupal\Core\Entity\EntityTypeInterface;
use Drupal\pb_girl_info\Plugin\Field\FieldType\AccessComputedField;
use Drupal\pb_girl_info\GirlInfoCustomAccessRules;
use Drupal\playboy_public_image_saver\PlayboyPublicImageSaver;


/**
 * Implements hook_migrate_MIGRATION_ID_prepare_row().
 */
function pb_girl_info_migrate_user_prepare_row(Row $row, MigrateSourceInterface $source, MigrationInterface $migration)
{
  // Set user status active if not blocked or expired.
  $row->setDestinationProperty('status', !($row->getSourceProperty('blocked') || $row->getSourceProperty('expired')));
}

/**
 * Implements hook_migrate_prepare_row().
 */
function pb_girl_info_migrate_prepare_row(Row $row, MigrateSourceInterface $source, MigrationInterface $migration)
{
  $entity_type_manager = \Drupal::entityTypeManager();
  if ($migration->migration_group == 'flags') {
    // Use girl info flag instead of gallery flags.
    if (strtolower($row->getSourceProperty('entity_type')) == 'media_container') {
      $row->setSourceProperty('entity_type', 'girl_info');
      $row->setDestinationProperty('entity_type', 'girl_info');
      $row->setDestinationProperty('flag_id', 'girl_info_flag');
      $girl_info_storage = $entity_type_manager->getStorage('girl_info');

      $gallery = \Drupal::service('entity.repository')->loadEntityByUuid('media', $row->getSourceProperty('uuid'));
      if (!empty($gallery)) {
        // Gallery found, load referencing girl infos.
        $girl_infos = $girl_info_storage->loadByProperties(['galleries' => $gallery->id()]);

        if (!is_null($girl_infos) && !empty($girl_infos)) {
          // Girl info found, use first and replace id and uuid in source properties.
          $info = array_values($girl_infos)[0];
          $row->setSourceProperty('id', $info->id());
        }
      }
    }

    // Dynamically set the source entity id for row.
    // Girl / Girl info.
    $entity_type = strtolower($row->getSourceProperty('entity_type'));
    if ($entity_type == 'media_image') {
      $entity_type = 'media';
    }

    /** @var \Drupal\Core\Entity\EntityInterface $entity */
    if (!empty($info)) {
      $entity = \Drupal::service('entity.repository')->loadEntityByUuid($entity_type, $info->uuid());
    } else {
      $entity = \Drupal::service('entity.repository')->loadEntityByUuid($entity_type, $row->getSourceProperty('uuid'));
    }
    if (!empty($entity)) {
      // Entity with uuid found. Set id.
      $row->setSourceProperty('entity_id', $entity->id());
    }
  }
}

/**
 * Implements hook_migration_plugins_alter().
 */
function pb_girl_info_migration_plugins_alter(array &$migrations)
{
  foreach (array_keys($migrations) as $plugin_id) {
    $definitions[$plugin_id]['idMap'] = ['plugin' => 'null'];
  }
}

/**
 * Mmigration processor callback for timestamp fields.
 *
 * @see migrate_plus.migration.pb_girl_info.yml
 *
 * @param string $item
 *   The date string.
 *
 * @return int
 *   The timestamp.
 */
function pb_girl_info_strtotime_migrate_callback($item)
{
  if (!is_null($item) && $item != '') {
    return strtotime($item);
  }
  return 0;
}

/**
 * Migration processor callback for date fields.
 *
 * @see migrate_plus.migration.pb_girl_info.yml
 *
 * @param string $item
 *   The date string.
 *
 * @return string
 *   The formatted date string.
 */
function pb_girl_info_date_field_migrate_callback($item)
{
  if (!is_null($item) && $item !== '') {
    $date = date('Y-m-d\TH:i:s', strtotime($item));
    return $date;
  }
}

/**
 * Processor callback to create taxonomy terms if not exist and return them.
 *
 * @see migrate_plus.migration.pb_girl_info_gallery.yml
 *
 * @param $item
 *   The term name.
 *
 * @return int
 *   The term id.
 */
function pb_girl_info_get_or_create_term_callback($tag)
{
  $taxonomy_storage = \Drupal::entityTypeManager()->getStorage('taxonomy_term');
  $term = $taxonomy_storage->loadByProperties([
    'name' => $tag['name'],
    'vid' => 'tags',
  ]);

  if (empty($term)) {
    $term = $taxonomy_storage->create([
      'name' => $tag['name'],
      'vid' => 'tags',
      'status' => $tag['state'] === 1,
    ]);
    $term->save();
    $tid = $term->id();
  } else {
    foreach ($term as $t) {
      if ($tag['state'] === 1) {
        $t->setPublished();
      } else {
        $t->setUnpublished();
      }
      $t->save();
    }
    $tid = current($term)->id();
  }

  return $tid;
}


function pb_girl_info_get_or_create_term_callback_country_ref($code)
{
  $countries = [
    "AF" => "Afghanistan",
    "EG" => "Ägypten",
    "AL" => "Albanien",
    "DZ" => "Algerien",
    "AD" => "Andorra",
    "AO" => "Angola",
    "AI" => "Anguilla",
    "AQ" => "Antarktis",
    "AG" => "Antigua und Barbuda",
    "GQ" => "Äquatorial Guinea",
    "AR" => "Argentinien",
    "AM" => "Armenien",
    "AW" => "Aruba",
    "AZ" => "Aserbaidschan",
    "ET" => "Äthiopien",
    "AU" => "Australien",
    "BS" => "Bahamas",
    "BH" => "Bahrain",
    "BD" => "Bangladesh",
    "BB" => "Barbados",
    "BE" => "Belgien",
    "BZ" => "Belize",
    "BJ" => "Benin",
    "BM" => "Bermudas",
    "BT" => "Bhutan",
    "MM" => "Birma",
    "BO" => "Bolivien",
    "BA" => "Bosnien-Herzegowina",
    "BW" => "Botswana",
    "BV" => "Bouvet Inseln",
    "BR" => "Brasilien",
    "IO" => "Britisch-Indischer Ozean",
    "BN" => "Brunei",
    "BG" => "Bulgarien",
    "BF" => "Burkina Faso",
    "BI" => "Burundi",
    "CL" => "Chile",
    "CN" => "China",
    "CX" => "Christmas Island",
    "CK" => "Cook Inseln",
    "CR" => "Costa Rica",
    "DK" => "Dänemark",
    "DE" => "Deutschland",
    "DJ" => "Djibuti",
    "DM" => "Dominika",
    "DO" => "Dominikanische Republik",
    "EC" => "Ecuador",
    "SV" => "El Salvador",
    "CI" => "Elfenbeinküste",
    "ER" => "Eritrea",
    "EE" => "Estland",
    "FK" => "Falkland Inseln",
    "FO" => "Färöer Inseln",
    "FJ" => "Fidschi",
    "FI" => "Finnland",
    "FR" => "Frankreich",
    "GF" => "französisch Guyana",
    "PF" => "Französisch Polynesien",
    "TF" => "Französisches Süd-Territorium",
    "GA" => "Gabun",
    "GM" => "Gambia",
    "GE" => "Georgien",
    "GH" => "Ghana",
    "GI" => "Gibraltar",
    "GD" => "Grenada",
    "GR" => "Griechenland",
    "GL" => "Grönland",
    "UK" => "Großbritannien",
    "GB" => "Großbritannien (UK)",
    "GP" => "Guadeloupe",
    "GU" => "Guam",
    "GT" => "Guatemala",
    "GN" => "Guinea",
    "GW" => "Guinea Bissau",
    "GY" => "Guyana",
    "HT" => "Haiti",
    "HM" => "Heard und McDonald Islands",
    "HN" => "Honduras",
    "HK" => "Hong Kong",
    "IN" => "Indien",
    "ID" => "Indonesien",
    "IQ" => "Irak",
    "IR" => "Iran",
    "IE" => "Irland",
    "IS" => "Island",
    "IL" => "Israel",
    "IT" => "Italien",
    "JM" => "Jamaika",
    "JP" => "Japan",
    "YE" => "Jemen",
    "JO" => "Jordanien",
    "YU" => "Jugoslawien",
    "KY" => "Kaiman Inseln",
    "KH" => "Kambodscha",
    "CM" => "Kamerun",
    "CA" => "Kanada",
    "CV" => "Kap Verde",
    "KZ" => "Kasachstan",
    "KE" => "Kenia",
    "KG" => "Kirgisistan",
    "KI" => "Kiribati",
    "CC" => "Kokosinseln",
    "CO" => "Kolumbien",
    "KM" => "Komoren",
    "CG" => "Kongo",
    "CD" => "Demokratische Republik Kongo",
    "HR" => "Kroatien",
    "CU" => "Kuba",
    "KW" => "Kuwait",
    "LA" => "Laos",
    "LS" => "Lesotho",
    "LV" => "Lettland",
    "LB" => "Libanon",
    "LR" => "Liberia",
    "LY" => "Libyen",
    "LI" => "Liechtenstein",
    "LT" => "Litauen",
    "LU" => "Luxemburg",
    "MO" => "Macao",
    "MG" => "Madagaskar",
    "MW" => "Malawi",
    "MY" => "Malaysia",
    "MV" => "Malediven",
    "ML" => "Mali",
    "MT" => "Malta",
    "MP" => "Marianen",
    "MA" => "Marokko",
    "MH" => "Marshall Inseln",
    "MQ" => "Martinique",
    "MR" => "Mauretanien",
    "MU" => "Mauritius",
    "YT" => "Mayotte",
    "MK" => "Mazedonien",
    "MX" => "Mexiko",
    "FM" => "Mikronesien",
    "MZ" => "Mocambique",
    "MD" => "Moldavien",
    "MC" => "Monaco",
    "MN" => "Mongolei",
    "MS" => "Montserrat",
    "NA" => "Namibia",
    "NR" => "Nauru",
    "NP" => "Nepal",
    "NC" => "Neukaledonien",
    "NZ" => "Neuseeland",
    "NI" => "Nicaragua",
    "NL" => "Niederlande",
    "AN" => "Niederländische Antillen",
    "NE" => "Niger",
    "NG" => "Nigeria",
    "NU" => "Niue",
    "KP" => "Nord Korea",
    "NF" => "Norfolk Inseln",
    "NO" => "Norwegen",
    "OM" => "Oman",
    "AT" => "Österreich",
    "PK" => "Pakistan",
    "PS" => "Palästina",
    "PW" => "Palau",
    "PA" => "Panama",
    "PG" => "Papua Neuguinea",
    "PY" => "Paraguay",
    "PE" => "Peru",
    "PH" => "Philippinen",
    "PN" => "Pitcairn",
    "PL" => "Polen",
    "PT" => "Portugal",
    "PR" => "Puerto Rico",
    "QA" => "Qatar",
    "RE" => "Reunion",
    "RW" => "Ruanda",
    "RO" => "Rumänien",
    "RU" => "Russland",
    "LC" => "Saint Lucia",
    "ZM" => "Sambia",
    "AS" => "Samoa",
    "WS" => "Samoa",
    "SM" => "San Marino",
    "ST" => "Sao Tome",
    "SA" => "Saudi Arabien",
    "SE" => "Schweden",
    "CH" => "Schweiz",
    "SN" => "Senegal",
    "SC" => "Seychellen",
    "SL" => "Sierra Leone",
    "SG" => "Singapur",
    "SK" => "Slowakei",
    "SI" => "Slowenien",
    "SB" => "Solomon Inseln",
    "SO" => "Somalia",
    "GS" => "Südgeorgien und die Südlichen Sandwichinseln",
    "ES" => "Spanien",
    "LK" => "Sri Lanka",
    "SH" => "St. Helena",
    "KN" => "St. Kitts Nevis Anguilla",
    "PM" => "St. Pierre und Miquelon",
    "VC" => "St. Vincent",
    "KR" => "Süd Korea",
    "ZA" => "Südafrika",
    "SD" => "Sudan",
    "SR" => "Surinam",
    "SJ" => "Svalbard und Jan Mayen Islands",
    "SZ" => "Swasiland",
    "SY" => "Syrien",
    "TJ" => "Tadschikistan",
    "TW" => "Taiwan",
    "TZ" => "Tansania",
    "TH" => "Thailand",
    "TP" => "Timor",
    "TG" => "Togo",
    "TK" => "Tokelau",
    "TO" => "Tonga",
    "TT" => "Trinidad Tobago",
    "TD" => "Tschad",
    "CZ" => "Tschechische Republik",
    "TN" => "Tunesien",
    "TR" => "Türkei",
    "TM" => "Turkmenistan",
    "TC" => "Turks und Kaikos Inseln",
    "TV" => "Tuvalu",
    "UG" => "Uganda",
    "UA" => "Ukraine",
    "HU" => "Ungarn",
    "UY" => "Uruguay",
    "UZ" => "Usbekistan",
    "VU" => "Vanuatu",
    "VA" => "Vatikan",
    "VE" => "Venezuela",
    "AE" => "Vereinigte Arabische Emirate",
    "US" => "Vereinigte Staaten von Amerika",
    "VN" => "Vietnam",
    "VG" => "Virgin Island (Brit.)",
    "VI" => "Virgin Island (USA)",
    "WF" => "Wallis et Futuna",
    "BY" => "Weissrussland",
    "EH" => "Westsahara",
    "CF" => "Zentralafrikanische Republik",
    "ZW" => "Zimbabwe",
    "CY" => "Zypern"
  ];
  $country = isset($countries[strtoupper($code)]) ? $countries[strtoupper($code)] : $code;
  if (is_null($country) || $country == '') {
    return;
  }
  $taxonomy_storage = \Drupal::entityTypeManager()->getStorage('taxonomy_term');
  $term = $taxonomy_storage->loadByProperties([
    'name' => $country,
    'vid' => 'country',
  ]);

  if (empty($term)) {
    $term = $taxonomy_storage->create([
      'name' => $country,
      'vid' => 'country',
      'status' => 1,
    ]);
    $term->save();
    $tid = $term->id();
  } else {
    $tid = current($term)->id();
  }

  return $tid;
}


function pb_girl_info_get_or_create_term_callback_country($country)
{
  if (is_null($country) || $country == "") {
    return;
  }
  $taxonomy_storage = \Drupal::entityTypeManager()->getStorage('taxonomy_term');
  $term = $taxonomy_storage->loadByProperties([
    'name' => $country,
    'vid' => 'country',
  ]);

  if (empty($term)) {
    $term = $taxonomy_storage->create([
      'name' => $country,
      'vid' => 'country',
      'status' => 1,
    ]);
    $term->save();
    $tid = $term->id();
  } else {
    $tid = current($term)->id();
  }

  return $tid;
}

/**
 * Custom function to print custom_access to public API. We need to overwrite the field for firstname, since we don't know a better way
 */
function pb_girl_info_views_pre_render(\Drupal\views\ViewExecutable $view)
{
  if (in_array($view->id(), ['public_api_get_girl_infos', 'public_api_get_girl_info', 'public_api_get_home_featured'])) {
    $results = $view->result;
    foreach ($results as &$result) {
      $customAccess = $result->_entity->get('custom_access')->value;
      $firstname = $result->_entity->get('firstname')->value;
      $result->_entity->set('firstname', $customAccess ? 'true' : 'false');
      $firstname = $result->_entity->get('firstname')->value;
    }
  }
}

function pb_girl_info_entity_access(\Drupal\Core\Entity\EntityInterface $entity, $operation, \Drupal\Core\Session\AccountInterface $account)
{
  /** @var \Drupal\pb_girl\Entity\GirlInterface $entity */

  // custom access check for media entities
  $roles = $account->getRoles();
  if ($entity->getEntityTypeId() == 'girl_info' && in_array('subscriber', $roles)) {
    $entity->set('custom_access', true);
  } elseif ($entity->getEntityTypeId() == 'media' && in_array('plus', $roles)) {
    if ($entity->bundle() == 'gallery' && $operation == 'view') {
      $video = $entity->get('field_videos')->referencedEntities();
      if (isset($video) && !empty($video)) {
        return \Drupal\Core\Access\AccessResult::allowedIfHasPermission($account, 'view published plus gallery entities');
      }
      $girl_infos = \Drupal::entityTypeManager()->getStorage('girl_info')->loadByProperties([
        'galleries' => $entity->id(),
      ]);
      $girl_info = reset($girl_infos);
      if ($girl_info !== false) {
        if ($girl_info->get('field_plus_access')->value === "1") {
          // check for the special case that the gallery is a video gallery and the girl_info is whitelisted through girl des tages, then return false
          $video = $entity->get('field_videos')->referencedEntities();
          if (isset($video) && !empty($video)) {
            // load girl des tages IDs
            $node = \Drupal::entityTypeManager()->getStorage('node')->load(PlayboyPublicImageSaver::$node_id);
            $girl_des_tages_ids = array_column($node->get('field_girl_infos')->getValue(), 'target_id');
            $girl_des_tages_ids = array_merge(array_column($node->get('field_manual')->getValue(), 'target_id'), $girl_des_tages_ids);

            if (in_array($girl_info->id(), $girl_des_tages_ids)) {
              return \Drupal\Core\Access\AccessResult::allowedIfHasPermission($account, 'administer girl info entities');
            }
          }

          return \Drupal\Core\Access\AccessResult::allowedIfHasPermission($account, 'view published plus gallery entities');
        }
      }
    } elseif ($entity->bundle() == 'image' && $operation == 'view') {
      $galleries = \Drupal::entityTypeManager()->getStorage('media')->loadByProperties([
        'bundle' => 'gallery',
        'field_media_slideshow' => $entity->id(),
      ]);
      $access = false;
      $gallery = reset($galleries);
      if ($gallery !== false) {
        foreach ($galleries as $gallery) {
          $girl_infos = \Drupal::entityTypeManager()->getStorage('girl_info')->loadByProperties([
            'galleries' => $gallery->id(),
          ]);
          $girl_info = reset($girl_infos);
          if ($girl_info !== false) {
            if ($girl_info->get('field_plus_access')->value === "1") {
              $access = true;
            }
          }
        }
        if ($access) {
          return \Drupal\Core\Access\AccessResult::allowedIfHasPermission($account, 'view published plus gallery entities');
        }
      } else {
        $nexx_videos = \Drupal::entityTypeManager()->getStorage('media')->loadByProperties([
          'bundle' => 'nexx_video',
          'field_preview_image' => $entity->id(),
        ]);
        $nexx_video = reset($nexx_videos);
        if ($nexx_video !== false) {
          return \Drupal\Core\Access\AccessResult::allowedIfHasPermission($account, 'view published plus gallery entities');
        }
      }
    }
  }
}

function pb_girl_info_entity_base_field_info(EntityTypeInterface $entity_type)
{
  $fields = [];

  if ($entity_type->id() === 'girl_info') {
    // create a custom computed boolean field in drupal
    $fields['custom_access'] = BaseFieldDefinition::create('boolean')
      ->setLabel(t('Custom access'))
      ->setDescription(t('Custom access field'))
      // Mark this field as computed.
      ->setComputed(TRUE)
      // Mark your field as read only since it's computed we will never populate it.
      ->setReadOnly(TRUE)
      // Set our class as field class.
      ->setClass(AccessComputedField::class)
      ->setDisplayConfigurable('view', TRUE)
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayOptions('view', [
        'label'  => 'above',
        'type'   => 'string',
        'weight' => 0,
      ]);
  }

  return $fields;
}
