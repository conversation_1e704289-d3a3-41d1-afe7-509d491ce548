{#
/**
 * @file girl-vote.html.twig
 * Default theme implementation to present Girl vote data.
 *
 * This template is used when viewing Girl vote pages.
 *
 *
 * Available variables:
 * - content: A list of content items. Use 'content' to print all content, or
 * - attributes: HTML attributes for the container element.
 *
 * @see template_preprocess_girl_vote()
 *
 * @ingroup themeable
 */
#}
<div{{attributes.addClass('girl')}}>
	{{ elements }}
</div>
