<?php

namespace Drupal\pb_girl;

use <PERSON><PERSON>al\Core\Entity\EntityAccessControlHandler;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\Access\AccessResult;

/**
 * Access controller for the Girl entity.
 *
 * @see \Drupal\pb_girl\Entity\Girl.
 */
class GirlAccessControlHandler extends EntityAccessControlHandler {

  /**
   * {@inheritdoc}
   */
  protected function checkAccess(EntityInterface $entity, $operation, AccountInterface $account) {
    /** @var \Drupal\pb_girl\Entity\GirlInterface $entity */

    switch ($operation) {

      case 'view':

        if (!$entity->isPublished()) {
          return AccessResult::allowedIfHasPermission($account, 'view unpublished girl entities');
        }

        return AccessResult::allowedIfHasPermission($account, 'view published girl entities');

      case 'update':

        return AccessResult::allowedIfHasPermission($account, 'edit girl entities');

      case 'delete':

        return AccessResult::allowedIfHasPermission($account, 'delete girl entities');
    }

    // Unknown operation, no opinion.
    return AccessResult::neutral();
  }

  /**
   * {@inheritdoc}
   */
  protected function checkCreateAccess(AccountInterface $account, array $context, $entity_bundle = NULL) {
    return AccessResult::allowedIfHasPermission($account, 'add girl entities');
  }

}
