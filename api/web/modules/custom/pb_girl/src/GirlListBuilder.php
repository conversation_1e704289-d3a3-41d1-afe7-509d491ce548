<?php

namespace Drupal\pb_girl;

use <PERSON>upal\Core\Entity\EntityInterface;
use Drupal\Core\Entity\EntityListBuilder;
use <PERSON>upal\Core\Link;

/**
 * Defines a class to build a listing of Girl entities.
 *
 * @ingroup pb_girl
 */
class GirlListBuilder extends EntityListBuilder {

  /**
   * {@inheritdoc}
   */
  public function buildHeader() {
    $header['id'] = $this->t('Girl ID');
    $header['name'] = $this->t('Name');
    return $header + parent::buildHeader();
  }

  /**
   * {@inheritdoc}
   */
  public function buildRow(EntityInterface $entity) {
    /* @var \Drupal\pb_girl\Entity\Girl $entity */
    $row['id'] = $entity->id();
    $row['name'] = Link::createFromRoute(
      $entity->label(),
      'entity.girl.edit_form',
      ['girl' => $entity->id()]
    );
    return $row + parent::buildRow($entity);
  }

}
