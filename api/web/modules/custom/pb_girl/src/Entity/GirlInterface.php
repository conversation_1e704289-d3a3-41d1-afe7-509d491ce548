<?php

namespace Dr<PERSON>al\pb_girl\Entity;

use <PERSON><PERSON><PERSON>\Core\Entity\ContentEntityInterface;
use <PERSON><PERSON>al\Core\Entity\EntityChangedInterface;
use <PERSON><PERSON>al\Core\Entity\EntityPublishedInterface;

/**
 * Provides an interface for defining Girl entities.
 *
 * @ingroup pb_girl
 */
interface GirlInter<PERSON> extends ContentEntityInterface, EntityChangedInterface, EntityPublishedInterface
{

  /**
   * Add get/set methods for your configuration properties here.
   */

  /**
   * Gets the Girl name.
   *
   * @return string
   *   Name of the Girl.
   */
  public function getName();

  /**
   * Sets the Girl name.
   *
   * @param string $name
   *   The Girl name.
   *
   * @return \Drupal\pb_girl\Entity\GirlInterface
   *   The called Girl entity.
   */
  public function setName($name);

  /**
   * Gets the Girl creation timestamp.
   *
   * @return int
   *   Creation timestamp of the Girl.
   */
  public function getCreatedTime();

  /**
   * Sets the Girl creation timestamp.
   *
   * @param int $timestamp
   *   The Girl creation timestamp.
   *
   * @return \Drupal\pb_girl\Entity\GirlInterface
   *   The called Girl entity.
   */
  public function setCreatedTime($timestamp);
}