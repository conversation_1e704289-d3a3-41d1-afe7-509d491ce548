<?php

namespace Drupal\pb_girl\Entity;

use Drupal\Core\Field\BaseFieldDefinition;
use Drupal\Core\Entity\ContentEntityBase;
use Drupal\Core\Entity\EntityChangedTrait;
use Drupal\Core\Entity\EntityPublishedTrait;
use Drupal\Core\Entity\EntityTypeInterface;
use Drupal\Core\Field\FieldStorageDefinitionInterface;

/**
 * Defines the Girl entity.
 *
 * @ingroup pb_girl
 *
 * @ContentEntityType(
 *   id = "girl",
 *   label = @Translation("Girl"),
 *   handlers = {
 *     "view_builder" = "Drupal\Core\Entity\EntityViewBuilder",
 *     "list_builder" = "Drupal\pb_girl\GirlListBuilder",
 *     "views_data" = "Drupal\pb_girl\Entity\GirlViewsData",
 *     "translation" = "Drupal\pb_girl\GirlTranslationHandler",
 *
 *     "form" = {
 *       "default" = "Drupal\pb_girl\Form\GirlForm",
 *       "add" = "Drupal\pb_girl\Form\GirlForm",
 *       "edit" = "Drupal\pb_girl\Form\GirlForm",
 *       "delete" = "Drupal\pb_girl\Form\GirlDeleteForm",
 *     },
 *     "route_provider" = {
 *       "html" = "Drupal\pb_girl\GirlHtmlRouteProvider",
 *     },
 *     "access" = "Drupal\pb_girl\GirlAccessControlHandler",
 *   },
 *   base_table = "girl",
 *   data_table = "girl_field_data",
 *   translatable = TRUE,
 *   admin_permission = "administer girl entities",
 *   entity_keys = {
 *     "id" = "id",
 *     "label" = "name",
 *     "uuid" = "uuid",
 *     "langcode" = "langcode",
 *     "published" = "status",
 *   },
 *   links = {
 *     "canonical" = "/admin/structure/girl/{girl}",
 *     "add-form" = "/admin/structure/girl/add",
 *     "edit-form" = "/admin/structure/girl/{girl}/edit",
 *     "delete-form" = "/admin/structure/girl/{girl}/delete",
 *     "collection" = "/admin/structure/girl",
 *   },
 *   field_ui_base_route = "girl.settings"
 * )
 */
class Girl extends ContentEntityBase implements GirlInterface
{

  use EntityChangedTrait;
  use EntityPublishedTrait;

  /**
   * {@inheritdoc}
   */
  public function getName()
  {
    return $this->get('name')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setName($name)
  {
    $this->set('name', $name);
    return $this;
  }

  /**
   * {@inheritdoc}
   */
  public function getCreatedTime()
  {
    return $this->get('created')->value;
  }

  /**
   * {@inheritdoc}
   */
  public function setCreatedTime($timestamp)
  {
    $this->set('created', $timestamp);
    return $this;
  }

  private static function createIntegerField($label, $description, $required)
  {
    return BaseFieldDefinition::create('integer')
      ->setLabel($label)
      ->setDescription($description)
      ->setSettings([
        'max_length' => 50,
        'text_processing' => 0,
      ])
      ->setDefaultValue('')
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'number',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired($required);
  }

  private static function createStringField($label, $description, $required)
  {
    return BaseFieldDefinition::create('string')
      ->setLabel($label)
      ->setDescription($description)
      ->setSettings([
        'max_length' => 50,
        'text_processing' => 0,
      ])
      ->setDefaultValue('')
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'string_textfield',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired($required);
  }

  private static function createTextField($label, $description, $required)
  {
    return BaseFieldDefinition::create('string_long')
      ->setLabel($label)
      ->setDescription($description)
      ->setSettings([
        'text_processing' => 0,
      ])
      ->setDefaultValue('')
      ->setDisplayOptions('view', [
        'label' => 'hidden',
        'type' => 'text_default',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'text_textarea',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired($required);
  }

  /**
   * {@inheritdoc}
   */
  public static function baseFieldDefinitions(EntityTypeInterface $entity_type)
  {
    $fields = parent::baseFieldDefinitions($entity_type);

    // Add the published field.
    $fields += static::publishedBaseFieldDefinitions($entity_type);

    $fields['name'] = BaseFieldDefinition::create('string')
      ->setLabel(t('Name'))
      ->setDescription(t('The name of the Girl.'))
      ->setSettings([
        'max_length' => 50,
        'text_processing' => 0,
      ])
      ->setDefaultValue('')
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'string_textfield',
        'weight' => -4,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['firstname'] = Girl::createStringField(t('Firstname'), t('The firstname of the Girl.'), FALSE);
    $fields['middlename'] = Girl::createStringField(t('Middlename'), t('The middlename of the Girl.'), FALSE);
    $fields['lastname'] = Girl::createStringField(t('Lastname'), t('The lastname of the Girl.'), FALSE);

    $fields['main_images'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel(t('Main images'))
      ->setDescription(t('Main images of the girl.'))
      ->setSettings([
        'target_type' => 'media',
        'handler_settings' => [
          'target_bundles' => [
            'image' => 'image',
          ],
        ],
      ])
      ->setCardinality(FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED)
      ->setDisplayOptions('form', [
        'type' => 'image_image',
        'weight' => -4
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['non_nude_images'] = BaseFieldDefinition::create('entity_reference')
      ->setLabel(t('Non-nude images'))
      ->setDescription(t('Non-nude images of the girl.'))
      ->setSettings([
        'target_type' => 'media',
        'handler_settings' => [
          'target_bundles' => [
            'image' => 'image',
          ],
        ],
      ])
      ->setCardinality(FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED)
      ->setDisplayOptions('form', [
        'type' => 'image_image',
        'weight' => -4
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayConfigurable('view', TRUE)
      ->setRequired(FALSE);

    $fields['status']->setDescription(t('A boolean indicating whether the Girl is published.'))
      ->setDisplayOptions('form', [
        'type' => 'boolean_checkbox',
        'weight' => -3,
      ]);

    $fields['created'] = BaseFieldDefinition::create('created')
      ->setLabel(t('Created'))
      ->setDescription(t('The time that the entity was created.'));

    $fields['changed'] = BaseFieldDefinition::create('changed')
      ->setLabel(t('Changed'))
      ->setDescription(t('The time that the entity was last edited.'));

    return $fields;
  }
}