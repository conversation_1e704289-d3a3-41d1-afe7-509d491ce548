# Playboy Girl Entity

## Docksal installation
```
  fin init
```

## Nativ installation
```
  cp runner.yml.dist runner.yml
  # Adjust base_url and database connection in runner.yml
  composer install
  ./vendor/bin/run drupal:site-install
```

## Import
### Import all girl entities
```
drush mim pb_girls
```
### Rollback all girl entities
```
drush mr pb_girls
```
### Create a girl vote entity
```
drush pb:gv:create YYYY-MM-DD
```
### Evaluate and set the winner of a past girl vote
```
drush pb:gv:set-winner YYYY-MM-DD
```
