<?php

namespace Drupal\girl_info_image_mover\Batch;

use <PERSON><PERSON><PERSON>\Core\File\FileSystemInterface;
use <PERSON>upal\file\Entity\File;

class ImageMover
{

  public static function move($image, $plus, &$context)
  {
    // if the image has the media image field
    if ($image->hasField('field_media_image') || $image->hasField('field_preview_image')) {
      $file = $image->hasField('field_media_image') ? $image->get('field_media_image')->entity : $image->get('field_preview_image')->entity->get('field_media_image')->entity;

      // if the file is a File
      if ($file instanceof \Drupal\file\Entity\File) {

        // get full URI of file
        $filepath = $file->getFileUri();
        $file_system = \Drupal::service('file_system');
        $moved_uri = '';

        // if plus is requested but the path is not containing 'plus', move the file to the plus folder
        if ($plus === true) {

          // regular move flow
          if (strpos($filepath, 'plus') === false) {
            // new private folder path
            $plus_filepath = 'private://plus/' . date('Y') . '-' . date('m');
            $file_system->prepareDirectory($plus_filepath, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
            $plus_absolute_filepath = $plus_filepath . '/' . basename($filepath);

            // copy the file to the private folder as is for the first image
            try {
              $moved_uri = $file_system->move($filepath, $plus_absolute_filepath, FileSystemInterface::EXISTS_REPLACE);
            } catch (\Throwable $th) {
              \Drupal::logger('girl_info_image_mover')->error($filepath . ' -> ' . $plus_absolute_filepath);
              \Drupal::logger('girl_info_image_mover')->error($th->getMessage());
              // check if the reason for the error is that the file is missing on the filesystem
              if (!file_exists($filepath)) {
                \Drupal::logger('girl_info_image_mover')->notice('File not found on filesystem: @file', ['@file' => $filepath]);
                // now check, maybe is already in the right location
                if (file_exists($plus_absolute_filepath)) {
                  \Drupal::logger('girl_info_image_mover')->notice('BUT file found in the wanted location: @file', ['@file' => $filepath]);
                  // if the file exists in the all access folder, set the moved_uri to the all access filepath
                  $moved_uri = $plus_absolute_filepath;
                }
              }
            }
          } else {
            // if the file is already in the plus folder, just set the moved_uri to the current filepath
            $moved_uri = $filepath;
          }
        } else if ($plus === false) {

          // regular move flow
          // if the file is in the plus folder, move it to the regular folder
          if (strpos($filepath, 'plus') !== false) {
            // if plus is not requested but the path is containing 'plus', move the file to the regular folder
            $all_access_filepath = 'private://' . date('Y') . '-' . date('m');
            $file_system->prepareDirectory($all_access_filepath, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);
            $all_access_absolute_filepath = $all_access_filepath . '/' . basename($filepath);

            // copy the file to the public folder as is for the first image
            try {
              $moved_uri = $file_system->move($filepath, $all_access_absolute_filepath, FileSystemInterface::EXISTS_REPLACE);
            } catch (\Throwable $th) {
              \Drupal::logger('girl_info_image_mover')->error($filepath . ' -> ' . $all_access_absolute_filepath);
              \Drupal::logger('girl_info_image_mover')->error($th->getMessage());
              // check if the reason for the error is that the file is missing on the filesystem
              if (!file_exists($filepath)) {
                \Drupal::logger('girl_info_image_mover')->notice('File not found on filesystem: @file', ['@file' => $filepath]);
                // now check, maybe is already in the right location
                if (file_exists($all_access_absolute_filepath)) {
                  \Drupal::logger('girl_info_image_mover')->notice('BUT file found in the wanted location: @file', ['@file' => $filepath]);
                  // if the file exists in the all access folder, set the moved_uri to the all access filepath
                  $moved_uri = $all_access_absolute_filepath;
                }
              }
            }
          } else {
            // if the file is not in the plus folder, just set the moved_uri to the current filepath
            $moved_uri = $filepath;
          }
        }

        // if the file was moved successfully, update the file entity
        // and set the new URI
        if ($moved_uri !== '') {
          // Load the file entity.
          $fids = \Drupal::entityQuery('file')
            ->condition('uri', $filepath)
            ->execute();

          if (!empty($fids)) {
            $file = File::load(reset($fids));
            if ($file) {
              // Update and save the new URI.
              try {
                $file->setFileUri($moved_uri);
                $file->save();
                $context['message'] = t('File moved and database updated: @file', ['@file' => $moved_uri]);
              } catch (\Throwable $th) {
                \Drupal::logger('girl_info_image_mover')->error($file->id() . ': ' . $file->getFilename());
                \Drupal::logger('girl_info_image_mover')->error($th->getMessage());
                $context['message'] = t('File moved but updating the database failed: @file', ['@file' => $moved_uri]);
              }
            } else {
              $context['message'] = t('File moved but file entity not found: @file', ['@file' => $moved_uri]);
            }
          } else {
            $context['message'] = t('File moved but file not found in database: @file', ['@file' => $moved_uri]);
          }
        } else {
          $context['message'] = t('File could not be moved: @file', ['@file' => $filepath]);
        }
      }
    } else {
      $context['message'] = t('File fields missing');
    }

    // debugging for CLI
    if (PHP_SAPI === 'cli') {
      if (isset($context['message'])) {
        echo $context['message'] . "\n";
      } else {
        echo t('No message set for image: @image', ['@image' => $image->id()]) . "\n";
      }
    }
  }

  public static function finished($success, $results, $operations)
  {
    if ($success) {
      \Drupal::messenger()->addStatus(t('All images moved successfully.'));
    } else {
      \Drupal::messenger()->addError(t('Some images could not be moved.'));
    }
  }
}
