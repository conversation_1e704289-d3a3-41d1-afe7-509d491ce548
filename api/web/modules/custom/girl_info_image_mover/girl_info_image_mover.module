<?php

use <PERSON><PERSON><PERSON>\node\NodeInterface;
use Dr<PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\pb_girl_info\Entity\GirlInfo;

/**
 * Implements hook_entity_presave().
 */
function girl_info_image_mover_entity_presave(\Drupal\Core\Entity\EntityInterface $entity)
{
  if ($entity->getEntityTypeId() == 'girl_info' && $entity->bundle() == 'girl_info') {
    // Flag the node for post-save processing.
    $entity->needs_image_move = TRUE;
  }
}

/**
 * Implements hook_entity_insert() and hook_entity_update().
 */
function girl_info_image_mover_entity_insert(\Drupal\Core\Entity\EntityInterface $entity)
{
  if (isset($entity->needs_image_move)) {
    girl_info_image_mover_trigger_batch($entity);
  }
}

function girl_info_image_mover_entity_update(\Drupal\Core\Entity\EntityInterface $entity)
{
  if (isset($entity->needs_image_move)) {
    girl_info_image_mover_trigger_batch($entity);
  }
}

/**
 * Trigger the batch process.
 */
function girl_info_image_mover_trigger_batch(Drupal\Core\Entity\EntityInterface $entity)
{
  $girl_info = \Drupal::entityTypeManager()->getStorage('girl_info')->load($entity->id());

  // get the images from the gallery
  $galleries = $girl_info->get('galleries')->referencedEntities();
  $images = [];
  if (count($galleries) > 0) {
    // loop through the galleries
    foreach ($galleries as $gallery) {
      // get the images from the gallery
      $images = array_merge($images, $gallery->get('field_media_slideshow')->referencedEntities());
      $images = array_merge($images, $gallery->get('field_videos')->referencedEntities());
    }
  }

  // if is cli then run immediately
  // otherwise, prepare the batch operations
  $is_cli = PHP_SAPI === 'cli';
  $operations = [];

  foreach ($images as $image) {
    $plus = false;
    $girl_info_entities = get_girl_info_entities_referencing_image($image->id());
    if ($girl_info_entities) {
      foreach ($girl_info_entities as $girl_info_entity) {
        if ($girl_info_entity->get('field_plus_access')->value === "1") {
          $plus = true;
          break;
        }
      }
    }

    if ($is_cli) {
      $context = [];
      // If running in CLI mode, call the move function directly.
      \Drupal\girl_info_image_mover\Batch\ImageMover::move(
        $image,
        $plus,
        $context
      );
    } else {
      $operations[] = [
        '\Drupal\girl_info_image_mover\Batch\ImageMover::move',
        [
          $image,
          $plus,
        ],
      ];
    }
  }

  if (!$is_cli) {
    $batch = [
      'title' => t('Moving images...'),
      'operations' => $operations,
      'finished' => '\Drupal\girl_info_image_mover\Batch\ImageMover::finished',
    ];

    batch_set($batch);
  }
}

function get_girl_info_entities_referencing_image($id)
{
  $query = \Drupal::entityQuery('media')
    ->condition('bundle', 'gallery');

  // Create the OR group.
  $or_group = $query->orConditionGroup()
    ->condition('field_media_slideshow.target_id', $id)
    ->condition('field_videos.target_id', $id);

  $query->condition($or_group);
  $gallery_ids = $query->execute();

  if (empty($gallery_ids)) {
    return [];
  }

  $girl_info_ids = [];

  // load the girl info entities that reference the galleries
  foreach ($gallery_ids as $gallery_id) {
    $query = \Drupal::entityQuery('girl_info')
      ->condition('galleries.target_id', $gallery_id);
    $girl_info_ids = array_merge($girl_info_ids, $query->execute());
  }

  $girl_infos = \Drupal::entityTypeManager()->getStorage('girl_info')->loadMultiple($girl_info_ids);
  return $girl_infos;
}
