<?php

/**
 * @file
 * Primary module hooks for BunnyCdn module.
 *
 * @DCG
 * This file is no longer required in Drupal 8.
 * @see https://www.drupal.org/node/2217931
 */


function bunnycdn_file_url_alter(&$uri)
{

  $cdn1 = 'https://playboy-all-access.b-cdn.net';
  $cdn_extensions = array(
    'gif',
    'jpg',
    'jpeg',
    'png',
  );

  // Most CDNs don't support private file transfers without a lot of hassle,
  // so don't support this in the common case.
  $schemes = array(
    'public',
  );
  $scheme = \Drupal\Core\StreamWrapper\StreamWrapperManager::getScheme($uri);

  // Only serve shipped files and public created files from the CDN.
  if (!$scheme || in_array($scheme, $schemes)) {

    // Shipped files.
    if (!$scheme) {
      $path = $uri;
    } else {
      $wrapper = \Drupal::service('stream_wrapper_manager')
        ->getViaScheme($scheme);
      $path = $wrapper
        ->getDirectoryPath() . '/' . \Drupal\Core\StreamWrapper\StreamWrapperManager::getTarget($uri);
    }

    // Clean up Windows paths.
    $path = str_replace('\\', '/', $path);

    // Serve files with the given extension from the CDN
    $pathinfo = pathinfo($path);
    if (isset($pathinfo['extension']) && in_array(strtolower($pathinfo['extension']), $cdn_extensions)) {
      $uri = $cdn1 . '/' . rawurlencode($path);
    }
    return;
  }
}
