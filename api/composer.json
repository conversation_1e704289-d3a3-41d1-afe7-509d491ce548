{"name": "drupal/recommended-project", "description": "Project template for Drupal 9 projects with a relocated document root", "type": "project", "license": "GPL-2.0-or-later", "homepage": "https://www.drupal.org/project/drupal", "support": {"docs": "https://www.drupal.org/docs/user_guide/en/index.html", "chat": "https://www.drupal.org/node/314178"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}], "require": {"composer/installers": "^1.9", "cweagans/composer-patches": "^1.7", "drupal/admin_can_login_anyuser": "^1.2", "drupal/admin_toolbar": "^3.0", "drupal/autosave_form": "^1.2", "drupal/better_exposed_filters": "^5.0@beta", "drupal/config_split": "^1.7", "drupal/console": "^1.9", "drupal/cookie_samesite_support": "1.x-dev", "drupal/core-composer-scaffold": "^9.2", "drupal/core-project-message": "^9.2", "drupal/core-recommended": "^9.2", "drupal/devel_entity_updates": "^3.0", "drupal/download_count": "^2.0", "drupal/dropzonejs": "^2.5", "drupal/facets": "^1.8", "drupal/field_group": "^3.2", "drupal/flag": "^4.0@beta", "drupal/focal_point": "^1.5", "drupal/focal_point_focus": "^1.10", "drupal/fullcalendar_view": "^5.1", "drupal/gin": "^3.0@alpha", "drupal/graphql": "3.1", "drupal/graphql_search_api": "^1.2", "drupal/graphql_views": "^1.1", "drupal/image_field_to_media": "^1.0", "drupal/image_style_warmer": "^1.1", "drupal/inline_entity_form": "^1.0@RC", "drupal/jsonapi_extras": "^3.19", "drupal/keycloak": "^1.6", "drupal/login_history": "^1.0", "drupal/media_bulk_upload": "^1.0@alpha", "drupal/media_library_bulk_upload": "^1.0@alpha", "drupal/media_library_edit": "^2.1", "drupal/metatag": "^1.26", "drupal/migrate_file": "2.0.x-dev@dev", "drupal/migrate_file_to_media": "^2.0", "drupal/migrate_plus": "^5.1", "drupal/migrate_process_negate": "^1.0", "drupal/migrate_tools": "^5.0", "drupal/nexx_integration": "^3.4", "drupal/openid_connect": "^1.1", "drupal/paragraphs": "^1.12", "drupal/pathauto": "^1.8", "drupal/private_file_token": "^1.0@alpha", "drupal/redirect_after_login": "^2.7", "drupal/redirect_after_logout": "^1.3", "drupal/redis": "^1.5", "drupal/rest_views": "^2.0", "drupal/restui": "^1.20", "drupal/s3fs": "^3.0@beta", "drupal/scheduler": "^1.4", "drupal/scheduler_content_moderation_integration": "^1.3", "drupal/search_api_solr": "^4.2", "drupal/simple_oauth": "^5.0", "drupal/smtp": "^1.0", "drupal/svg_image": "^1.15", "drupal/term_merge": "1.x-dev", "drupal/token": "^1.9", "drupal/typed_data": "^1.0@alpha", "drupal/webform": "^6.0", "drupal/webform_rest": "^4.0", "platformsh/config-reader": "^2.4"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "dev", "prefer-stable": true, "config": {"sort-packages": true, "allow-plugins": {"composer/installers": true, "drupal/console-extend-plugin": true, "cweagans/composer-patches": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true}}, "extra": {"enable-patching": true, "patches": {"drupal/migrate_plus": {"Allow wildcard for URL source plugin [#2921374]": "https://www.drupal.org/files/issues/2019-07-31/migrate_plus-allow_wildcard_for_url_source_plugin-2921374-24.patch", "FileSystemInterface Fix": "patches/migrate_plus.filesystem.patch"}, "drupal/core": {"cors on statistics": "patches/statistics-cors.patch", "allow all entities to be tracked": "https://www.drupal.org/files/issues/2021-12-14/2532334_91.patch", "views fix for reverse entities": "https://www.drupal.org/files/issues/2021-08-24/provide-views-reverse-relationships-automatically-for-entity-base-fields-2706431-54.patch"}, "drupal/openid_connect": {"refresh_token": "https://www.drupal.org/files/issues/2020-07-23/openid_connect-2923419-2-refresh-tokens-method.patch", "remove session when logged": "patches/remove_session_when_logged.patch"}}, "drupal-scaffold": {"locations": {"web-root": "web/"}}, "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"]}, "drupal-core-project-message": {"include-keys": ["homepage", "support"], "post-create-project-cmd-message": ["<bg=blue;fg=white>                                                         </>", "<bg=blue;fg=white>  Congratulations, you’ve installed the Drupal codebase  </>", "<bg=blue;fg=white>  from the drupal/recommended-project template!          </>", "<bg=blue;fg=white>                                                         </>", "", "<bg=yellow;fg=black>Next steps</>:", "  * Install the site: https://www.drupal.org/docs/8/install", "  * Read the user guide: https://www.drupal.org/docs/user_guide/en/index.html", "  * Get support: https://www.drupal.org/support", "  * Get involved with the Drupal community:", "      https://www.drupal.org/getting-involved", "  * Remove the plugin that prints this message:", "      composer remove drupal/core-project-message"]}}, "require-dev": {"drush/drush": "^9.7.1 | ^10.0.0"}}