{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "4c1a6e497cf46ef31b14c8b7fe941c90", "packages": [{"name": "alchemy/zippy", "version": "0.4.9", "source": {"type": "git", "url": "https://github.com/alchemy-fr/Zippy.git", "reference": "59fbeefb9a249122867ef25e53addfcce31850d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alchemy-fr/Zippy/zipball/59fbeefb9a249122867ef25e53addfcce31850d7", "reference": "59fbeefb9a249122867ef25e53addfcce31850d7", "shasum": ""}, "require": {"doctrine/collections": "~1.0", "php": ">=5.5", "symfony/filesystem": "^2.0.5 || ^3.0 || ^4.0", "symfony/polyfill-mbstring": "^1.3", "symfony/process": "^2.1 || ^3.0 || ^4.0"}, "require-dev": {"ext-zip": "*", "guzzle/guzzle": "~3.0", "guzzlehttp/guzzle": "^6.0", "phpunit/phpunit": "^4.0 || ^5.0", "symfony/finder": "^2.0.5 || ^3.0 || ^4.0"}, "suggest": {"ext-zip": "To use the ZipExtensionAdapter", "guzzle/guzzle": "To use the GuzzleTeleporter with Guzzle 3", "guzzlehttp/guzzle": "To use the GuzzleTeleporter with Guzzle 6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.4.x-dev"}}, "autoload": {"psr-4": {"Alchemy\\Zippy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Alchemy", "email": "<EMAIL>", "homepage": "http://www.alchemy.fr/"}], "description": "<PERSON><PERSON><PERSON>, the archive manager companion", "keywords": ["bzip", "compression", "tar", "zip"], "support": {"issues": "https://github.com/alchemy-fr/Zippy/issues", "source": "https://github.com/alchemy-fr/Zippy/tree/master"}, "time": "2018-02-22T13:58:36+00:00"}, {"name": "asm89/stack-cors", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/asm89/stack-cors.git", "reference": "b9c31def6a83f84b4d4a40d35996d375755f0e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/stack-cors/zipball/b9c31def6a83f84b4d4a40d35996d375755f0e08", "reference": "b9c31def6a83f84b4d4a40d35996d375755f0e08", "shasum": ""}, "require": {"php": ">=5.5.9", "symfony/http-foundation": "~2.7|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^4.8.10", "squizlabs/php_codesniffer": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Asm89\\Stack\\": "src/Asm89/Stack/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library and stack middleware", "homepage": "https://github.com/asm89/stack-cors", "keywords": ["cors", "stack"], "support": {"issues": "https://github.com/asm89/stack-cors/issues", "source": "https://github.com/asm89/stack-cors/tree/1.3.0"}, "time": "2019-12-24T22:41:47+00:00"}, {"name": "aws/aws-crt-php", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "3942776a8c99209908ee0b287746263725685732"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/3942776a8c99209908ee0b287746263725685732", "reference": "3942776a8c99209908ee0b287746263725685732", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.0.2"}, "time": "2021-09-03T22:57:30+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.209.4", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "784e5ccc1b544db5018a41fc4dbfeda4c7d436b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/784e5ccc1b544db5018a41fc4dbfeda4c7d436b3", "reference": "784e5ccc1b544db5018a41fc4dbfeda4c7d436b3", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.0.2", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^5.3.3|^6.2.1|^7.0", "guzzlehttp/promises": "^1.4.0", "guzzlehttp/psr7": "^1.7.0|^2.0", "mtdowling/jmespath.php": "^2.6", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"Aws\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.209.4"}, "time": "2022-01-12T19:14:37+00:00"}, {"name": "composer/installers", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/d20a64ed3c94748397ff5973488761b22f6d3f19", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "tastyigniter", "typo3", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v1.12.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-09-13T08:19:44+00:00"}, {"name": "composer/semver", "version": "3.2.6", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "83e511e247de329283478496f7a1e114c9517506"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/83e511e247de329283478496f7a1e114c9517506", "reference": "83e511e247de329283478496f7a1e114c9517506", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.54", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.2.6"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-10-25T11:34:17+00:00"}, {"name": "consolidation/annotated-command", "version": "4.5.1", "source": {"type": "git", "url": "https://github.com/consolidation/annotated-command.git", "reference": "701a7abe8505abe89520837be798e15a3953a367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/annotated-command/zipball/701a7abe8505abe89520837be798e15a3953a367", "reference": "701a7abe8505abe89520837be798e15a3953a367", "shasum": ""}, "require": {"consolidation/output-formatters": "^4.1.1", "php": ">=7.1.3", "psr/log": "^1|^2", "symfony/console": "^4.4.8|^5|^6", "symfony/event-dispatcher": "^4.4.8|^5|^6", "symfony/finder": "^4.4.8|^5|^6"}, "require-dev": {"composer-runtime-api": "^2.0", "phpunit/phpunit": "^7.5.20 || ^8 || ^9", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\AnnotatedCommand\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Initialize Symfony Console commands from annotated command class methods.", "support": {"issues": "https://github.com/consolidation/annotated-command/issues", "source": "https://github.com/consolidation/annotated-command/tree/4.5.1"}, "time": "2021-12-30T04:00:37+00:00"}, {"name": "consolidation/output-formatters", "version": "4.2.1", "source": {"type": "git", "url": "https://github.com/consolidation/output-formatters.git", "reference": "4413d7c732afb5d7bdac565c41aa9c8c49c48888"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/output-formatters/zipball/4413d7c732afb5d7bdac565c41aa9c8c49c48888", "reference": "4413d7c732afb5d7bdac565c41aa9c8c49c48888", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "php": ">=7.1.3", "symfony/console": "^4|^5|^6", "symfony/finder": "^4|^5|^6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4.2", "phpunit/phpunit": ">=7", "squizlabs/php_codesniffer": "^3", "symfony/var-dumper": "^4|^5|^6", "symfony/yaml": "^4|^5|^6", "yoast/phpunit-polyfills": "^0.2.0"}, "suggest": {"symfony/var-dumper": "For using the var_dump formatter"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\OutputFormatters\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Format text by applying transformations provided by plug-in formatters.", "support": {"issues": "https://github.com/consolidation/output-formatters/issues", "source": "https://github.com/consolidation/output-formatters/tree/4.2.1"}, "time": "2021-12-30T03:58:00+00:00"}, {"name": "cweagans/composer-patches", "version": "1.7.1", "source": {"type": "git", "url": "https://github.com/cweagans/composer-patches.git", "reference": "9888dcc74993c030b75f3dd548bb5e20cdbd740c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweagans/composer-patches/zipball/9888dcc74993c030b75f3dd548bb5e20cdbd740c", "reference": "9888dcc74993c030b75f3dd548bb5e20cdbd740c", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3.0"}, "require-dev": {"composer/composer": "~1.0 || ~2.0", "phpunit/phpunit": "~4.6"}, "type": "composer-plugin", "extra": {"class": "cweagans\\Composer\\Patches"}, "autoload": {"psr-4": {"cweagans\\Composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a way to patch Composer packages.", "support": {"issues": "https://github.com/cweagans/composer-patches/issues", "source": "https://github.com/cweagans/composer-patches/tree/1.7.1"}, "time": "2021-06-08T15:12:46+00:00"}, {"name": "defuse/php-encryption", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/defuse/php-encryption.git", "reference": "77880488b9954b7884c25555c2a0ea9e7053f9d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/77880488b9954b7884c25555c2a0ea9e7053f9d2", "reference": "77880488b9954b7884c25555c2a0ea9e7053f9d2", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^4|^5|^6|^7|^8|^9"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "support": {"issues": "https://github.com/defuse/php-encryption/issues", "source": "https://github.com/defuse/php-encryption/tree/v2.3.1"}, "time": "2021-04-09T23:57:26+00:00"}, {"name": "dflydev/dot-access-configuration", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-configuration.git", "reference": "2e6eb0c8b8830b26bb23defcfc38d4276508fc49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-configuration/zipball/2e6eb0c8b8830b26bb23defcfc38d4276508fc49", "reference": "2e6eb0c8b8830b26bb23defcfc38d4276508fc49", "shasum": ""}, "require": {"dflydev/dot-access-data": "1.*", "dflydev/placeholder-resolver": "1.*", "php": ">=5.3.2"}, "require-dev": {"symfony/yaml": "~2.1"}, "suggest": {"symfony/yaml": "Required for using the YAML Configuration Builders"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Dflydev\\DotAccessConfiguration": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}], "description": "Given a deep data structure representing a configuration, access configuration by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-configuration", "keywords": ["config", "configuration"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-configuration/issues", "source": "https://github.com/dflydev/dflydev-dot-access-configuration/tree/master"}, "time": "2018-09-08T23:00:17+00:00"}, {"name": "dflydev/dot-access-data", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "3fbd874921ab2c041e899d044585a2ab9795df8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/3fbd874921ab2c041e899d044585a2ab9795df8a", "reference": "3fbd874921ab2c041e899d044585a2ab9795df8a", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Dflydev\\DotAccessData": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/master"}, "time": "2017-01-20T21:14:22+00:00"}, {"name": "dflydev/placeholder-resolver", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-placeholder-resolver.git", "reference": "c498d0cae91b1bb36cc7d60906dab8e62bb7c356"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-placeholder-resolver/zipball/c498d0cae91b1bb36cc7d60906dab8e62bb7c356", "reference": "c498d0cae91b1bb36cc7d60906dab8e62bb7c356", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Dflydev\\PlaceholderResolver": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}], "description": "Given a data source representing key => value pairs, resolve placeholders like ${foo.bar} to the value associated with the 'foo.bar' key in the data source.", "homepage": "https://github.com/dflydev/dflydev-placeholder-resolver", "keywords": ["placeholder", "resolver"], "support": {"issues": "https://github.com/dflydev/dflydev-placeholder-resolver/issues", "source": "https://github.com/dflydev/dflydev-placeholder-resolver/tree/v1.0.2"}, "time": "2012-10-28T21:08:28+00:00"}, {"name": "doctrine/annotations", "version": "1.13.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "5b668aef16090008790395c02c893b1ba13f7e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/5b668aef16090008790395c02c893b1ba13f7e08", "reference": "5b668aef16090008790395c02c893b1ba13f7e08", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.2"}, "time": "2021-08-05T19:00:23+00:00"}, {"name": "doctrine/cache", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "4cf401d14df219fa6f38b671f5493449151c9ad8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/4cf401d14df219fa6f38b671f5493449151c9ad8", "reference": "4cf401d14df219fa6f38b671f5493449151c9ad8", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^8.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.2 || ^6.0@dev", "symfony/var-exporter": "^4.4 || ^5.2 || ^6.0@dev"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.12.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2021-07-17T14:39:21+00:00"}, {"name": "doctrine/collections", "version": "1.6.8", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "1958a744696c6bb3bb0d28db2611dc11610e78af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/1958a744696c6bb3bb0d28db2611dc11610e78af", "reference": "1958a744696c6bb3bb0d28db2611dc11610e78af", "shasum": ""}, "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.2.1"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.8"}, "time": "2021-08-10T18:51:53+00:00"}, {"name": "doctrine/common", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/f3812c026e557892c34ef37f6ab808a6b567da7f", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2020-06-05T16:46:05+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector", "Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/lexer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2020-05-25T17:44:05+00:00"}, {"name": "doctrine/persistence", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2020-06-20T12:56:16+00:00"}, {"name": "doctrine/reflection", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "fa587178be682efe90d005e3a322590d6ebb59a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/fa587178be682efe90d005e3a322590d6ebb59a5", "reference": "fa587178be682efe90d005e3a322590d6ebb59a5", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^6.0 || ^8.2.0", "doctrine/common": "^2.10", "phpstan/phpstan": "^0.11.0 || ^0.12.20", "phpstan/phpstan-phpunit": "^0.11.0 || ^0.12.16", "phpunit/phpunit": "^7.5 || ^9.1.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.2"}, "abandoned": "roave/better-reflection", "time": "2020-10-27T21:46:55+00:00"}, {"name": "drupal/admin_can_login_anyuser", "version": "1.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/admin_can_login_anyuser.git", "reference": "8.x-1.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/admin_can_login_anyuser-8.x-1.2.zip", "reference": "8.x-1.2", "shasum": "bbb427611276a2bb6da3f995a728eebcae28d8a1"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.2", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Project has not opted into security advisory coverage!"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3496265"}, {"name": "rahu<PERSON><PERSON><PERSON><PERSON>_ec3", "homepage": "https://www.drupal.org/user/2706389"}], "description": "Administrator role user can login any user from admin", "homepage": "https://www.drupal.org/project/admin_can_login_anyuser", "support": {"source": "https://git.drupalcode.org/project/admin_can_login_anyuser"}}, {"name": "drupal/admin_toolbar", "version": "3.0.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/admin_toolbar.git", "reference": "3.0.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/admin_toolbar-3.0.3.zip", "reference": "3.0.3", "shasum": "ce735c931c0bd6da79bd8e45ca459d61015bbe44"}, "require": {"drupal/core": "^8.8.0 || ^9.0"}, "require-dev": {"drupal/admin_toolbar_tools": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON> (eme)", "homepage": "https://www.drupal.org/u/eme", "role": "Maintainer"}, {"name": "<PERSON><PERSON> (romainj)", "homepage": "https://www.drupal.org/u/romainj", "role": "Maintainer"}, {"name": "<PERSON> (adriancid)", "homepage": "https://www.drupal.org/u/adriancid", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON> (matio89)", "homepage": "https://www.drupal.org/u/matio89", "role": "Maintainer"}, {"name": "fethi.krout", "homepage": "https://www.drupal.org/user/3206765"}, {"name": "matio89", "homepage": "https://www.drupal.org/user/2320090"}, {"name": "r<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/370706"}], "description": "Provides a drop-down menu interface to the core Drupal Toolbar.", "homepage": "http://drupal.org/project/admin_toolbar", "keywords": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/admin_toolbar", "issues": "https://www.drupal.org/project/issues/admin_toolbar"}}, {"name": "drupal/autosave_form", "version": "1.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/autosave_form.git", "reference": "8.x-1.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/autosave_form-8.x-1.3.zip", "reference": "8.x-1.3", "shasum": "5a7d07baf6952e7ac00a580ed96f7effffe72c8f"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2901211"}], "description": "Adds autosave feature on forms.", "homepage": "https://www.drupal.org/project/autosave_form", "support": {"source": "https://git.drupalcode.org/project/autosave_form"}}, {"name": "drupal/better_exposed_filters", "version": "5.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/better_exposed_filters.git", "reference": "8.x-5.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/better_exposed_filters-8.x-5.0.zip", "reference": "8.x-5.0", "shasum": "ef575591af202b5c6867841ce58e1f447455e502"}, "require": {"drupal/core": "^8.8 || ^9", "drupal/jquery_ui": "^1.4", "drupal/jquery_ui_datepicker": "^1.0", "drupal/jquery_ui_slider": "^1.1", "drupal/jquery_ui_touch_punch": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-5.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mikeker"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/etroid"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/neslee-canil-pinto"}, {"name": "jkopel", "homepage": "https://www.drupal.org/user/66207"}, {"name": "mikeker", "homepage": "https://www.drupal.org/user/192273"}, {"name": "rlhawk", "homepage": "https://www.drupal.org/user/352283"}], "description": "Replaces the Views default single- or multi-select boxes with more advanced options.", "homepage": "https://www.drupal.org/project/better_exposed_filters", "support": {"source": "https://git.drupalcode.org/project/better_exposed_filters", "issues": "https://www.drupal.org/project/issues/better_exposed_filters"}}, {"name": "drupal/config_filter", "version": "1.8.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/config_filter.git", "reference": "8.x-1.8"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/config_filter-8.x-1.8.zip", "reference": "8.x-1.8", "shasum": "5def5f97e79d6f5af6bb7007f012443475c90bfe"}, "require": {"drupal/core": "^8 || ^9"}, "suggest": {"drupal/config_split": "Split site configuration for different environments."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.8", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/bircher", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "Nuvole Web", "homepage": "http://nuvole.org", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "pescetti", "homepage": "https://www.drupal.org/user/436244"}], "description": "Config Filter allows other modules to interact with a ConfigStorage through filter plugins.", "homepage": "https://www.drupal.org/project/config_filter", "keywords": ["<PERSON><PERSON><PERSON>", "configuration", "configuration management"], "support": {"source": "https://git.drupalcode.org/project/config_filter", "issues": "https://www.drupal.org/project/issues/config_filter", "slack": "https://drupal.slack.com/archives/C45342CDD"}}, {"name": "drupal/config_split", "version": "1.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/config_split.git", "reference": "8.x-1.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/config_split-8.x-1.7.zip", "reference": "8.x-1.7", "shasum": "3cd524ebc0b52db31a6bef2c55693f4e62890b4f"}, "require": {"drupal/config_filter": "^1", "drupal/core": "^8.8 || ^9"}, "conflict": {"drupal/console": "<1.3.2"}, "suggest": {"drupal/chosen": "<PERSON><PERSON> uses the Chosen jQuery plugin to make the <select> elements more user-friendly.", "drupal/select2_all": "Applies the Select2 library to all select fields on the site similar to the Chosen module."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/bircher", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "Nuvole Web", "homepage": "http://nuvole.org", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "pescetti", "homepage": "https://www.drupal.org/user/436244"}], "description": "Configuration filter for importing and exporting extra config", "homepage": "https://www.drupal.org/project/config_split", "keywords": ["<PERSON><PERSON><PERSON>", "configuration", "configuration management"], "support": {"source": "https://git.drupalcode.org/project/config_split", "issues": "https://www.drupal.org/project/issues/config_split", "slack": "https://drupal.slack.com/archives/C45342CDD"}}, {"name": "drupal/console", "version": "1.9.8", "source": {"type": "git", "url": "https://github.com/hechoendrupal/drupal-console.git", "reference": "d292c940c07d164e32bbe9525e909311ca65e8cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hechoendrupal/drupal-console/zipball/d292c940c07d164e32bbe9525e909311ca65e8cb", "reference": "d292c940c07d164e32bbe9525e909311ca65e8cb", "shasum": ""}, "require": {"alchemy/zippy": "~0.4", "composer/installers": "~1.0", "doctrine/annotations": "^1.2", "doctrine/collections": "^1.3", "drupal/console-core": "1.9.7", "drupal/console-extend-plugin": "~0.9.5", "php": ">=7.0.8", "psy/psysh": "0.6.* || ~0.8", "symfony/css-selector": "~3.0|~4.0", "symfony/dom-crawler": "~3.0|~4.0", "symfony/http-foundation": "~3.0|~4.0"}, "suggest": {"symfony/thanks": "Thank your favorite PHP projects on GitHub using the CLI", "vlucas/phpdotenv": "Loads environment variables from .env to getenv(), $_ENV and $_SERVER automagically"}, "bin": ["bin/drupal"], "type": "library", "autoload": {"psr-4": {"Drupal\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dmouse.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jmolivas.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://enzolutions.com/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Drupal Console Contributors", "homepage": "https://github.com/hechoendrupal/drupal-console/graphs/contributors"}], "description": "The Drupal CLI. A tool to generate boilerplate code, interact with and debug Drupal.", "homepage": "http://drupalconsole.com/", "keywords": ["console", "development", "drupal", "symfony"], "support": {"docs": "https://docs.drupalconsole.com/", "forum": "https://gitter.im/hechoendrupal/DrupalConsole", "issues": "https://github.com/hechoendrupal/drupal-console/issues", "source": "https://github.com/hechoendrupal/drupal-console/tree/1.9.8"}, "funding": [{"url": "https://opencollective.com/drupalconsole", "type": "open_collective"}], "time": "2021-11-29T17:09:44+00:00"}, {"name": "drupal/console-core", "version": "1.9.7", "source": {"type": "git", "url": "https://github.com/hechoendrupal/drupal-console-core.git", "reference": "ab3abc2631761c9588230ba88189d9ba4eb9ed63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hechoendrupal/drupal-console-core/zipball/ab3abc2631761c9588230ba88189d9ba4eb9ed63", "reference": "ab3abc2631761c9588230ba88189d9ba4eb9ed63", "shasum": ""}, "require": {"dflydev/dot-access-configuration": "^1.0", "drupal/console-en": "1.9.7", "guzzlehttp/guzzle": "~6.1", "php": ">=7.0.8", "stecman/symfony-console-completion": "~0.7", "symfony/config": "~3.0|^4.4", "symfony/console": "~3.0|^4.4", "symfony/debug": "~3.0|^4.4", "symfony/dependency-injection": "~3.0|^4.4", "symfony/event-dispatcher": "~3.0|^4.4", "symfony/filesystem": "~3.0|^4.4", "symfony/finder": "~3.0|^4.4", "symfony/process": "~3.0|^4.4", "symfony/translation": "~3.0|^4.4", "symfony/yaml": "~3.0|^4.4", "twig/twig": "^1.38.2|^2.12.0", "webflo/drupal-finder": "^1.0", "webmozart/path-util": "^2.3"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Drupal\\Console\\Core\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dmouse.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jmolivas.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://enzolutions.com/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Drupal Console Contributors", "homepage": "https://github.com/hechoendrupal/DrupalConsole/graphs/contributors"}], "description": "Drupal Console Core", "homepage": "http://drupalconsole.com/", "keywords": ["console", "development", "drupal", "symfony"], "support": {"docs": "http://docs.drupalconsole.com/", "forum": "https://gitter.im/hechoendrupal/DrupalConsole", "issues": "https://github.com/hechoendrupal/DrupalConsole/issues", "source": "https://github.com/hechoendrupal/drupal-console-core/tree/1.9.7"}, "time": "2020-11-30T01:45:57+00:00"}, {"name": "drupal/console-en", "version": "v1.9.7", "source": {"type": "git", "url": "https://github.com/hechoendrupal/drupal-console-en.git", "reference": "7594601fff153c2799a62bd678ff80749baeee0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hechoendrupal/drupal-console-en/zipball/7594601fff153c2799a62bd678ff80749baeee0c", "reference": "7594601fff153c2799a62bd678ff80749baeee0c", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dmouse.net"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jmolivas.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://enzolutions.com/"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Drupal Console Contributors", "homepage": "https://github.com/hechoendrupal/DrupalConsole/graphs/contributors"}], "description": "<PERSON><PERSON><PERSON> Console English Language", "homepage": "http://drupalconsole.com/", "keywords": ["console", "development", "drupal", "symfony"], "support": {"docs": "https://docs.drupalconsole.com", "forum": "https://gitter.im/hechoendrupal/DrupalConsole", "issues": "https://github.com/hechoendrupal/DrupalConsole/issues", "source": "https://github.com/hechoendrupal/drupal-console-en/tree/master"}, "time": "2020-08-15T03:34:54+00:00"}, {"name": "drupal/console-extend-plugin", "version": "0.9.5", "source": {"type": "git", "url": "https://github.com/hechoendrupal/drupal-console-extend-plugin.git", "reference": "eff6da99cfb5fe1fc60990672d2667c402eb3585"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hechoendrupal/drupal-console-extend-plugin/zipball/eff6da99cfb5fe1fc60990672d2667c402eb3585", "reference": "eff6da99cfb5fe1fc60990672d2667c402eb3585", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "composer/installers": "^1.2", "symfony/finder": "~3.0|^4.4", "symfony/yaml": "~3.0|^4.4"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Console\\Composer\\Plugin\\Extender"}, "autoload": {"psr-4": {"Drupal\\Console\\Composer\\Plugin\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON><PERSON>e Extend Plugin", "support": {"issues": "https://github.com/hechoendrupal/drupal-console-extend-plugin/issues", "source": "https://github.com/hechoendrupal/drupal-console-extend-plugin/tree/0.9.5"}, "time": "2020-11-18T00:15:28+00:00"}, {"name": "drupal/consumers", "version": "1.11.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/consumers.git", "reference": "8.x-1.11"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/consumers-8.x-1.11.zip", "reference": "8.x-1.11", "shasum": "2f440f4f61d5a85575504612b6b52d1d54ecd22f"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.11", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "e0ipso", "homepage": "https://www.drupal.org/user/550110"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/79230"}], "description": "Declare all the consumers of your API", "homepage": "https://www.drupal.org/project/consumers", "support": {"source": "https://git.drupalcode.org/project/consumers"}}, {"name": "drupal/cookie_samesite_support", "version": "dev-1.x", "source": {"type": "git", "url": "https://git.drupalcode.org/project/cookie_samesite_support.git", "reference": "5735a3f8e0331d28e3f0a1e053ed8e76a021064e"}, "require": {"drupal/core": "^8.7||^9.0", "php": ">=7.2.0"}, "type": "drupal-module", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}, "drupal": {"version": "1.x-dev", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Project has not opted into security advisory coverage!"}}}, "autoload": {"psr-4": {"Drupal\\cookie_samesite_support\\": "src/"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON> (nikunjkotecha)", "homepage": "https://www.drupal.org/u/nikunjkotecha", "role": "Maintainer"}], "description": "Adds support for SameSite=None for all the browsers.", "homepage": "https://www.drupal.org/project/cookie_samesite_support", "support": {"source": "https://cgit.drupalcode.org/cookie_samesite_support", "issues": "https://drupal.org/project/issues/cookie_samesite_support"}}, {"name": "drupal/core", "version": "9.3.2", "source": {"type": "git", "url": "https://github.com/drupal/core.git", "reference": "6c9ba6b6314550e7efb8f5f4e2a40f54cfd6aee1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core/zipball/6c9ba6b6314550e7efb8f5f4e2a40f54cfd6aee1", "reference": "6c9ba6b6314550e7efb8f5f4e2a40f54cfd6aee1", "shasum": ""}, "require": {"asm89/stack-cors": "^1.1", "composer/semver": "^3.0", "doctrine/annotations": "^1.12", "doctrine/reflection": "^1.1", "egulias/email-validator": "^2.1.22|^3.0", "ext-date": "*", "ext-dom": "*", "ext-filter": "*", "ext-gd": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-simplexml": "*", "ext-spl": "*", "ext-tokenizer": "*", "ext-xml": "*", "guzzlehttp/guzzle": "^6.5.2", "laminas/laminas-diactoros": "^2.1", "laminas/laminas-feed": "^2.12", "masterminds/html5": "^2.1", "pear/archive_tar": "^1.4.14", "php": ">=7.3.0", "psr/log": "^1.0", "stack/builder": "^1.0", "symfony-cmf/routing": "^2.1", "symfony/console": "^4.4", "symfony/dependency-injection": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-foundation": "^4.4.7", "symfony/http-kernel": "^4.4", "symfony/mime": "^5.4", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/process": "^4.4", "symfony/psr-http-message-bridge": "^2.0", "symfony/routing": "^4.4", "symfony/serializer": "^4.4", "symfony/translation": "^4.4", "symfony/validator": "^4.4", "symfony/yaml": "^4.4.19", "twig/twig": "^2.12.0", "typo3/phar-stream-wrapper": "^3.1.3"}, "conflict": {"drush/drush": "<8.1.10"}, "replace": {"drupal/action": "self.version", "drupal/aggregator": "self.version", "drupal/automated_cron": "self.version", "drupal/ban": "self.version", "drupal/bartik": "self.version", "drupal/basic_auth": "self.version", "drupal/big_pipe": "self.version", "drupal/block": "self.version", "drupal/block_content": "self.version", "drupal/book": "self.version", "drupal/breakpoint": "self.version", "drupal/ckeditor": "self.version", "drupal/ckeditor5": "self.version", "drupal/claro": "self.version", "drupal/classy": "self.version", "drupal/color": "self.version", "drupal/comment": "self.version", "drupal/config": "self.version", "drupal/config_translation": "self.version", "drupal/contact": "self.version", "drupal/content_moderation": "self.version", "drupal/content_translation": "self.version", "drupal/contextual": "self.version", "drupal/core-annotation": "self.version", "drupal/core-assertion": "self.version", "drupal/core-bridge": "self.version", "drupal/core-class-finder": "self.version", "drupal/core-datetime": "self.version", "drupal/core-dependency-injection": "self.version", "drupal/core-diff": "self.version", "drupal/core-discovery": "self.version", "drupal/core-event-dispatcher": "self.version", "drupal/core-file-cache": "self.version", "drupal/core-file-security": "self.version", "drupal/core-filesystem": "self.version", "drupal/core-front-matter": "self.version", "drupal/core-gettext": "self.version", "drupal/core-graph": "self.version", "drupal/core-http-foundation": "self.version", "drupal/core-php-storage": "self.version", "drupal/core-plugin": "self.version", "drupal/core-proxy-builder": "self.version", "drupal/core-render": "self.version", "drupal/core-serialization": "self.version", "drupal/core-transliteration": "self.version", "drupal/core-utility": "self.version", "drupal/core-uuid": "self.version", "drupal/core-version": "self.version", "drupal/datetime": "self.version", "drupal/datetime_range": "self.version", "drupal/dblog": "self.version", "drupal/dynamic_page_cache": "self.version", "drupal/editor": "self.version", "drupal/entity_reference": "self.version", "drupal/field": "self.version", "drupal/field_layout": "self.version", "drupal/field_ui": "self.version", "drupal/file": "self.version", "drupal/filter": "self.version", "drupal/forum": "self.version", "drupal/hal": "self.version", "drupal/help": "self.version", "drupal/help_topics": "self.version", "drupal/history": "self.version", "drupal/image": "self.version", "drupal/inline_form_errors": "self.version", "drupal/jsonapi": "self.version", "drupal/language": "self.version", "drupal/layout_builder": "self.version", "drupal/layout_discovery": "self.version", "drupal/link": "self.version", "drupal/locale": "self.version", "drupal/media": "self.version", "drupal/media_library": "self.version", "drupal/menu_link_content": "self.version", "drupal/menu_ui": "self.version", "drupal/migrate": "self.version", "drupal/migrate_drupal": "self.version", "drupal/migrate_drupal_multilingual": "self.version", "drupal/migrate_drupal_ui": "self.version", "drupal/minimal": "self.version", "drupal/node": "self.version", "drupal/olivero": "self.version", "drupal/options": "self.version", "drupal/page_cache": "self.version", "drupal/path": "self.version", "drupal/path_alias": "self.version", "drupal/quickedit": "self.version", "drupal/rdf": "self.version", "drupal/responsive_image": "self.version", "drupal/rest": "self.version", "drupal/search": "self.version", "drupal/serialization": "self.version", "drupal/settings_tray": "self.version", "drupal/seven": "self.version", "drupal/shortcut": "self.version", "drupal/standard": "self.version", "drupal/stark": "self.version", "drupal/statistics": "self.version", "drupal/syslog": "self.version", "drupal/system": "self.version", "drupal/taxonomy": "self.version", "drupal/telephone": "self.version", "drupal/text": "self.version", "drupal/toolbar": "self.version", "drupal/tour": "self.version", "drupal/tracker": "self.version", "drupal/update": "self.version", "drupal/user": "self.version", "drupal/views": "self.version", "drupal/views_ui": "self.version", "drupal/workflows": "self.version", "drupal/workspaces": "self.version"}, "type": "drupal-core", "extra": {"drupal-scaffold": {"file-mapping": {"[project-root]/.editorconfig": "assets/scaffold/files/editorconfig", "[project-root]/.gitattributes": "assets/scaffold/files/gitattributes", "[web-root]/.csslintrc": "assets/scaffold/files/csslintrc", "[web-root]/.eslintignore": "assets/scaffold/files/eslintignore", "[web-root]/.eslintrc.json": "assets/scaffold/files/eslintrc.json", "[web-root]/.ht.router.php": "assets/scaffold/files/ht.router.php", "[web-root]/.htaccess": "assets/scaffold/files/htaccess", "[web-root]/example.gitignore": "assets/scaffold/files/example.gitignore", "[web-root]/index.php": "assets/scaffold/files/index.php", "[web-root]/INSTALL.txt": "assets/scaffold/files/drupal.INSTALL.txt", "[web-root]/README.md": "assets/scaffold/files/drupal.README.md", "[web-root]/robots.txt": "assets/scaffold/files/robots.txt", "[web-root]/update.php": "assets/scaffold/files/update.php", "[web-root]/web.config": "assets/scaffold/files/web.config", "[web-root]/sites/README.txt": "assets/scaffold/files/sites.README.txt", "[web-root]/sites/development.services.yml": "assets/scaffold/files/development.services.yml", "[web-root]/sites/example.settings.local.php": "assets/scaffold/files/example.settings.local.php", "[web-root]/sites/example.sites.php": "assets/scaffold/files/example.sites.php", "[web-root]/sites/default/default.services.yml": "assets/scaffold/files/default.services.yml", "[web-root]/sites/default/default.settings.php": "assets/scaffold/files/default.settings.php", "[web-root]/modules/README.txt": "assets/scaffold/files/modules.README.txt", "[web-root]/profiles/README.txt": "assets/scaffold/files/profiles.README.txt", "[web-root]/themes/README.txt": "assets/scaffold/files/themes.README.txt"}}}, "autoload": {"psr-4": {"Drupal\\Core\\": "lib/Drupal/Core", "Drupal\\Component\\": "lib/Drupal/Component", "Drupal\\Driver\\": "../drivers/lib/Drupal/Driver"}, "classmap": ["lib/Drupal.php", "lib/Drupal/Component/DependencyInjection/Container.php", "lib/Drupal/Component/DependencyInjection/PhpArrayContainer.php", "lib/Drupal/Component/FileCache/FileCacheFactory.php", "lib/Drupal/Component/Utility/Timer.php", "lib/Drupal/Component/Utility/Unicode.php", "lib/Drupal/Core/Cache/Cache.php", "lib/Drupal/Core/Cache/CacheBackendInterface.php", "lib/Drupal/Core/Cache/CacheTagsChecksumInterface.php", "lib/Drupal/Core/Cache/CacheTagsChecksumTrait.php", "lib/Drupal/Core/Cache/CacheTagsInvalidatorInterface.php", "lib/Drupal/Core/Cache/DatabaseBackend.php", "lib/Drupal/Core/Cache/DatabaseCacheTagsChecksum.php", "lib/Drupal/Core/Database/Connection.php", "lib/Drupal/Core/Database/Database.php", "lib/Drupal/Core/Database/Driver/mysql/Connection.php", "lib/Drupal/Core/Database/Driver/pgsql/Connection.php", "lib/Drupal/Core/Database/Driver/sqlite/Connection.php", "lib/Drupal/Core/Database/Statement.php", "lib/Drupal/Core/Database/StatementInterface.php", "lib/Drupal/Core/DependencyInjection/Container.php", "lib/Drupal/Core/DrupalKernel.php", "lib/Drupal/Core/DrupalKernelInterface.php", "lib/Drupal/Core/Http/InputBag.php", "lib/Drupal/Core/Installer/InstallerRedirectTrait.php", "lib/Drupal/Core/Site/Settings.php"], "files": ["includes/bootstrap.inc", "includes/guzzle_php81_shim.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal is an open source content management platform powering millions of websites and applications.", "support": {"source": "https://github.com/drupal/core/tree/9.3.2"}, "time": "2022-01-05T02:55:30+00:00"}, {"name": "drupal/core-composer-scaffold", "version": "9.3.2", "source": {"type": "git", "url": "https://github.com/drupal/core-composer-scaffold.git", "reference": "d3e0b1d707125c5de0f54315906e65654c3608da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-composer-scaffold/zipball/d3e0b1d707125c5de0f54315906e65654c3608da", "reference": "d3e0b1d707125c5de0f54315906e65654c3608da", "shasum": ""}, "require": {"composer-plugin-api": "^1 || ^2", "php": ">=7.3.0"}, "conflict": {"drupal-composer/drupal-scaffold": "*"}, "require-dev": {"composer/composer": "^1.8@stable"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\Scaffold\\Plugin", "branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\Scaffold\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A flexible Composer project scaffold builder.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "support": {"source": "https://github.com/drupal/core-composer-scaffold/tree/9.3.2"}, "time": "2021-11-19T09:52:23+00:00"}, {"name": "drupal/core-project-message", "version": "9.3.2", "source": {"type": "git", "url": "https://github.com/drupal/core-project-message.git", "reference": "812d6da43dd49cc210af62e80fa92189e68e565a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-project-message/zipball/812d6da43dd49cc210af62e80fa92189e68e565a", "reference": "812d6da43dd49cc210af62e80fa92189e68e565a", "shasum": ""}, "require": {"composer-plugin-api": "^1.1 || ^2", "php": ">=7.3.0"}, "type": "composer-plugin", "extra": {"class": "Drupal\\Composer\\Plugin\\ProjectMessage\\MessagePlugin"}, "autoload": {"psr-4": {"Drupal\\Composer\\Plugin\\ProjectMessage\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Adds a message after Composer installation.", "homepage": "https://www.drupal.org/project/drupal", "keywords": ["drupal"], "support": {"source": "https://github.com/drupal/core-project-message/tree/9.3.2"}, "time": "2020-09-14T13:40:36+00:00"}, {"name": "drupal/core-recommended", "version": "9.3.2", "source": {"type": "git", "url": "https://github.com/drupal/core-recommended.git", "reference": "9f570d6bf6e568b8c2deab02349a11fad3d1e272"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drupal/core-recommended/zipball/9f570d6bf6e568b8c2deab02349a11fad3d1e272", "reference": "9f570d6bf6e568b8c2deab02349a11fad3d1e272", "shasum": ""}, "require": {"asm89/stack-cors": "1.3.0", "composer/semver": "3.2.6", "doctrine/annotations": "1.13.2", "doctrine/lexer": "1.2.1", "doctrine/reflection": "1.2.2", "drupal/core": "9.3.2", "egulias/email-validator": "3.1.2", "guzzlehttp/guzzle": "6.5.5", "guzzlehttp/promises": "1.5.1", "guzzlehttp/psr7": "1.8.3", "laminas/laminas-diactoros": "2.8.0", "laminas/laminas-escaper": "2.9.0", "laminas/laminas-feed": "2.15.0", "laminas/laminas-stdlib": "3.6.1", "masterminds/html5": "2.7.5", "pear/archive_tar": "1.4.14", "pear/console_getopt": "v1.4.3", "pear/pear-core-minimal": "v1.10.11", "pear/pear_exception": "v1.0.2", "psr/cache": "1.0.1", "psr/container": "1.1.1", "psr/http-factory": "1.0.1", "psr/http-message": "1.0.1", "psr/log": "1.1.4", "ralouphie/getallheaders": "3.0.3", "stack/builder": "v1.0.6", "symfony-cmf/routing": "2.3.4", "symfony/console": "v4.4.34", "symfony/debug": "v4.4.31", "symfony/dependency-injection": "v4.4.34", "symfony/deprecation-contracts": "v2.5.0", "symfony/error-handler": "v4.4.34", "symfony/event-dispatcher": "v4.4.34", "symfony/event-dispatcher-contracts": "v1.1.11", "symfony/http-client-contracts": "v2.5.0", "symfony/http-foundation": "v4.4.34", "symfony/http-kernel": "v4.4.35", "symfony/mime": "v5.4.0", "symfony/polyfill-ctype": "v1.23.0", "symfony/polyfill-iconv": "v1.23.0", "symfony/polyfill-intl-idn": "v1.23.0", "symfony/polyfill-intl-normalizer": "v1.23.0", "symfony/polyfill-mbstring": "v1.23.1", "symfony/polyfill-php80": "v1.23.1", "symfony/process": "v4.4.35", "symfony/psr-http-message-bridge": "v2.1.2", "symfony/routing": "v4.4.34", "symfony/serializer": "v4.4.35", "symfony/service-contracts": "v2.5.0", "symfony/translation": "v4.4.34", "symfony/translation-contracts": "v2.5.0", "symfony/validator": "v4.4.35", "symfony/var-dumper": "v5.4.0", "symfony/yaml": "v4.4.34", "twig/twig": "v2.14.7", "typo3/phar-stream-wrapper": "v3.1.7"}, "conflict": {"webflo/drupal-core-strict": "*"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Locked core dependencies; require this project INSTEAD OF drupal/core.", "support": {"source": "https://github.com/drupal/core-recommended/tree/9.3.2"}, "time": "2022-01-05T02:55:30+00:00"}, {"name": "drupal/crop", "version": "2.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/crop.git", "reference": "8.x-2.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/crop-8.x-2.1.zip", "reference": "8.x-2.1", "shasum": "c03541907d59874ca8a81f574258f6c0de8cbdc8"}, "require": {"drupal/core": "^8.8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "Drupal Media Team", "homepage": "https://www.drupal.org/user/3260690"}, {"name": "phenaproxima", "homepage": "https://www.drupal.org/user/205645"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "woprrr", "homepage": "https://www.drupal.org/user/858604"}], "description": "Provides storage and API for image crops.", "homepage": "https://www.drupal.org/project/crop", "support": {"source": "https://git.drupalcode.org/project/crop", "issues": "https://www.drupal.org/project/issues/crop"}}, {"name": "drupal/ctools", "version": "3.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/ctools.git", "reference": "8.x-3.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/ctools-8.x-3.7.zip", "reference": "8.x-3.7", "shasum": "b11c0981a1d2ab3cc9e8e614a337d8e2a2a70c0e"}, "require": {"drupal/core": "^8.8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "branch-alias": {"dev-8.x-3.x": "3.x-dev"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (EclipseGc)", "homepage": "https://www.drupal.org/u/eclipsegc", "role": "Maintainer"}, {"name": "<PERSON> (japerry)", "homepage": "https://www.drupal.org/u/japerry", "role": "Maintainer"}, {"name": "<PERSON> (tim.plunkett)", "homepage": "https://www.drupal.org/u/timplunkett", "role": "Maintainer"}, {"name": "<PERSON> (neclimdul)", "homepage": "https://www.drupal.org/u/neclimdul", "role": "Maintainer"}, {"name": "<PERSON> (da<PERSON>hn<PERSON>)", "homepage": "https://www.drupal.org/u/dawehner", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/160302"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/26979"}, {"name": "neclimdul", "homepage": "https://www.drupal.org/user/48673"}, {"name": "sdboyer", "homepage": "https://www.drupal.org/user/146719"}, {"name": "sun", "homepage": "https://www.drupal.org/user/54136"}, {"name": "tim.plunkett", "homepage": "https://www.drupal.org/user/241634"}], "description": "Provides a number of utility and helper APIs for Drupal developers and site builders.", "homepage": "https://www.drupal.org/project/ctools", "support": {"source": "https://git.drupalcode.org/project/ctools", "issues": "https://www.drupal.org/project/issues/ctools"}}, {"name": "drupal/devel", "version": "4.1.3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/devel.git", "reference": "4.1.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/devel-4.1.3.zip", "reference": "4.1.3", "shasum": "f82261ca19a43379742e1df4c5a3795ef7a70441"}, "require": {"doctrine/common": "^2.7", "drupal/core": "^8.8 || ^9", "symfony/var-dumper": "^4 || ^5"}, "conflict": {"kint-php/kint": "<3"}, "require-dev": {"drush/drush": "^10"}, "suggest": {"kint-php/kint": "Kint provides an informative display of arrays/objects. Useful for debugging and developing."}, "type": "drupal-module", "extra": {"drupal": {"version": "4.1.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "drupalspoons", "homepage": "https://www.drupal.org/user/3647684"}, {"name": "moshe weitzman", "homepage": "https://www.drupal.org/user/23"}], "description": "Various blocks, pages, and functions for developers.", "homepage": "https://www.drupal.org/project/devel", "support": {"source": "https://gitlab.com/drupalspoons/devel", "issues": "https://gitlab.com/drupalspoons/devel/-/issues", "slack": "https://drupal.slack.com/archives/C012WAW1MH6"}}, {"name": "drupal/devel_entity_updates", "version": "3.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/devel_entity_updates.git", "reference": "3.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/devel_entity_updates-3.0.1.zip", "reference": "3.0.1", "shasum": "ed7f7b410659538b4ccf898933ef413f75382896"}, "require": {"drupal/core": "^8.7.7 || ^9", "drupal/devel": "*"}, "conflict": {"drupal/core": "<8.7", "drush/drush": ">=9.0 <9.6"}, "require-dev": {"drupal/devel": "^2 || ^3"}, "type": "drupal-module", "extra": {"drupal": {"version": "3.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON> (plach)", "homepage": "https://www.drupal.org/u/plach", "role": "Maintainer"}, {"name": "plach", "homepage": "https://www.drupal.org/user/183211"}], "description": "Provides developers an API and drush command to perform automatic entity updates.", "homepage": "http://drupal.org/project/devel_entity_updates", "support": {"source": "https://cgit.drupalcode.org/devel_entity_updates", "issues": "https://drupal.org/project/issues/devel_entity_updates", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/download_count", "version": "2.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/download_count.git", "reference": "2.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/download_count-2.0.0.zip", "reference": "2.0.0", "shasum": "289dd192a662f9e22363b38d2008a23bc26ed53a"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/399718"}, {"name": "UsingSession", "homepage": "https://www.drupal.org/user/3582050"}, {"name": "WorldFallz", "homepage": "https://www.drupal.org/user/155304"}], "description": "Tracks file downloads for Drupal private core file fields.", "homepage": "https://www.drupal.org/project/download_count", "support": {"source": "https://git.drupalcode.org/project/download_count"}}, {"name": "drupal/dropzonejs", "version": "2.5.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/dropzonejs.git", "reference": "8.x-2.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/dropzonejs-8.x-2.5.zip", "reference": "8.x-2.5", "shasum": "9918bd8c3c62599ec701be3bbac986741e735859"}, "require": {"drupal/core": "^8.8 || ^9"}, "require-dev": {"drupal/entity_browser": "^2.5"}, "suggest": {"enyo/dropzone": "Required to use drupal/dropzonejs. DropzoneJS is an open source library that provides drag’n’drop file uploads with image previews."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://drupal.org/u/slashrsm", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://drupal.org/u/chrfritsch", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://drupal.org/u/Primsi", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://drupal.org/u/jungle", "role": "Maintainer"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/1998478/committers", "role": "contributor"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "wouters_f", "homepage": "https://www.drupal.org/user/721548"}, {"name": "zkday", "homepage": "https://www.drupal.org/user/888644"}], "description": "Drupal integration for DropzoneJS - An open source library that provides drag’n’drop file uploads with image previews.", "homepage": "https://www.drupal.org/project/dropzonejs", "keywords": ["DropzoneJS", "<PERSON><PERSON><PERSON>"], "support": {"source": "https://www.drupal.org/project/dropzonejs", "issues": "https://www.drupal.org/project/issues/dropzonejs", "#media": "http://drupal.slack.com"}}, {"name": "drupal/entity_reference_revisions", "version": "1.9.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/entity_reference_revisions.git", "reference": "8.x-1.9"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/entity_reference_revisions-8.x-1.9.zip", "reference": "8.x-1.9", "shasum": "e1c51bdea495eb3b458130d6f0a00c347f5637df"}, "require": {"drupal/core": "^8.7.7 || ^9"}, "require-dev": {"drupal/diff": "1.x-dev"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.9", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/514222"}, {"name": "jeroen.b", "homepage": "https://www.drupal.org/user/1853532"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}], "description": "Entity Reference Revisions", "homepage": "https://www.drupal.org/project/entity_reference_revisions", "support": {"source": "https://git.drupalcode.org/project/entity_reference_revisions"}}, {"name": "drupal/facets", "version": "1.8.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/facets.git", "reference": "8.x-1.8"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/facets-8.x-1.8.zip", "reference": "8.x-1.8", "shasum": "f621b84b59c5315db14a0529df5dfc74ca5bc9de"}, "require": {"drupal/core": "^8.8 || ^9"}, "conflict": {"drupal/search_api": "<1.14"}, "require-dev": {"drupal/search_api": "~1.14"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.8", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "branch-alias": {"dev-8.x-1.x": "1.x-dev"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "See all contributors", "homepage": "https://www.drupal.org/node/2348769/committers"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/462700"}, {"name": "b<PERSON>son_", "homepage": "https://www.drupal.org/user/2393360"}, {"name": "drunken monkey", "homepage": "https://www.drupal.org/user/205582"}, {"name": "mkalkbrenner", "homepage": "https://www.drupal.org/user/124705"}], "description": "The Facet module allows site builders to easily create and manage faceted search interfaces.", "homepage": "https://www.drupal.org/project/facets", "support": {"source": "git://git.drupal.org/project/facets.git", "issues": "https://www.drupal.org/project/issues/facets", "irc": "irc://irc.freenode.org/drupal-search-api"}}, {"name": "drupal/field_group", "version": "3.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/field_group.git", "reference": "8.x-3.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/field_group-8.x-3.2.zip", "reference": "8.x-3.2", "shasum": "2020bbfe40f6ba43bc733ae7c8761632572433a0"}, "require": {"drupal/core": "^8.8 || ^9"}, "require-dev": {"drupal/jquery_ui_accordion": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Hydra", "homepage": "https://www.drupal.org/user/647364"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/322618"}, {"name": "jyve", "homepage": "https://www.drupal.org/user/591438"}, {"name": "nils.destoop", "homepage": "https://www.drupal.org/user/361625"}, {"name": "swentel", "homepage": "https://www.drupal.org/user/107403"}], "description": "Provides the field_group module.", "homepage": "https://www.drupal.org/project/field_group", "support": {"source": "https://git.drupalcode.org/project/field_group", "issues": "https://www.drupal.org/project/issues/field_group"}}, {"name": "drupal/flag", "version": "4.0.0-beta3", "source": {"type": "git", "url": "https://git.drupalcode.org/project/flag.git", "reference": "8.x-4.0-beta3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/flag-8.x-4.0-beta3.zip", "reference": "8.x-4.0-beta3", "shasum": "856a4871034955406e111aee2227e716cc8064d6"}, "require": {"drupal/core": "^8.8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-4.0-beta3", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/214652"}, {"name": "fago", "homepage": "https://www.drupal.org/user/16747"}, {"name": "fubhy", "homepage": "https://www.drupal.org/user/761344"}, {"name": "joa<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/107701"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/26979"}, {"name": "mooffie", "homepage": "https://www.drupal.org/user/78454"}, {"name": "quicksketch", "homepage": "https://www.drupal.org/user/35821"}, {"name": "shabana.navas", "homepage": "https://www.drupal.org/user/1311398"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/65793"}], "description": "Create customized flags that users can set on entities.", "homepage": "https://www.drupal.org/project/flag", "support": {"source": "https://git.drupalcode.org/project/flag"}}, {"name": "drupal/focal_point", "version": "1.5.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/focal_point.git", "reference": "8.x-1.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/focal_point-8.x-1.5.zip", "reference": "8.x-1.5", "shasum": "41198e9220788c3b7d3146b10e5dfd6c73cd4784"}, "require": {"drupal/core": "^8.8 || ^9", "drupal/crop": "^1.0 || ^2.0"}, "require-dev": {"drupal/crop": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (bleen)", "homepage": "https://www.drupal.org/u/bleen", "role": "Maintainer"}], "description": "Focal Point allows content creators to mark the most important part of an image for easier cropping.", "homepage": "https://drupal.org/project/focal_point", "support": {"source": "https://cgit.drupalcode.org/focal_point", "issues": "https://drupal.org/project/issues/focal_point", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/focal_point_focus", "version": "1.11.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/focal_point_focus.git", "reference": "8.x-1.11"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/focal_point_focus-8.x-1.11.zip", "reference": "8.x-1.11", "shasum": "6dc7c5adf2d43e4fcb5feb175f45f88bb133b90f"}, "require": {"drupal/core": "^8 || ^9", "drupal/crop": "*", "drupal/focal_point": "*", "php": "^7.1 || ^8.0", "skaught/jquery-focuspoint": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.11", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "SKAUGHT", "homepage": "https://www.drupal.org/user/297907"}], "description": "Uses focuspoint (jquery plugin) and Focal Point crops for Image Formatter.", "homepage": "https://www.drupal.org/project/focal_point_focus", "keywords": ["<PERSON><PERSON><PERSON>", "Focal Point", "Focal Point Focus"], "support": {"source": "https://drupal.org/project/focal_point_focus", "issues": "https://drupal.org/project/issues/focal_point_focus"}}, {"name": "drupal/fullcalendar_view", "version": "5.1.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/fullcalendar_view.git", "reference": "5.1.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/fullcalendar_view-5.1.2.zip", "reference": "5.1.2", "shasum": "52309d725a9e6dc80b03fd883ba78fb675dec947"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "5.1.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "KarinG", "homepage": "https://www.drupal.org/user/787114"}, {"name": "Mingsong", "homepage": "https://www.drupal.org/user/2986445"}], "description": "This is a lightweight View plugin module that provides a calendar view format powered by FullCalendar JavaScript.", "homepage": "http://drupal.org/project/fullcalendar_view", "keywords": ["<PERSON><PERSON><PERSON>", "fullcalendar_view"], "support": {"source": "http://cgit.drupalcode.org/fullcalendar_view", "issues": "http://drupal.org/project/issues/fullcalendar_view"}}, {"name": "drupal/gin", "version": "3.0.0-alpha37", "source": {"type": "git", "url": "https://git.drupalcode.org/project/gin.git", "reference": "8.x-3.0-alpha37"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/gin-8.x-3.0-alpha37.zip", "reference": "8.x-3.0-alpha37", "shasum": "56f7226618f7e45697ee799e5e02c57d79cb3b1a"}, "require": {"drupal/core": "^8.8 || ^9 || ^10", "drupal/gin_toolbar": "^1.0@beta"}, "type": "drupal-theme", "extra": {"drupal": {"version": "8.x-3.0-alpha37", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Alpha releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON> (saschaeggi)", "homepage": "https://www.drupal.org/u/saschaeggi", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1999056"}], "description": "For a better Admin and Content Editor Experience.", "homepage": "https://www.drupal.org/project/gin", "support": {"source": "https://git.drupalcode.org/project/gin", "issues": "https://www.drupal.org/project/issues/gin"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/saschaeggi"}, {"type": "other", "url": "https://paypal.me/saschaeggi"}]}, {"name": "drupal/gin_toolbar", "version": "1.0.0-beta20", "source": {"type": "git", "url": "https://git.drupalcode.org/project/gin_toolbar.git", "reference": "8.x-1.0-beta20"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/gin_toolbar-8.x-1.0-beta20.zip", "reference": "8.x-1.0-beta20", "shasum": "770d6945de7ae001e3981db83daace610d4db5c4"}, "require": {"drupal/core": "^8 || ^9 || ^10"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0-beta20", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON> (saschaeggi)", "homepage": "https://www.drupal.org/u/saschaeggi", "role": "Maintainer"}], "description": "Gin Toolbar for Frontend use", "homepage": "https://www.drupal.org/project/gin_toolbar", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/gin_toolbar", "issues": "https://www.drupal.org/project/issues/gin_toolbar"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/saschaeggi"}, {"type": "other", "url": "https://paypal.me/saschaeggi"}]}, {"name": "drupal/graphql", "version": "3.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/graphql.git", "reference": "8.x-3.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/graphql-8.x-3.1.zip", "reference": "8.x-3.1", "shasum": "088430befbf13f2c8dadf1d9d14d01487e6f52bc"}, "require": {"drupal/core": "^8.8 || ^9", "webonyx/graphql-php": "^0.12.5"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "fubhy", "homepage": "https://www.drupal.org/user/761344"}, {"name": "hideaway", "homepage": "https://www.drupal.org/user/741876"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/612814"}, {"name": "k<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/262198"}, {"name": "pmelab", "homepage": "https://www.drupal.org/user/555322"}], "description": "Exposes your Drupal data model through a GraphQL schema.", "homepage": "http://drupal.org/project/graphql", "support": {"source": "https://git.drupalcode.org/project/graphql"}}, {"name": "drupal/graphql_core", "version": "3.1.0", "require": {"drupal/core": "^8.8 || ^9", "drupal/graphql": "*"}, "type": "metapackage", "extra": {"drupal": {"version": "8.x-3.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "fubhy", "homepage": "https://www.drupal.org/user/761344"}, {"name": "hideaway", "homepage": "https://www.drupal.org/user/741876"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/612814"}, {"name": "k<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/262198"}, {"name": "pmelab", "homepage": "https://www.drupal.org/user/555322"}], "description": "Provides type system plugins and derivers on behalf of core modules.", "homepage": "https://www.drupal.org/project/graphql", "support": {"source": "https://git.drupalcode.org/project/graphql"}}, {"name": "drupal/graphql_search_api", "version": "1.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/graphql_search_api.git", "reference": "8.x-1.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/graphql_search_api-8.x-1.2.zip", "reference": "8.x-1.2", "shasum": "02b9460873f7610354ffa1704c84665249c6923f"}, "require": {"drupal/core": "^8 || ^9", "drupal/graphql": "*", "drupal/search_api": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1907988"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/612814"}, {"name": "<PERSON>yu<PERSON>", "homepage": "https://www.drupal.org/user/652554"}], "description": "A Search API GraphQL schema.", "homepage": "https://www.drupal.org/project/graphql_search_api", "support": {"source": "https://git.drupalcode.org/project/graphql_search_api"}}, {"name": "drupal/graphql_views", "version": "1.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/graphql_views.git", "reference": "8.x-1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/graphql_views-8.x-1.1.zip", "reference": "8.x-1.1", "shasum": "86aaf71e74d51f84af176d619eb4aa6c1082170b"}, "require": {"drupal/core": "^8 || ^9", "drupal/graphql_core": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "fubhy", "homepage": "https://www.drupal.org/user/761344"}, {"name": "k<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/262198"}, {"name": "pmelab", "homepage": "https://www.drupal.org/user/555322"}], "description": "Exposes your Drupal Views data model through a GraphQL schema.", "homepage": "http://drupal.org/project/graphql_views", "support": {"source": "https://git.drupalcode.org/project/graphql_views"}}, {"name": "drupal/image_field_to_media", "version": "1.0.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/image_field_to_media.git", "reference": "1.0.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/image_field_to_media-1.0.2.zip", "reference": "1.0.2", "shasum": "c94310a447096b9b7f9fc3945fc7e004ad877b23"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "1.0.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON> (wombatbuddy)", "homepage": "https://www.drupal.org/u/wombatbuddy", "role": "Maintainer"}], "description": "Enables to convert existing image fields to Media image fields.", "homepage": "https://www.drupal.org/project/image_field_to_media", "support": {"source": "https://git.drupalcode.org/project/image_field_to_media", "issues": "https://www.drupal.org/project/issues/image_field_to_media"}}, {"name": "drupal/image_style_warmer", "version": "1.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/image_style_warmer.git", "reference": "8.x-1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/image_style_warmer-8.x-1.1.zip", "reference": "8.x-1.1", "shasum": "b05b911281e6d45df0a7735cfa9f8816f9d6233a"}, "require": {"drupal/core": "^8.8 || ^9"}, "require-dev": {"drush/drush": "^9.0 || ^10"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON> (IT-Cru)", "homepage": "https://www.drupal.org/u/IT-Cru", "role": "Maintainer"}], "description": "Create image styles of an image on upload or via queue worker.", "homepage": "https://drupal.org/project/image_style_warmer", "support": {"source": "https://cgit.drupalcode.org/image_style_warmer", "issues": "https://drupal.org/project/issues/image_style_warmer"}}, {"name": "drupal/inline_entity_form", "version": "1.0.0-rc9", "source": {"type": "git", "url": "https://git.drupalcode.org/project/inline_entity_form.git", "reference": "8.x-1.0-rc9"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/inline_entity_form-8.x-1.0-rc9.zip", "reference": "8.x-1.0-rc9", "shasum": "78953103a9c6e4e44bc877820a35f39913ea4559"}, "require": {"drupal/core": "^8.8 || ^9", "php": ">=7.1"}, "require-dev": {"drupal/entity_reference_revisions": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0-rc9", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "RC releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "b<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/86106"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/99340"}, {"name": "geek-merlin", "homepage": "https://www.drupal.org/user/229048"}, {"name": "joa<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/107701"}, {"name": "j<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/972218"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2182186"}, {"name": "oknate", "homepage": "https://www.drupal.org/user/471638"}, {"name": "ram4nd", "homepage": "https://www.drupal.org/user/601534"}, {"name": "rszrama", "homepage": "https://www.drupal.org/user/49344"}, {"name": "slashrsm", "homepage": "https://www.drupal.org/user/744628"}, {"name": "webflo", "homepage": "https://www.drupal.org/user/254778"}], "description": "Provides a widget for inline management (creation, modification, removal) of referenced entities.", "homepage": "https://www.drupal.org/project/inline_entity_form", "support": {"source": "https://git.drupalcode.org/project/inline_entity_form"}}, {"name": "drupal/j<PERSON>y_ui", "version": "1.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/jquery_ui.git", "reference": "8.x-1.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/jquery_ui-8.x-1.4.zip", "reference": "8.x-1.4", "shasum": "64c19ecc8902e2b4b1ab0cc5f5fe28dbc83bfebe"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "RobLoach", "homepage": "https://www.drupal.org/user/61114"}, {"name": "j<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/17190"}, {"name": "lauriii", "homepage": "https://www.drupal.org/user/1078742"}, {"name": "litwol", "homepage": "https://www.drupal.org/user/78134"}, {"name": "mfb", "homepage": "https://www.drupal.org/user/12302"}, {"name": "mfer", "homepage": "https://www.drupal.org/user/25701"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2972409"}, {"name": "sun", "homepage": "https://www.drupal.org/user/54136"}, {"name": "webchick", "homepage": "https://www.drupal.org/user/24967"}, {"name": "zrpnr", "homepage": "https://www.drupal.org/user/1448368"}], "description": "Provides jQuery UI library.", "homepage": "https://www.drupal.org/project/jquery_ui", "support": {"source": "https://git.drupalcode.org/project/jquery_ui"}}, {"name": "drupal/j<PERSON>y_ui_datepicker", "version": "1.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/jquery_ui_datepicker.git", "reference": "8.x-1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/jquery_ui_datepicker-8.x-1.1.zip", "reference": "8.x-1.1", "shasum": "69f62467f846bb514a10fa93f4c3b34c6275353f"}, "require": {"drupal/core": "^8 || ^9", "drupal/jquery_ui": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "bnjmnm", "homepage": "https://www.drupal.org/user/2369194"}, {"name": "ivnish", "homepage": "https://www.drupal.org/user/3547706"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/371407"}, {"name": "lauriii", "homepage": "https://www.drupal.org/user/1078742"}, {"name": "zrpnr", "homepage": "https://www.drupal.org/user/1448368"}], "description": "Provides jQuery UI Datepicker library.", "homepage": "https://www.drupal.org/project/jquery_ui_datepicker", "support": {"source": "https://git.drupalcode.org/project/jquery_ui_datepicker"}}, {"name": "drupal/j<PERSON>y_ui_slider", "version": "1.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/jquery_ui_slider.git", "reference": "8.x-1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/jquery_ui_slider-8.x-1.1.zip", "reference": "8.x-1.1", "shasum": "79b90cf60d45fc33ffdaa84bb2d6563f78a7d3d1"}, "require": {"drupal/core": "^8 || ^9", "drupal/jquery_ui": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "bnjmnm", "homepage": "https://www.drupal.org/user/2369194"}, {"name": "lauriii", "homepage": "https://www.drupal.org/user/1078742"}, {"name": "zrpnr", "homepage": "https://www.drupal.org/user/1448368"}], "description": "Provides jQuery UI Slider library.", "homepage": "https://www.drupal.org/project/jquery_ui_slider", "support": {"source": "https://git.drupalcode.org/project/jquery_ui_slider"}}, {"name": "drupal/j<PERSON>y_ui_touch_punch", "version": "1.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/jquery_ui_touch_punch.git", "reference": "1.0.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/jquery_ui_touch_punch-1.0.0.zip", "reference": "1.0.0", "shasum": "8444a0ed897ba3d8e8876a9602ec8b3dca678cd1"}, "require": {"drupal/core": "^8 || ^9", "drupal/jquery_ui": "^1.0"}, "suggest": {"furf/jquery-ui-touch-punch": "Required to use drupal/jquery_ui_touch_punch module."}, "type": "drupal-module", "extra": {"drupal": {"version": "1.0.0", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Project has not opted into security advisory coverage!"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://drupal.org/u/naveenvalecha", "role": "Maintainer"}], "description": "Provides jQuery UI Touch Punch library.", "homepage": "https://www.drupal.org/project/jquery_ui_touch_punch", "keywords": ["<PERSON><PERSON><PERSON>", "j<PERSON>y_ui_touch_punch"], "support": {"source": "https://www.drupal.org/project/jquery_ui_touch_punch", "issues": "https://www.drupal.org/project/issues/jquery_ui_touch_punch", "irc": "irc://irc.freenode.org/drupal-contribute"}}, {"name": "drupal/jsonapi_extras", "version": "3.20.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/jsonapi_extras.git", "reference": "8.x-3.20"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/jsonapi_extras-8.x-3.20.zip", "reference": "8.x-3.20", "shasum": "54bec6ae2b9261a53645182121ae16d3183e43ec"}, "require": {"drupal/core": "^8 || ^9", "drupal/jsonapi": "*", "e0ipso/shaper": "^1"}, "require-dev": {"drupal/jsonapi": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.20", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3366066", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mkolar"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/u/karlos007"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/bbrala"}], "description": "JSON:API Extras provides a means to override and provide limited configurations to the default zero-configuration implementation provided by the JSON:API module.", "homepage": "https://www.drupal.org/project/jsonapi_extras", "support": {"source": "https://git.drupalcode.org/project/jsonapi_extras"}}, {"name": "drupal/keycloak", "version": "1.6.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/keycloak.git", "reference": "8.x-1.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/keycloak-8.x-1.6.zip", "reference": "8.x-1.6", "shasum": "2b820b47f311e6081ed54d5e307dea52a62e12d5"}, "require": {"drupal/core": "^8 || ^9", "drupal/openid_connect": "^1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mario-steinitz", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/2536014"}, {"name": "c<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/744134"}, {"name": "jose<PERSON>.o<PERSON>", "homepage": "https://www.drupal.org/user/1321830"}], "description": "Enables OpenID Connect Keycloak sign on and user data synchronization.", "homepage": "https://www.drupal.org/project/keycloak", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/keycloak", "issues": "https://www.drupal.org/project/issues/keycloak"}}, {"name": "drupal/login_history", "version": "1.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/login_history.git", "reference": "8.x-1.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/login_history-8.x-1.1.zip", "reference": "8.x-1.1", "shasum": "540fa39120b6984a8b5f33b9ce850e2655218684"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1167326"}, {"name": "bocaj", "homepage": "https://www.drupal.org/user/582042"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}, {"name": "jcnventura", "homepage": "https://www.drupal.org/user/122464"}], "description": "Provides information about individual user logins", "homepage": "https://www.drupal.org/project/login_history", "support": {"source": "https://git.drupalcode.org/project/login_history", "issues": "https://www.drupal.org/project/issues/login_history"}}, {"name": "drupal/media_bulk_upload", "version": "1.0.0-alpha27", "source": {"type": "git", "url": "https://git.drupalcode.org/project/media_bulk_upload.git", "reference": "8.x-1.0-alpha27"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/media_bulk_upload-8.x-1.0-alpha27.zip", "reference": "8.x-1.0-alpha27", "shasum": "8dbb1d2c45731650d12ae53519913d385ce8497e"}, "require": {"drupal/core": "^8 || ^9", "drupal/dropzonejs": "^2.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0-alpha27", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Alpha releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "Mirnaxvb", "homepage": "https://www.drupal.org/user/3157031"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/3301055"}, {"name": "j<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2733365"}, {"name": "mycw1991", "homepage": "https://www.drupal.org/user/3523472"}], "description": "Allows uploading files in bulk and converting them to media entities.", "homepage": "https://www.drupal.org/project/media_bulk_upload", "support": {"source": "git://git.drupal.org/project/media_bulk_upload.git", "issues": "https://www.drupal.org/project/issues/media_bulk_upload"}}, {"name": "drupal/media_library_bulk_upload", "version": "1.0.0-alpha1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/media_library_bulk_upload.git", "reference": "1.0.0-alpha1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/media_library_bulk_upload-1.0.0-alpha1.zip", "reference": "1.0.0-alpha1", "shasum": "50c7aabd91d8c02183f985431d8502e2877ff51f"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "1.0.0-alpha1", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Project has not opted into security advisory coverage!"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "Unsupported Projects", "homepage": "https://www.drupal.org/user/291168"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2901211"}], "description": "Allows uploading files in bulk and converting them to media entities using the media library.", "homepage": "https://www.drupal.org/project/media_library_bulk_upload", "support": {"source": "https://git.drupalcode.org/project/media_library_bulk_upload"}}, {"name": "drupal/media_library_edit", "version": "2.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/media_library_edit.git", "reference": "8.x-2.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/media_library_edit-8.x-2.2.zip", "reference": "8.x-2.2", "shasum": "ae844a174ce24082988a8990a595b8f2398fa402"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "aheb<PERSON>", "homepage": "https://www.drupal.org/user/3190515"}], "description": "Add an edit button to the Media Library widget when an item is selected.", "homepage": "https://www.drupal.org/project/media_library_edit", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/media_library_edit", "issues": "https://www.drupal.org/project/issues/media_library_edit"}}, {"name": "drupal/metatag", "version": "1.26.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/metatag.git", "reference": "8.x-1.26"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/metatag-8.x-1.26.zip", "reference": "8.x-1.26", "shasum": "afa4a37422748baa2f0c35b13961438668ef3be8"}, "require": {"drupal/core": "^9.3 || ^10", "drupal/token": "^1.0"}, "require-dev": {"drupal/devel": "^4.0 || ^5.0", "drupal/hal": "^9 || ^1 || ^2", "drupal/metatag_dc": "*", "drupal/metatag_open_graph": "*", "drupal/page_manager": "^4.0", "drupal/panelizer": "^4.0", "drupal/redirect": "^1.0", "drupal/webprofiler": "^9 || ^10", "mpyw/phpunit-patch-serializable-comparison": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.26", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "See contributors", "homepage": "https://www.drupal.org/node/640498/committers", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/53892"}], "description": "Manage meta tags for all entities.", "homepage": "https://www.drupal.org/project/metatag", "keywords": ["<PERSON><PERSON><PERSON>", "seo"], "support": {"source": "https://git.drupalcode.org/project/metatag", "issues": "https://www.drupal.org/project/issues/metatag", "docs": "https://www.drupal.org/docs/8/modules/metatag"}}, {"name": "drupal/migrate_file", "version": "dev-2.0.x", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_file.git", "reference": "e33323bffaa1d990acb485db5544efaa75b1be95"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"branch-alias": {"dev-2.0.x": "2.0.x-dev"}, "drupal": {"version": "2.0.0+2-dev", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Dev releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "d<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/823702"}], "description": "Additional support for migrating files including downloading remote files and using remote uris (without download)", "homepage": "https://www.drupal.org/project/migrate_file", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "http://cgit.drupalcode.org/migrate_file", "issues": "https://www.drupal.org/project/issues/migrate_file"}}, {"name": "drupal/migrate_file_to_media", "version": "2.0.7", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_file_to_media.git", "reference": "2.0.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_file_to_media-2.0.7.zip", "reference": "2.0.7", "shasum": "9f53aaeac262b7aee71a0f3a79781aaf5b1c00d3"}, "require": {"drupal/core": "^8 || ^9", "drupal/migrate_plus": "*", "drupal/migrate_tools": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "ayalon", "homepage": "https://www.drupal.org/user/419226"}, {"name": "grahl", "homepage": "https://www.drupal.org/user/146040"}], "description": "Migrate file fields to media fields.", "homepage": "https://www.drupal.org/project/migrate_file_to_media", "support": {"source": "https://git.drupalcode.org/project/migrate_file_to_media"}}, {"name": "drupal/migrate_plus", "version": "5.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_plus.git", "reference": "8.x-5.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_plus-8.x-5.2.zip", "reference": "8.x-5.2", "shasum": "03ae34c362ccfacc4ae95b58469b25522e0ffb68"}, "require": {"drupal/core": "^9.1"}, "require-dev": {"drupal/migrate_example_advanced_setup": "*", "drupal/migrate_example_setup": "*", "drush/drush": "^10"}, "suggest": {"ext-soap": "*", "sainsburys/guzzle-oauth2-plugin": "3.0 required for the OAuth2 authentication plugin"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-5.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mikeryan", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/heddn", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Enhancements to core migration support.", "homepage": "https://www.drupal.org/project/migrate_plus", "support": {"source": "https://git.drupalcode.org/project/migrate_plus", "issues": "https://www.drupal.org/project/issues/migrate_plus", "slack": "#migrate"}}, {"name": "drupal/migrate_process_negate", "version": "1.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_process_negate.git", "reference": "1.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_process_negate-1.0.1.zip", "reference": "1.0.1", "shasum": "83d1bf76d30459c7c5e9a90bc3891e3fe3045389"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "1.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3589301"}], "description": "Provides a migrate process plugin for negating a source value", "homepage": "https://www.drupal.org/project/migrate_process_negate", "support": {"source": "https://git.drupalcode.org/project/migrate_process_negate"}}, {"name": "drupal/migrate_tools", "version": "5.1.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/migrate_tools.git", "reference": "8.x-5.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/migrate_tools-8.x-5.1.zip", "reference": "8.x-5.1", "shasum": "2c1a9d35d318a0e1de30a99fbf64bedd4e65cee1"}, "require": {"drupal/core": "^9.1", "drupal/migrate_plus": "^5", "php": ">=7.1"}, "require-dev": {"drupal/migrate_source_csv": "^3.0", "drush/drush": "^10"}, "suggest": {"drush/drush": "^9 || ^10"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-5.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mikeryan", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/heddn", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Tools to assist in developing and running migrations.", "homepage": "http://drupal.org/project/migrate_tools", "support": {"source": "https://git.drupalcode.org/project/migrate_tools", "issues": "https://www.drupal.org/project/issues/migrate_tools", "slack": "#migrate"}}, {"name": "drupal/nexx_integration", "version": "3.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/nexx_integration.git", "reference": "8.x-3.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/nexx_integration-8.x-3.4.zip", "reference": "8.x-3.4", "shasum": "c100bfa79594560fffb770d2e927507d219a4fac"}, "require": {"drupal/core": "^8.8 || ^9", "drupal/token": "~1.0"}, "require-dev": {"burdamagazinorg/thunder-dev-tools": "dev-master"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "chr.fritsch", "homepage": "https://www.drupal.org/user/2103716"}, {"name": "daniel.bosen", "homepage": "https://www.drupal.org/user/404865"}, {"name": "michal.sec", "homepage": "https://www.drupal.org/user/3515781"}, {"name": "mkolar", "homepage": "https://www.drupal.org/user/1980948"}, {"name": "thunderbot", "homepage": "https://www.drupal.org/user/3511180"}, {"name": "tj<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/3411453"}, {"name": "volkerk", "homepage": "https://www.drupal.org/user/57527"}], "description": "Nexx Omnia and player Integration", "homepage": "https://www.drupal.org/project/nexx_integration", "support": {"source": "https://git.drupalcode.org/project/nexx_integration"}}, {"name": "drupal/openid_connect", "version": "1.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/openid_connect.git", "reference": "8.x-1.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/openid_connect-8.x-1.2.zip", "reference": "8.x-1.2", "shasum": "b00290161d7dc54fe1dccca1b0ab91bed920f051"}, "require": {"drupal/core": "^8.8 || ^9", "ext-json": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/2536014"}, {"name": "b<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/613760"}, {"name": "b<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/86106"}, {"name": "jcnventura", "homepage": "https://www.drupal.org/user/122464"}, {"name": "pfrilling", "homepage": "https://www.drupal.org/user/169695"}, {"name": "pj<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/1025236"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/5449"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/28074"}], "description": "A pluggable client implementation for the OpenID Connect protocol.", "homepage": "https://www.drupal.org/project/openid_connect", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/openid_connect", "issues": "https://www.drupal.org/project/issues/openid_connect"}}, {"name": "drupal/paragraphs", "version": "1.12.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/paragraphs.git", "reference": "8.x-1.12"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/paragraphs-8.x-1.12.zip", "reference": "8.x-1.12", "shasum": "3b67d8af1160af42d93a4610be1e02869e428965"}, "require": {"drupal/core": "^8.8 || ^9", "drupal/entity_reference_revisions": "~1.3"}, "require-dev": {"drupal/block_field": "~1.0", "drupal/ctools": "3.x-dev", "drupal/diff": "~1.0", "drupal/entity_browser": "2.x-dev", "drupal/entity_usage": "2.x-dev", "drupal/field_group": "3.x-dev", "drupal/inline_entity_form": "~1.0", "drupal/paragraphs-paragraphs_library": "*", "drupal/replicate": "~1.0", "drupal/search_api": "~1.0", "drupal/search_api_db": "*"}, "suggest": {"drupal/entity_browser": "Recommended for an improved user experience when using the Paragraphs library module"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.12", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/514222"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/282629"}, {"name": "jeroen.b", "homepage": "https://www.drupal.org/user/1853532"}, {"name": "j<PERSON>ller", "homepage": "https://www.drupal.org/user/99012"}, {"name": "mi<PERSON>_dietiker", "homepage": "https://www.drupal.org/user/227761"}], "description": "Enables the creation of Paragraphs entities.", "homepage": "https://www.drupal.org/project/paragraphs", "support": {"source": "https://git.drupalcode.org/project/paragraphs"}}, {"name": "drupal/pathauto", "version": "1.8.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/pathauto.git", "reference": "8.x-1.8"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/pathauto-8.x-1.8.zip", "reference": "8.x-1.8", "shasum": "ede3216abb9c4f77709338d9147334c595046329"}, "require": {"drupal/core": "^8.8 || ^9", "drupal/ctools": "*", "drupal/token": "*"}, "suggest": {"drupal/redirect": "When installed Pathauto will provide a new \"Update Action\" in case your URLs change. This is the recommended update action and is considered the best practice for SEO and usability."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.8", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/53892"}, {"name": "Freso", "homepage": "https://www.drupal.org/user/27504"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}], "description": "Provides a mechanism for modules to automatically generate aliases for the content they manage.", "homepage": "https://www.drupal.org/project/pathauto", "support": {"source": "https://cgit.drupalcode.org/pathauto", "issues": "https://www.drupal.org/project/issues/pathauto", "documentation": "https://www.drupal.org/docs/8/modules/pathauto"}}, {"name": "drupal/private_file_token", "version": "1.0.0-alpha2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/private_file_token.git", "reference": "1.0.0-alpha2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/private_file_token-1.0.0-alpha2.zip", "reference": "1.0.0-alpha2", "shasum": "7c1ecaa62351511fee3f874744eea6bba70ccd7c"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "1.0.0-alpha2", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Project has not opted into security advisory coverage!"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "hideaway", "homepage": "https://www.drupal.org/user/741876"}], "description": "Allow to access private files by an authentication token in the URL.", "homepage": "https://www.drupal.org/project/private_file_token", "support": {"source": "https://git.drupalcode.org/project/private_file_token"}}, {"name": "drupal/redirect_after_login", "version": "2.7.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/redirect_after_login.git", "reference": "8.x-2.7"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/redirect_after_login-8.x-2.7.zip", "reference": "8.x-2.7", "shasum": "3ce87df792e918c2ec5a00e24818c893e558d111"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-2.7", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/u/shamsher_alam", "role": "Author"}, {"name": "pen", "homepage": "https://www.drupal.org/user/2435634"}, {"name": "prempatel2447", "homepage": "https://www.drupal.org/user/3250112"}, {"name": "rahul-kr-sh", "homepage": "https://www.drupal.org/user/3561577"}], "description": "Redirect user after login to a configured url", "homepage": "https://drupal.org/project/redirect_after_login", "support": {"source": "https://git.drupalcode.org/project/redirect_after_login"}}, {"name": "drupal/redirect_after_logout", "version": "1.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/redirect_after_logout.git", "reference": "8.x-1.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/redirect_after_logout-8.x-1.3.zip", "reference": "8.x-1.3", "shasum": "c51e635b16e6800b089b5b2d3a98c937513a191f"}, "require": {"drupal/core": "^9 || ^10"}, "require-dev": {"drupal/masquerade": "^2.0@beta", "drupal/token": "^1.10"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON> (nevergone)", "homepage": "https://www.drupal.org/u/nevergone", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON> (Prashant.c)", "homepage": "https://www.drupal.org/u/prashantc", "role": "Co-maintainer, Drupal 8 port"}], "description": "User redirect, after logout.", "homepage": "https://drupal.org/project/redirect_after_logout", "support": {"source": "https://cgit.drupalcode.org/redirect_after_logout", "issues": "https://drupal.org/project/issues/redirect_after_logout"}}, {"name": "drupal/redis", "version": "1.5.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/redis.git", "reference": "8.x-1.5"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/redis-8.x-1.5.zip", "reference": "8.x-1.5", "shasum": "4283333dc2bf405045765b83ca662acc409a6543"}, "require": {"drupal/core": "^8.8 || ^9"}, "suggest": {"predis/predis": "^1.1.1"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.5", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "autoload": {"psr-4": {"Drupal\\redis\\": "src"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/214652"}, {"name": "pou<PERSON>", "homepage": "https://www.drupal.org/user/240164"}], "description": "Integration of Drupal with the Redis key-value store.", "homepage": "https://www.drupal.org/project/redis", "support": {"source": "https://git.drupalcode.org/project/redis"}}, {"name": "drupal/rest_views", "version": "2.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/rest_views.git", "reference": "2.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/rest_views-2.0.1.zip", "reference": "2.0.1", "shasum": "9b60dcea756bf3bba0a318d6728c5464762eccb5"}, "require": {"drupal/core": "^8.7 || ^9", "php": "^7.1 || ^8"}, "require-dev": {"drupal/entity_reference_revisions": "*", "drupal/geolocation": "*", "drupal/search_api": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "2.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON>", "homepage": "https://drupal.org/u/cburschka", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Expose fields and entity references in REST view displays.", "homepage": "https://www.drupal.org/project/rest_views", "keywords": ["<PERSON><PERSON><PERSON>"], "support": {"source": "https://git.drupalcode.org/project/rest_views", "issues": "https://www.drupal.org/project/issues/rest_views"}}, {"name": "drupal/restui", "version": "1.20.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/restui.git", "reference": "8.x-1.20"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/restui-8.x-1.20.zip", "reference": "8.x-1.20", "shasum": "df1d3c486ee0e7b4e9a24e6523a69c9efe73caff"}, "require": {"drupal/core": "^8.7.7 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.20", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "-enzo-", "homepage": "https://www.drupal.org/user/294937"}, {"name": "clemens.tolboom", "homepage": "https://www.drupal.org/user/125814"}, {"name": "jua<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/682736"}, {"name": "k<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/262198"}], "description": "Provides a user interface to manage REST resources.", "homepage": "https://www.drupal.org/project/restui", "support": {"source": "https://git.drupalcode.org/project/restui"}}, {"name": "drupal/s3fs", "version": "3.0.0-beta4", "source": {"type": "git", "url": "https://git.drupalcode.org/project/s3fs.git", "reference": "8.x-3.0-beta4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/s3fs-8.x-3.0-beta4.zip", "reference": "8.x-3.0-beta4", "shasum": "740f65a9f999c4a914b2ce7732785b229b162363"}, "require": {"aws/aws-sdk-php": "^3.18", "drupal/core": "^8.8 || ^9"}, "require-dev": {"drush/drush": "^10.0"}, "suggest": {"doctrine/cache": "~1.4"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-3.0-beta4", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/213194", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/u/jansete"}, {"name": "<PERSON><PERSON>a", "homepage": "https://www.drupal.org/user/1790054"}, {"name": "jansete", "homepage": "https://www.drupal.org/user/1358146"}, {"name": "naveenvalecha", "homepage": "https://www.drupal.org/user/2665733"}, {"name": "ram4nd", "homepage": "https://www.drupal.org/user/601534"}], "description": "Adds an Amazon Simple Storage Service-based remote file system to Drupal.", "homepage": "https://www.drupal.org/project/s3fs", "support": {"source": "https://git.drupalcode.org/project/s3fs", "issues": "https://www.drupal.org/project/issues/s3fs"}}, {"name": "drupal/scheduler", "version": "1.4.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/scheduler.git", "reference": "8.x-1.4"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/scheduler-8.x-1.4.zip", "reference": "8.x-1.4", "shasum": "5b2203e4688e5d3ac67d0780605809c92c6ece70"}, "require": {"drupal/core": "^8 || ^9"}, "require-dev": {"drupal/devel_generate": "^2.0 || 4.x-dev", "drupal/rules": "^3", "drush/drush": "^9.0 || ^10"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.4", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (<PERSON>)", "homepage": "https://www.drupal.org/u/eric-schaefer", "role": "Maintainer"}, {"name": "<PERSON> (jonathan1055)", "homepage": "https://www.drupal.org/u/jonathan1055", "role": "Maintainer"}, {"name": "<PERSON> (pfrenssen)", "homepage": "https://www.drupal.org/u/pfrenssen", "role": "Maintainer"}, {"name": "<PERSON> (rickmanelius)", "homepage": "https://www.drupal.org/u/rickmanelius", "role": "Maintainer"}], "description": "Automatically publish and unpublish content at specified dates and times.", "homepage": "https://drupal.org/project/scheduler", "support": {"source": "https://git.drupalcode.org/project/scheduler", "issues": "https://www.drupal.org/project/issues/scheduler"}}, {"name": "drupal/scheduler_content_moderation_integration", "version": "1.3.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/scheduler_content_moderation_integration.git", "reference": "8.x-1.3"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/scheduler_content_moderation_integration-8.x-1.3.zip", "reference": "8.x-1.3", "shasum": "27df8713893f6418c99e5d99f2fa9b5dd66fc048"}, "require": {"drupal/core": "^8.7.7 || ^9", "drupal/scheduler": "^1.1"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.3", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "chr.fritsch", "homepage": "https://www.drupal.org/user/2103716"}, {"name": "daniel.bosen", "homepage": "https://www.drupal.org/user/404865"}, {"name": "thunderbot", "homepage": "https://www.drupal.org/user/3511180"}, {"name": "volkerk", "homepage": "https://www.drupal.org/user/57527"}], "description": "Scheduler Content Moderation Integration", "homepage": "https://www.drupal.org/project/scheduler_content_moderation_integration", "support": {"source": "https://git.drupalcode.org/project/scheduler_content_moderation_integration"}}, {"name": "drupal/search_api", "version": "1.22.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/search_api.git", "reference": "8.x-1.22"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/search_api-8.x-1.22.zip", "reference": "8.x-1.22", "shasum": "bac5923161436830eecdf115ef0333b3b931657e"}, "require": {"drupal/core": "^8.8 || ^9"}, "conflict": {"drupal/search_api_solr": "2.* || 3.0 || 3.1"}, "require-dev": {"drupal/language_fallback_fix": "@dev", "drupal/search_api_autocomplete": "@dev", "drupal/search_api_db": "*"}, "suggest": {"drupal/facets": "Adds the ability to create faceted searches.", "drupal/search_api_autocomplete": "Allows adding autocomplete suggestions to search fields.", "drupal/search_api_solr": "Adds support for using Apache Solr as a backend."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.22", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/drunken-monkey"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/u/nick_vh"}, {"name": "See other contributors", "homepage": "https://www.drupal.org/node/790418/committers"}], "description": "Provides a generic framework for modules offering search capabilities.", "homepage": "https://www.drupal.org/project/search_api", "support": {"source": "https://git.drupalcode.org/project/search_api", "issues": "https://www.drupal.org/project/issues/search_api", "irc": "irc://irc.freenode.org/drupal-search-api"}}, {"name": "drupal/search_api_solr", "version": "4.2.6", "source": {"type": "git", "url": "https://git.drupalcode.org/project/search_api_solr.git", "reference": "4.2.6"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/search_api_solr-4.2.6.zip", "reference": "4.2.6", "shasum": "6ea4a66fe2736e0e0ea6f5952f1a30fc10391994"}, "require": {"composer/semver": "^1.0|^3.0", "consolidation/annotated-command": "^2.12|^4.1", "drupal/core": "^9.2 || ^10.0", "drupal/search_api": "~1.21", "ext-dom": "*", "ext-json": "*", "ext-simplexml": "*", "laminas/laminas-stdlib": "^3.2", "maennchen/zipstream-php": "^1.2|^2.0", "php": "^7.3|^8.0", "solarium/solarium": "^6.2.1"}, "conflict": {"drupal/acquia_search_solr": "<1.0.0-beta8", "drupal/search_api_solr_multilingual": "<3.0.0"}, "require-dev": {"drupal/devel": "^4.0", "drupal/facets": "1.x-dev", "drupal/geofield": "1.x-dev", "drupal/search_api_autocomplete": "*", "drupal/search_api_location": "1.x-dev", "drupal/search_api_spellcheck": "3.x-dev", "monolog/monolog": "^1.25", "phayes/geophp": "^1.2"}, "suggest": {"drupal/facets": "Provides facetted search.", "drupal/search_api_autocomplete": "Provides auto complete for search boxes.", "drupal/search_api_location": "Provides location searches.", "drupal/search_api_solr_nlp": "Highly recommended! Provides Solr field types based on natural language processing (NLP).", "drupal/search_api_spellcheck": "Provides spell checking and 'Did You Mean?'."}, "type": "drupal-module", "extra": {"drupal": {"version": "4.2.6", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/u/mkalkbrenner"}, {"name": "Other contributors", "homepage": "https://www.drupal.org/node/982682/committers"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/729614"}, {"name": "c<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/419305"}, {"name": "drunken monkey", "homepage": "https://www.drupal.org/user/205582"}, {"name": "mkalkbrenner", "homepage": "https://www.drupal.org/user/124705"}], "description": "Offers an implementation of the Search API that uses an Apache Solr server for indexing content.", "homepage": "https://www.drupal.org/project/search_api_solr", "support": {"source": "http://git.drupal.org/project/search_api_solr.git", "issues": "https://www.drupal.org/project/issues/search_api_solr", "chat": "https://drupalchat.me/channel/search"}}, {"name": "drupal/simple_oauth", "version": "5.2.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/simple_oauth.git", "reference": "5.2.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/simple_oauth-5.2.0.zip", "reference": "5.2.0", "shasum": "3d2b0f38284190a59e4ac99d6f5d4af424c4d19f"}, "require": {"drupal/consumers": "^1.2", "drupal/core": "^8 || ^9", "lcobucci/jwt": "^4", "league/oauth2-server": "^8.3", "php": ">=7.4", "steverhoades/oauth2-openid-connect-server": "^2.4"}, "require-dev": {"phpspec/prophecy-phpunit": "^2"}, "type": "drupal-module", "extra": {"drupal": {"version": "5.2.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/405824", "email": "<EMAIL>"}, {"name": "e0ipso", "homepage": "https://www.drupal.org/user/550110"}, {"name": "pcambra", "homepage": "https://www.drupal.org/user/122101"}], "description": "The Simple OAuth module for Drupal", "homepage": "https://www.drupal.org/project/simple_oauth", "support": {"source": "https://git.drupalcode.org/project/simple_oauth"}}, {"name": "drupal/smtp", "version": "1.0.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/smtp.git", "reference": "8.x-1.0"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/smtp-8.x-1.0.zip", "reference": "8.x-1.0", "shasum": "c40cc7a3c20d3f743e3a4e53f4cc296748da89fd"}, "require": {"drupal/core": "^8.8 || ^9", "phpmailer/phpmailer": "^6.1.7"}, "suggest": {"drupal/mailsystem": "Allows using SMTP alongside other mail modules."}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "branch-alias": {"dev-8.x-1.x": "1.x-dev"}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "LukeLast", "homepage": "https://www.drupal.org/user/30151"}, {"name": "japerry", "homepage": "https://www.drupal.org/user/45640"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/72012"}, {"name": "o<PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4649"}, {"name": "<PERSON>ashiv", "homepage": "https://www.drupal.org/user/1773304"}, {"name": "wundo", "homepage": "https://www.drupal.org/user/25523"}, {"name": "yettyn", "homepage": "https://www.drupal.org/user/93281"}], "description": "Allow for site emails to be sent through an SMTP server of your choice.", "homepage": "https://www.drupal.org/project/smtp", "support": {"source": "https://git.drupalcode.org/project/smtp", "issues": "https://www.drupal.org/project/issues/smtp"}}, {"name": "drupal/svg_image", "version": "1.15.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/svg_image.git", "reference": "8.x-1.15"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/svg_image-8.x-1.15.zip", "reference": "8.x-1.15", "shasum": "368d0189bb3c59ea40cf52d83c8551b6358aa161"}, "require": {"drupal/core": "^8 || ^9", "enshrined/svg-sanitize": ">=0.9 <1.0"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.15", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0+"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/2870933", "role": "Maintainer"}, {"name": "See contributors", "homepage": "https://www.drupal.org/node/2887125/committers"}], "description": "Overrides the standard image formatter and widget to support SVG files.", "homepage": "http://drupal.org/project/svg_image", "support": {"source": "http://cgit.drupalcode.org/svg_image", "issues": "http://drupal.org/project/svg_image"}}, {"name": "drupal/term_merge", "version": "dev-1.x", "source": {"type": "git", "url": "https://git.drupalcode.org/project/term_merge.git", "reference": "ba0b6febde373a1c70fe34177055257194141e79"}, "require": {"drupal/core": "^8 || ^9", "drupal/term_reference_change": "*"}, "type": "drupal-module", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}, "drupal": {"version": "8.x-1.0-alpha2+2-dev", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Dev releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/111415"}, {"name": "bleen", "homepage": "https://www.drupal.org/user/77375"}, {"name": "bucefal91", "homepage": "https://www.drupal.org/user/504128"}, {"name": "daniel_j", "homepage": "https://www.drupal.org/user/970952"}, {"name": "eli", "homepage": "https://www.drupal.org/user/49774"}, {"name": "legolas<PERSON>", "homepage": "https://www.drupal.org/user/2480548"}, {"name": "nylin", "homepage": "https://www.drupal.org/user/816040"}], "description": "Allows users to merge taxonomy terms in the same vocabulary together.", "homepage": "https://www.drupal.org/project/term_merge", "support": {"source": "https://git.drupalcode.org/project/term_merge"}}, {"name": "drupal/term_reference_change", "version": "1.0.0-beta1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/term_reference_change.git", "reference": "8.x-1.0-beta1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/term_reference_change-8.x-1.0-beta1.zip", "reference": "8.x-1.0-beta1", "shasum": "166711a0c8f9c54055250deac7d74f098a25c885"}, "require": {"drupal/core": "^8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0-beta1", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "legolas<PERSON>", "homepage": "https://www.drupal.org/user/2480548"}], "description": "Allows term references to be changed in bulk.", "homepage": "https://www.drupal.org/project/term_reference_change", "support": {"source": "https://git.drupalcode.org/project/term_reference_change"}}, {"name": "drupal/token", "version": "1.10.0", "source": {"type": "git", "url": "https://git.drupalcode.org/project/token.git", "reference": "8.x-1.10"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/token-8.x-1.10.zip", "reference": "8.x-1.10", "shasum": "8b81224ab0420221b292e8d3b66d0da726317400"}, "require": {"drupal/core": "^8.8 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.10", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/214652"}, {"name": "<PERSON>", "homepage": "https://www.drupal.org/user/53892"}, {"name": "eaton", "homepage": "https://www.drupal.org/user/16496"}, {"name": "fago", "homepage": "https://www.drupal.org/user/16747"}, {"name": "greggles", "homepage": "https://www.drupal.org/user/36762"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://www.drupal.org/user/4420"}], "description": "Provides a user interface for the Token API, some missing core tokens.", "homepage": "https://www.drupal.org/project/token", "support": {"source": "https://git.drupalcode.org/project/token"}}, {"name": "drupal/typed_data", "version": "1.0.0-beta1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/typed_data.git", "reference": "8.x-1.0-beta1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/typed_data-8.x-1.0-beta1.zip", "reference": "8.x-1.0-beta1", "shasum": "73d079f29c7a5679e0db3e28ae888e8cce2f1335"}, "require": {"drupal/core": "^8.8.2 || ^9"}, "type": "drupal-module", "extra": {"drupal": {"version": "8.x-1.0-beta1", "datestamp": "**********", "security-coverage": {"status": "not-covered", "message": "Beta releases are not covered by Drupal security advisories."}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TR", "homepage": "https://www.drupal.org/user/202830"}, {"name": "fago", "homepage": "https://www.drupal.org/user/16747"}], "description": "Extends the core Typed Data API with new APIs and features.", "homepage": "https://www.drupal.org/project/typed_data", "support": {"source": "https://git.drupalcode.org/project/typed_data", "issues": "https://www.drupal.org/project/issues/typed_data"}}, {"name": "drupal/webform", "version": "6.1.2", "source": {"type": "git", "url": "https://git.drupalcode.org/project/webform.git", "reference": "6.1.2"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/webform-6.1.2.zip", "reference": "6.1.2", "shasum": "3afb96566f5d31474483e163b4e0138d43cffdcd"}, "require": {"drupal/core": "^8.8 || ^9"}, "require-dev": {"drupal/address": "~1.0", "drupal/bootstrap": "~3.0", "drupal/captcha": "~1.0", "drupal/chosen": "~3.0", "drupal/clientside_validation": "*", "drupal/clientside_validation_jquery": "*", "drupal/devel": "*", "drupal/entity": "~1.0", "drupal/entity_print": "*", "drupal/gnode": "*", "drupal/group": "*", "drupal/jquery_ui": "~1.0", "drupal/jquery_ui_checkboxradio": "*", "drupal/jquery_ui_datepicker": "~1.0", "drupal/lingotek": "~3.0", "drupal/mailsystem": "~4.0", "drupal/paragraphs": "~1.0", "drupal/select2": "~1.0", "drupal/smtp": "~1.0", "drupal/styleguide": "~1.0", "drupal/telephone_validation": "~2.0", "drupal/token": "*", "drupal/variationcache": "~1.0", "drupal/webform_access": "*", "drupal/webform_attachment": "*", "drupal/webform_clientside_validation": "*", "drupal/webform_devel": "*", "drupal/webform_entity_print": "*", "drupal/webform_group": "*", "drupal/webform_node": "*", "drupal/webform_options_limit": "*", "drupal/webform_scheduled_email": "*", "drupal/webform_share": "*", "drupal/webform_ui": "*"}, "suggest": {"drupal/jquery_ui_checkboxradio": "Provides jQuery UI Checkboxradio library. Required by the Webform jQueryUI Buttons module. The Webform jQueryUI Buttons module is deprecated because jQueryUI is no longer maintained.", "drupal/jquery_ui_datepicker": "Provides jQuery UI Datepicker library. Required to support datepickers. The Webform jQueryUI Datepicker module is deprecated because jQueryUI is no longer maintained."}, "type": "drupal-module", "extra": {"drupal": {"version": "6.1.2", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}, "drush": {"services": {"drush.services.yml": "^9 || ^10"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON> (jrockowitz)", "homepage": "https://www.drupal.org/u/jrockowitz", "role": "Maintainer"}, {"name": "<PERSON> (bucefal91)", "homepage": "https://www.drupal.org/u/bucefal91", "role": "Co-maintainer"}, {"name": "Contributors", "homepage": "https://www.drupal.org/node/7404/committers", "role": "Contributor"}, {"name": "quicksketch", "homepage": "https://www.drupal.org/user/35821"}, {"name": "torotil", "homepage": "https://www.drupal.org/user/865256"}], "description": "Enables the creation of webforms and questionnaires.", "homepage": "https://drupal.org/project/webform", "support": {"source": "https://git.drupalcode.org/project/webform", "issues": "https://www.drupal.org/project/issues/webform?version=8.x", "docs": "https://www.drupal.org/docs/8/modules/webform", "forum": "https://drupal.stackexchange.com/questions/tagged/webform"}}, {"name": "drupal/webform_rest", "version": "4.0.1", "source": {"type": "git", "url": "https://git.drupalcode.org/project/webform_rest.git", "reference": "4.0.1"}, "dist": {"type": "zip", "url": "https://ftp.drupal.org/files/projects/webform_rest-4.0.1.zip", "reference": "4.0.1", "shasum": "bc73009fdc347b24c4c0119f1b6a38f2adc7ff61"}, "require": {"drupal/core": "^8 || ^9", "drupal/restui": "*", "drupal/webform": "*"}, "type": "drupal-module", "extra": {"drupal": {"version": "4.0.1", "datestamp": "**********", "security-coverage": {"status": "covered", "message": "Covered by <PERSON><PERSON><PERSON>'s security advisory policy"}}}, "notification-url": "https://packages.drupal.org/8/downloads", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://www.drupal.org/user/3557178"}, {"name": "d<PERSON>para", "homepage": "https://www.drupal.org/user/3557179"}, {"name": "imclean", "homepage": "https://www.drupal.org/user/112588"}, {"name": "joaomarques736", "homepage": "https://www.drupal.org/user/3557181"}], "description": "Allows retrieving webform elements and submitting webforms via REST", "homepage": "https://www.drupal.org/project/webform_rest", "support": {"source": "https://git.drupalcode.org/project/webform_rest"}}, {"name": "e0ipso/shaper", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/e0ipso/shaper.git", "reference": "7d73018ec4fe8de9730dfe755067cc02460e1a38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/e0ipso/shaper/zipball/7d73018ec4fe8de9730dfe755067cc02460e1a38", "reference": "7d73018ec4fe8de9730dfe755067cc02460e1a38", "shasum": ""}, "require": {"justinrainbow/json-schema": "^5.2"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4", "phpunit/phpcov": "^8.2", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Shaper\\": "src", "Shaper\\Tests\\": "tests/src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Lightweight library to handle in and out transformations in PHP.", "support": {"issues": "https://github.com/e0ipso/shaper/issues", "source": "https://github.com/e0ipso/shaper/tree/1.2.4"}, "time": "2021-05-19T09:42:57+00:00"}, {"name": "egulias/email-validator", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ee0db30118f661fb166bcffbf5d82032df484697"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ee0db30118f661fb166bcffbf5d82032df484697", "reference": "ee0db30118f661fb166bcffbf5d82032df484697", "shasum": ""}, "require": {"doctrine/lexer": "^1.2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.1.2"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2021-10-11T09:18:27+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.14.1", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "307b42066fb0b76b5119f5e1f0826e18fefabe95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/307b42066fb0b76b5119f5e1f0826e18fefabe95", "reference": "307b42066fb0b76b5119f5e1f0826e18fefabe95", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.0 || ^8.0"}, "require-dev": {"codeclimate/php-test-reporter": "^0.1.2", "phpunit/phpunit": "^6.5 || ^8.5"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.14.1"}, "time": "2021-08-09T23:46:54+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5"}, "time": "2020-06-16T21:01:06+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2021-10-22T20:56:57+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "1afdd860a2566ed3c2b0b4a3de6e23434a79ec85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/1afdd860a2566ed3c2b0b4a3de6e23434a79ec85", "reference": "1afdd860a2566ed3c2b0b4a3de6e23434a79ec85", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.8.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2021-10-05T13:56:00+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "5.2.11", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "2ab6744b7296ded80f8cc4f9509abbff393399aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/2ab6744b7296ded80f8cc4f9509abbff393399aa", "reference": "2ab6744b7296ded80f8cc4f9509abbff393399aa", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/justinrainbow/json-schema/issues", "source": "https://github.com/justinrainbow/json-schema/tree/5.2.11"}, "time": "2021-07-22T09:24:00+00:00"}, {"name": "laminas/laminas-diactoros", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diactoros.git", "reference": "0c26ef1d95b6d7e6e3943a243ba3dc0797227199"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diactoros/zipball/0c26ef1d95b6d7e6e3943a243ba3dc0797227199", "reference": "0c26ef1d95b6d7e6e3943a243ba3dc0797227199", "shasum": ""}, "require": {"php": "^7.3 || ~8.0.0 || ~8.1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "conflict": {"phpspec/prophecy": "<1.9.0", "zendframework/zend-diactoros": "*"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-gd": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^0.8.0", "laminas/laminas-coding-standard": "~1.0.0", "php-http/psr7-integration-tests": "^1.1", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.1", "psalm/plugin-phpunit": "^0.14.0", "vimeo/psalm": "^4.3"}, "type": "library", "extra": {"laminas": {"config-provider": "Laminas\\Diactoros\\ConfigProvider", "module": "Laminas\\Diactoros"}}, "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/marshal_uri_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php", "src/functions/create_uploaded_file.legacy.php", "src/functions/marshal_headers_from_sapi.legacy.php", "src/functions/marshal_method_from_sapi.legacy.php", "src/functions/marshal_protocol_version_from_sapi.legacy.php", "src/functions/marshal_uri_from_sapi.legacy.php", "src/functions/normalize_server.legacy.php", "src/functions/normalize_uploaded_files.legacy.php", "src/functions/parse_cookie_header.legacy.php"], "psr-4": {"Laminas\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-17", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-diactoros/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-diactoros/issues", "rss": "https://github.com/laminas/laminas-diactoros/releases.atom", "source": "https://github.com/laminas/laminas-diactoros"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-09-22T03:54:36+00:00"}, {"name": "laminas/laminas-escaper", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-escaper.git", "reference": "891ad70986729e20ed2e86355fcf93c9dc238a5f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-escaper/zipball/891ad70986729e20ed2e86355fcf93c9dc238a5f", "reference": "891ad70986729e20ed2e86355fcf93c9dc238a5f", "shasum": ""}, "require": {"php": "^7.3 || ~8.0.0 || ~8.1.0"}, "conflict": {"zendframework/zend-escaper": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.3.0", "phpunit/phpunit": "^9.3", "psalm/plugin-phpunit": "^0.12.2", "vimeo/psalm": "^3.16"}, "suggest": {"ext-iconv": "*", "ext-mbstring": "*"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Escaper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Securely and safely escape HTML, HTML attributes, JavaScript, CSS, and URLs", "homepage": "https://laminas.dev", "keywords": ["escaper", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-escaper/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-escaper/issues", "rss": "https://github.com/laminas/laminas-escaper/releases.atom", "source": "https://github.com/laminas/laminas-escaper"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-09-02T17:10:53+00:00"}, {"name": "laminas/laminas-feed", "version": "2.15.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-feed.git", "reference": "3ef837a12833c74b438d2c3780023c4244e0abae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-feed/zipball/3ef837a12833c74b438d2c3780023c4244e0abae", "reference": "3ef837a12833c74b438d2c3780023c4244e0abae", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "laminas/laminas-escaper": "^2.9", "laminas/laminas-stdlib": "^3.6", "php": "^7.3 || ~8.0.0 || ~8.1.0"}, "conflict": {"laminas/laminas-servicemanager": "<3.3", "zendframework/zend-feed": "*"}, "require-dev": {"laminas/laminas-cache": "^2.7.2", "laminas/laminas-coding-standard": "~2.2.1", "laminas/laminas-db": "^2.13.3", "laminas/laminas-http": "^2.15", "laminas/laminas-servicemanager": "^3.7", "laminas/laminas-validator": "^2.15", "phpunit/phpunit": "^9.5.5", "psalm/plugin-phpunit": "^0.13.0", "psr/http-message": "^1.0.1", "vimeo/psalm": "^4.1"}, "suggest": {"laminas/laminas-cache": "Laminas\\Cache component, for optionally caching feeds between requests", "laminas/laminas-db": "Laminas\\Db component, for use with PubSubHubbub", "laminas/laminas-http": "Laminas\\Http for PubSubHubbub, and optionally for use with Laminas\\Feed\\Reader", "laminas/laminas-servicemanager": "Laminas\\ServiceManager component, for easily extending ExtensionManager implementations", "laminas/laminas-validator": "Laminas\\Validator component, for validating email addresses used in Atom feeds and entries when using the Writer subcomponent", "psr/http-message": "PSR-7 ^1.0.1, if you wish to use Laminas\\Feed\\Reader\\Http\\Psr7ResponseDecorator"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Feed\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides functionality for consuming RSS and Atom feeds", "homepage": "https://laminas.dev", "keywords": ["feed", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-feed/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-feed/issues", "rss": "https://github.com/laminas/laminas-feed/releases.atom", "source": "https://github.com/laminas/laminas-feed"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-09-20T18:11:11+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.6.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "db581851a092246ad99e12d4fddf105184924c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/db581851a092246ad99e12d4fddf105184924c71", "reference": "db581851a092246ad99e12d4fddf105184924c71", "shasum": ""}, "require": {"php": "^7.3 || ~8.0.0 || ~8.1.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~2.3.0", "phpbench/phpbench": "^0.17.1", "phpunit/phpunit": "~9.3.7", "psalm/plugin-phpunit": "^0.16.0", "vimeo/psalm": "^4.7"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-11-10T11:33:52+00:00"}, {"name": "lcobucci/clock", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/lcobucci/clock.git", "reference": "353d83fe2e6ae95745b16b3d911813df6a05bfb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/clock/zipball/353d83fe2e6ae95745b16b3d911813df6a05bfb3", "reference": "353d83fe2e6ae95745b16b3d911813df6a05bfb3", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"infection/infection": "^0.17", "lcobucci/coding-standard": "^6.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/php-code-coverage": "9.1.4", "phpunit/phpunit": "9.3.7"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\Clock\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Yet another clock abstraction", "support": {"issues": "https://github.com/lcobucci/clock/issues", "source": "https://github.com/lcobucci/clock/tree/2.0.x"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2020-08-27T18:56:02+00:00"}, {"name": "lcobucci/jwt", "version": "4.1.5", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "fe2d89f2eaa7087af4aa166c6f480ef04e000582"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/fe2d89f2eaa7087af4aa166c6f480ef04e000582", "reference": "fe2d89f2eaa7087af4aa166c6f480ef04e000582", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-sodium": "*", "lcobucci/clock": "^2.0", "php": "^7.4 || ^8.0"}, "require-dev": {"infection/infection": "^0.21", "lcobucci/coding-standard": "^6.0", "mikey179/vfsstream": "^1.6.7", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/php-invoker": "^3.1", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/4.1.5"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2021-09-28T19:34:56+00:00"}, {"name": "league/event", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/event.git", "reference": "d2cc124cf9a3fab2bb4ff963307f60361ce4d119"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/event/zipball/d2cc124cf9a3fab2bb4ff963307f60361ce4d119", "reference": "d2cc124cf9a3fab2bb4ff963307f60361ce4d119", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "^2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}}, "autoload": {"psr-4": {"League\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Event package", "keywords": ["emitter", "event", "listener"], "support": {"issues": "https://github.com/thephpleague/event/issues", "source": "https://github.com/thephpleague/event/tree/master"}, "time": "2018-11-26T11:52:41+00:00"}, {"name": "league/oauth2-server", "version": "8.3.3", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-server.git", "reference": "f5698a3893eda9a17bcd48636990281e7ca77b2a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-server/zipball/f5698a3893eda9a17bcd48636990281e7ca77b2a", "reference": "f5698a3893eda9a17bcd48636990281e7ca77b2a", "shasum": ""}, "require": {"defuse/php-encryption": "^2.2.1", "ext-json": "*", "ext-openssl": "*", "lcobucci/jwt": "^3.4.6 || ^4.0.4", "league/event": "^2.2", "php": "^7.2 || ^8.0", "psr/http-message": "^1.0.1"}, "replace": {"league/oauth2server": "*", "lncd/oauth2": "*"}, "require-dev": {"laminas/laminas-diactoros": "^2.4.1", "phpstan/phpstan": "^0.12.57", "phpstan/phpstan-phpunit": "^0.12.16", "phpunit/phpunit": "^8.5.13", "roave/security-advisories": "dev-master"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.noexceptions.io", "role": "Developer"}], "description": "A lightweight and powerful OAuth 2.0 authorization and resource server library with support for all the core specification grants. This library will allow you to secure your API with OAuth and allow your applications users to approve apps that want to access their data from your API.", "homepage": "https://oauth2.thephpleague.com/", "keywords": ["Authentication", "api", "auth", "authorisation", "authorization", "o<PERSON>h", "oauth 2", "oauth 2.0", "oauth2", "protect", "resource", "secure", "server"], "support": {"issues": "https://github.com/thephpleague/oauth2-server/issues", "source": "https://github.com/thephpleague/oauth2-server/tree/8.3.3"}, "funding": [{"url": "https://github.com/sephster", "type": "github"}], "time": "2021-10-11T20:41:49+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/master"}, "funding": [{"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2020-05-30T13:11:16+00:00"}, {"name": "masterminds/html5", "version": "2.7.5", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f640ac1bdddff06ea333a920c95bbad8872429ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f640ac1bdddff06ea333a920c95bbad8872429ab", "reference": "f640ac1bdddff06ea333a920c95bbad8872429ab", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-libxml": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.7.5"}, "time": "2021-07-01T14:25:37+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "reference": "9b87907a81b87bc76d19a7fb2d61e61486ee9edb", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.6.1"}, "time": "2021-06-14T00:11:39+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.3", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "b942d263c641ddb5190929ff840c68f78713e937"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/b942d263c641ddb5190929ff840c68f78713e937", "reference": "b942d263c641ddb5190929ff840c68f78713e937", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.3"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2021-07-05T08:18:36+00:00"}, {"name": "nikic/php-parser", "version": "v4.13.2", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "210577fe3cf7badcc5814d99455df46564f3c077"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/210577fe3cf7badcc5814d99455df46564f3c077", "reference": "210577fe3cf7badcc5814d99455df46564f3c077", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.13.2"}, "time": "2021-11-30T19:35:32+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "pear/archive_tar", "version": "1.4.14", "source": {"type": "git", "url": "https://github.com/pear/Archive_Tar.git", "reference": "4d761c5334c790e45ef3245f0864b8955c562caa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Archive_Tar/zipball/4d761c5334c790e45ef3245f0864b8955c562caa", "reference": "4d761c5334c790e45ef3245f0864b8955c562caa", "shasum": ""}, "require": {"pear/pear-core-minimal": "^1.10.0alpha2", "php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-bz2": "Bz2 compression support.", "ext-xz": "Lzma2 compression support.", "ext-zlib": "Gzip compression support."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Archive_Tar": ""}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Tar file management class with compression support (gzip, bzip2, lzma2)", "homepage": "https://github.com/pear/Archive_Tar", "keywords": ["archive", "tar"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Archive_Tar", "source": "https://github.com/pear/Archive_Tar"}, "funding": [{"url": "https://github.com/mrook", "type": "github"}, {"url": "https://www.patreon.com/michielrook", "type": "patreon"}], "time": "2021-07-20T13:53:39+00:00"}, {"name": "pear/console_getopt", "version": "v1.4.3", "source": {"type": "git", "url": "https://github.com/pear/Console_Getopt.git", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/Console_Getopt/zipball/a41f8d3e668987609178c7c4a9fe48fecac53fa0", "reference": "a41f8d3e668987609178c7c4a9fe48fecac53fa0", "shasum": ""}, "type": "library", "autoload": {"psr-0": {"Console": "./"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["./"], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}, {"name": "Stig Bakken", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Helper"}], "description": "More info available on: http://pear.php.net/package/Console_<PERSON>opt", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=Console_Getopt", "source": "https://github.com/pear/Console_<PERSON>opt"}, "time": "2019-11-20T18:27:48+00:00"}, {"name": "pear/pear-core-minimal", "version": "v1.10.11", "source": {"type": "git", "url": "https://github.com/pear/pear-core-minimal.git", "reference": "68d0d32ada737153b7e93b8d3c710ebe70ac867d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/pear-core-minimal/zipball/68d0d32ada737153b7e93b8d3c710ebe70ac867d", "reference": "68d0d32ada737153b7e93b8d3c710ebe70ac867d", "shasum": ""}, "require": {"pear/console_getopt": "~1.4", "pear/pear_exception": "~1.0"}, "replace": {"rsky/pear-core-min": "self.version"}, "type": "library", "autoload": {"psr-0": {"": "src/"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["src/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead"}], "description": "Minimal set of PEAR core files to be used as composer dependency", "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR", "source": "https://github.com/pear/pear-core-minimal"}, "time": "2021-08-10T22:31:03+00:00"}, {"name": "pear/pear_exception", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/pear/PEAR_Exception.git", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pear/PEAR_Exception/zipball/b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "reference": "b14fbe2ddb0b9f94f5b24cf08783d599f776fff0", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "<9"}, "type": "class", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["PEAR/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["."], "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PEAR Exception base class.", "homepage": "https://github.com/pear/PEAR_Exception", "keywords": ["exception"], "support": {"issues": "http://pear.php.net/bugs/search.php?cmd=display&package_name[]=PEAR_Exception", "source": "https://github.com/pear/PEAR_Exception"}, "time": "2021-03-21T15:43:46+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.5.3", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "baeb7cde6b60b1286912690ab0693c7789a31e71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/baeb7cde6b60b1286912690ab0693c7789a31e71", "reference": "baeb7cde6b60b1286912690ab0693c7789a31e71", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.2", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.6.0", "yoast/phpunit-polyfills": "^1.0.0"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "stevenmaguire/oauth2-microsoft": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.5.3"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "time": "2021-11-25T16:34:11+00:00"}, {"name": "platformsh/config-reader", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/platformsh/config-reader-php.git", "reference": "5511abfdb673ccfcf0eac9eaf65204f73edd90fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/platformsh/config-reader-php/zipball/5511abfdb673ccfcf0eac9eaf65204f73edd90fd", "reference": "5511abfdb673ccfcf0eac9eaf65204f73edd90fd", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "autoload": {"psr-4": {"Platformsh\\ConfigReader\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Small helper to access Platform.sh environment variables", "support": {"issues": "https://github.com/platformsh/config-reader-php/issues", "source": "https://github.com/platformsh/config-reader-php/tree/2.4.0"}, "time": "2020-12-10T20:50:12+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/8622567409010282b7aeebe4bb841fe98b58dcaf", "reference": "8622567409010282b7aeebe4bb841fe98b58dcaf", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.1"}, "time": "2021-03-05T17:36:06+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psy/psysh", "version": "v0.10.12", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "a0d9981aa07ecfcbea28e4bfa868031cca121e7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/a0d9981aa07ecfcbea28e4bfa868031cca121e7d", "reference": "a0d9981aa07ecfcbea28e4bfa868031cca121e7d", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "~4.0|~3.0|~2.0|~1.3", "php": "^8.0 || ^7.0 || ^5.5.9", "symfony/console": "~5.0|~4.0|~3.0|^2.4.2|~2.3.10", "symfony/var-dumper": "~5.0|~4.0|~3.0|~2.7"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "3.17.*"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-main": "0.10.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.10.12"}, "time": "2021-11-30T14:05:36+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "skaught/jquery-focuspoint", "version": "v1.1.3.1", "source": {"type": "git", "url": "https://github.com/skaught/jquery-focuspoint.git", "reference": "dbb2ff0020e0238f22c1b8b922182e52c3244213"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/skaught/jquery-focuspoint/zipball/dbb2ff0020e0238f22c1b8b922182e52c3244213", "reference": "dbb2ff0020e0238f22c1b8b922182e52c3244213", "shasum": ""}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["(MIT or GPL-1.0)"], "description": "jQuery plugin for 'responsive cropping'. Dynamically crop images to fill available space without cutting out the image's subject. Great for full-screen images", "homepage": "http://jonom.github.io/jquery-focuspoint/", "support": {"source": "https://github.com/skaught/jquery-focuspoint/tree/master"}, "time": "2019-12-28T19:28:39+00:00"}, {"name": "solarium/solarium", "version": "6.2.1", "source": {"type": "git", "url": "https://github.com/solariumphp/solarium.git", "reference": "3c13804d3e12b2783ca450a4e8a6d759e496d651"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/solariumphp/solarium/zipball/3c13804d3e12b2783ca450a4e8a6d759e496d651", "reference": "3c13804d3e12b2783ca450a4e8a6d759e496d651", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "symfony/event-dispatcher-contracts": "^1.0 || ^2.0"}, "require-dev": {"escapestudios/symfony2-coding-standard": "^3.11", "guzzlehttp/guzzle": "^7.2", "nyholm/psr7": "^1.2", "php-http/guzzle7-adapter": "^0.1", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^9.5", "roave/security-advisories": "dev-master", "symfony/event-dispatcher": "^4.3 || ^5.0"}, "type": "library", "autoload": {"psr-4": {"Solarium\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "See GitHub contributors", "homepage": "https://github.com/solariumphp/solarium/contributors"}], "description": "PHP Solr client", "homepage": "http://www.solarium-project.org", "keywords": ["php", "search", "solr"], "support": {"issues": "https://github.com/solariumphp/solarium/issues", "source": "https://github.com/solariumphp/solarium/tree/6.2.1"}, "time": "2021-12-26T10:15:52+00:00"}, {"name": "stack/builder", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/stackphp/builder.git", "reference": "a4faaa6f532c6086bc66c29e1bc6c29593e1ca7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stackphp/builder/zipball/a4faaa6f532c6086bc66c29e1bc6c29593e1ca7c", "reference": "a4faaa6f532c6086bc66c29e1bc6c29593e1ca7c", "shasum": ""}, "require": {"php": ">=7.2.0", "symfony/http-foundation": "~2.1|~3.0|~4.0|~5.0", "symfony/http-kernel": "~2.1|~3.0|~4.0|~5.0"}, "require-dev": {"phpunit/phpunit": "~8.0", "symfony/routing": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Stack": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Builder for stack middleware based on HttpKernelInterface.", "keywords": ["stack"], "support": {"issues": "https://github.com/stackphp/builder/issues", "source": "https://github.com/stackphp/builder/tree/v1.0.6"}, "time": "2020-01-30T12:17:27+00:00"}, {"name": "stecman/symfony-console-completion", "version": "0.11.0", "source": {"type": "git", "url": "https://github.com/stecman/symfony-console-completion.git", "reference": "a9502dab59405e275a9f264536c4e1cb61fc3518"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stecman/symfony-console-completion/zipball/a9502dab59405e275a9f264536c4e1cb61fc3518", "reference": "a9502dab59405e275a9f264536c4e1cb61fc3518", "shasum": ""}, "require": {"php": ">=5.3.2", "symfony/console": "~2.3 || ~3.0 || ~4.0 || ~5.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36 || ~5.7 || ~6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Stecman\\Component\\Symfony\\Console\\BashCompletion\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Automatic BASH completion for Symfony Console Component based applications.", "support": {"issues": "https://github.com/stecman/symfony-console-completion/issues", "source": "https://github.com/stecman/symfony-console-completion/tree/0.11.0"}, "time": "2019-11-24T17:03:06+00:00"}, {"name": "steverhoades/oauth2-openid-connect-server", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/steverhoades/oauth2-openid-connect-server.git", "reference": "5f8f0246c1507dcc4d9dbcad32d651fe276c4409"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/steverhoades/oauth2-openid-connect-server/zipball/5f8f0246c1507dcc4d9dbcad32d651fe276c4409", "reference": "5f8f0246c1507dcc4d9dbcad32d651fe276c4409", "shasum": ""}, "require": {"lcobucci/jwt": "4.1.5", "league/oauth2-server": "^5.1|^6.0|^7.0|^8.0"}, "require-dev": {"laminas/laminas-diactoros": "^1.3.2", "phpunit/phpunit": "^5.0|^9.5"}, "type": "library", "autoload": {"psr-4": {"OpenIDConnectServer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An OpenID Connect Server that sites on The PHP League's OAuth2 Server", "support": {"issues": "https://github.com/steverhoades/oauth2-openid-connect-server/issues", "source": "https://github.com/steverhoades/oauth2-openid-connect-server/tree/v2.4.0"}, "time": "2021-10-28T13:38:28+00:00"}, {"name": "symfony-cmf/routing", "version": "2.3.4", "source": {"type": "git", "url": "https://github.com/symfony-cmf/Routing.git", "reference": "bbcdf2f6301d740454ba9ebb8adaefd436c36a6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony-cmf/Routing/zipball/bbcdf2f6301d740454ba9ebb8adaefd436c36a6b", "reference": "bbcdf2f6301d740454ba9ebb8adaefd436c36a6b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/http-kernel": "^4.4 || ^5.0", "symfony/routing": "^4.4 || ^5.0"}, "require-dev": {"symfony-cmf/testing": "^3@dev", "symfony/config": "^4.4 || ^5.0", "symfony/dependency-injection": "^4.4 || ^5.0", "symfony/event-dispatcher": "^4.4 || ^5.0", "symfony/phpunit-bridge": "^5.0"}, "suggest": {"symfony/event-dispatcher": "DynamicRouter can optionally trigger an event at the start of matching. Minimal version (^4.4 || ^5.0)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Cmf\\Component\\Routing\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony CMF Community", "homepage": "https://github.com/symfony-cmf/Routing/contributors"}], "description": "Extends the Symfony routing component for dynamic routes and chaining several routers", "homepage": "http://cmf.symfony.com", "keywords": ["database", "routing"], "support": {"issues": "https://github.com/symfony-cmf/Routing/issues", "source": "https://github.com/symfony-cmf/Routing/tree/2.3.4"}, "time": "2021-11-08T16:33:10+00:00"}, {"name": "symfony/config", "version": "v4.4.36", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "03218ffbd5faeda5e6a97f9109acebf7973ff385"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/03218ffbd5faeda5e6a97f9109acebf7973ff385", "reference": "03218ffbd5faeda5e6a97f9109acebf7973ff385", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<3.4"}, "require-dev": {"symfony/event-dispatcher": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/messenger": "^4.1|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v4.4.36"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-12-12T15:06:47+00:00"}, {"name": "symfony/console", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "329b3a75cc6b16d435ba1b1a41df54a53382a3f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/329b3a75cc6b16d435ba1b1a41df54a53382a3f0", "reference": "329b3a75cc6b16d435ba1b1a41df54a53382a3f0", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T12:23:33+00:00"}, {"name": "symfony/css-selector", "version": "v4.4.27", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "5194f18bd80d106f11efa8f7cd0fbdcc3af96ce6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/5194f18bd80d106f11efa8f7cd0fbdcc3af96ce6", "reference": "5194f18bd80d106f11efa8f7cd0fbdcc3af96ce6", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v4.4.27"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-07-21T12:19:41+00:00"}, {"name": "symfony/debug", "version": "v4.4.31", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "43ede438d4cb52cd589ae5dc070e9323866ba8e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/43ede438d4cb52cd589ae5dc070e9323866ba8e0", "reference": "43ede438d4cb52cd589ae5dc070e9323866ba8e0", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v4.4.31"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-09-24T13:30:14+00:00"}, {"name": "symfony/dependency-injection", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "117d7f132ed7efbd535ec947709d49bec1b9d24b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/117d7f132ed7efbd535ec947709d49bec1b9d24b", "reference": "117d7f132ed7efbd535ec947709d49bec1b9d24b", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/container": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<4.3|>=5.0", "symfony/finder": "<3.4", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<3.4"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0|2.0"}, "require-dev": {"symfony/config": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/yaml": "^4.4|^5.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-15T14:42:25+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "6f981ee24cf69ee7ce9736146d1c57c2780598a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/6f981ee24cf69ee7ce9736146d1c57c2780598a8", "reference": "6f981ee24cf69ee7ce9736146d1c57c2780598a8", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-07-12T14:48:14+00:00"}, {"name": "symfony/dom-crawler", "version": "v4.4.36", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "42de12bee3b5e594977209bcdf58ec4fef8dde39"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/42de12bee3b5e594977209bcdf58ec4fef8dde39", "reference": "42de12bee3b5e594977209bcdf58ec4fef8dde39", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v4.4.36"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-12-28T14:48:02+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "17785c374645def1e884d8ec49976c156c61db4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/17785c374645def1e884d8ec49976c156c61db4d", "reference": "17785c374645def1e884d8ec49976c156c61db4d", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3", "symfony/debug": "^4.4.5", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-12T14:57:39+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8", "reference": "1a024b45369c9d55d76b6b8a241bd20c9ea1cbd8", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-15T14:42:25+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.11", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "01e9a4efac0ee33a05dfdf93b346f62e7d0e998c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/01e9a4efac0ee33a05dfdf93b346f62e7d0e998c", "reference": "01e9a4efac0ee33a05dfdf93b346f62e7d0e998c", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-03-23T15:25:38+00:00"}, {"name": "symfony/filesystem", "version": "v4.4.27", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "517fb795794faf29086a77d99eb8f35e457837a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/517fb795794faf29086a77d99eb8f35e457837a7", "reference": "517fb795794faf29086a77d99eb8f35e457837a7", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v4.4.27"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-07-21T12:19:41+00:00"}, {"name": "symfony/finder", "version": "v4.4.36", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1fef05633cd61b629e963e5d8200fb6b67ecf42c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1fef05633cd61b629e963e5d8200fb6b67ecf42c", "reference": "1fef05633cd61b629e963e5d8200fb6b67ecf42c", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.36"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-12-15T10:33:10+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "ec82e57b5b714dbb69300d348bd840b345e24166"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ec82e57b5b714dbb69300d348bd840b345e24166", "reference": "ec82e57b5b714dbb69300d348bd840b345e24166", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-03T09:24:47+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "f4cbbb6fc428588ce8373802461e7fe84e6809ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/f4cbbb6fc428588ce8373802461e7fe84e6809ab", "reference": "f4cbbb6fc428588ce8373802461e7fe84e6809ab", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T12:23:33+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.35", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "fb793f1381c34b79a43596a532a6a49bd729c9db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/fb793f1381c34b79a43596a532a6a49bd729c9db", "reference": "fb793f1381c34b79a43596a532a6a49bd729c9db", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4.30|^5.3.7", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.43|<2.13,>=2"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v4.4.35"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-24T08:40:10+00:00"}, {"name": "symfony/mime", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "d4365000217b67c01acff407573906ff91bcfb34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/d4365000217b67c01acff407573906ff91bcfb34", "reference": "d4365000217b67c01acff407573906ff91bcfb34", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.1|^6.0", "symfony/property-info": "^4.4|^5.1|^6.0", "symfony/serializer": "^5.2|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-23T10:19:22+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/46cd95797e9df938fdd2b03693b5fca5e64b01ce", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-19T12:13:01+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "63b5bb7db83e5673936d6e3b8b3e022ff6474933"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/63b5bb7db83e5673936d6e3b8b3e022ff6474933", "reference": "63b5bb7db83e5673936d6e3b8b3e022ff6474933", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T09:27:20+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "65bd267525e82759e7d8c4e8ceea44f398838e65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/65bd267525e82759e7d8c4e8ceea44f398838e65", "reference": "65bd267525e82759e7d8c4e8ceea44f398838e65", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T09:27:20+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.23.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8590a5f561694770bdcd3f9b5c69dde6945028e8", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-19T12:13:01+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.23.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9174a3d80210dca8daa7f31fec659150bbeabfc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9174a3d80210dca8daa7f31fec659150bbeabfc6", "reference": "9174a3d80210dca8daa7f31fec659150bbeabfc6", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.23.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T12:26:48+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/9a142215a36a3888e30d0a9eeea9766764e96976", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T09:17:38+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/cc5db0e22b3cb4111010e48785a97f670b350ca5", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-06-05T21:20:04+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.23.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "1100343ed1a92e3a38f9ae122fc0eb21602547be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/1100343ed1a92e3a38f9ae122fc0eb21602547be", "reference": "1100343ed1a92e3a38f9ae122fc0eb21602547be", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.23.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-07-28T13:41:28+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.24.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "5de4ba2d41b15f9bd0e19b2ab9674135813ec98f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/5de4ba2d41b15f9bd0e19b2ab9674135813ec98f", "reference": "5de4ba2d41b15f9bd0e19b2ab9674135813ec98f", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.24.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-09-13T13:58:11+00:00"}, {"name": "symfony/process", "version": "v4.4.35", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "c2098705326addae6e6742151dfade47ac71da1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/c2098705326addae6e6742151dfade47ac71da1b", "reference": "c2098705326addae6e6742151dfade47ac71da1b", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.4.35"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-22T22:36:24+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34", "reference": "22b37c8a3f6b5d94e9cdbd88e1270d96e2f97b34", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0", "symfony/http-foundation": "^4.4 || ^5.0 || ^6.0"}, "require-dev": {"nyholm/psr7": "^1.1", "psr/log": "^1.1 || ^2 || ^3", "symfony/browser-kit": "^4.4 || ^5.0 || ^6.0", "symfony/config": "^4.4 || ^5.0 || ^6.0", "symfony/event-dispatcher": "^4.4 || ^5.0 || ^6.0", "symfony/framework-bundle": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "^4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4@dev || ^6.0"}, "suggest": {"nyholm/psr7": "For a super lightweight PSR-7/17 implementation"}, "type": "symfony-bridge", "extra": {"branch-alias": {"dev-main": "2.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "http://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"issues": "https://github.com/symfony/psr-http-message-bridge/issues", "source": "https://github.com/symfony/psr-http-message-bridge/tree/v2.1.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-05T13:13:39+00:00"}, {"name": "symfony/routing", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "fc9dda0c8496f8ef0a89805c2eabfc43b8cef366"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/fc9dda0c8496f8ef0a89805c2eabfc43b8cef366", "reference": "fc9dda0c8496f8ef0a89805c2eabfc43b8cef366", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "psr/log": "^1|^2|^3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T12:23:33+00:00"}, {"name": "symfony/serializer", "version": "v4.4.35", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "1b2ae02cb1b923987947e013688c51954a80b751"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/1b2ae02cb1b923987947e013688c51954a80b751", "reference": "1b2ae02cb1b923987947e013688c51954a80b751", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0|>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0|1.3.*", "symfony/dependency-injection": "<3.4", "symfony/property-access": "<3.4", "symfony/property-info": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^3.4.41|^4.4.9|^5.0.9", "symfony/property-info": "^3.4.13|~4.0|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v4.4.35"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-24T08:12:42+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "1ab11b933cd6bc5464b08e81e2c5b07dec58b0fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/1ab11b933cd6bc5464b08e81e2c5b07dec58b0fc", "reference": "1ab11b933cd6bc5464b08e81e2c5b07dec58b0fc", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T16:48:04+00:00"}, {"name": "symfony/translation", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "26d330720627b234803595ecfc0191eeabc65190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/26d330720627b234803595ecfc0191eeabc65190", "reference": "26d330720627b234803595ecfc0191eeabc65190", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-04T12:23:33+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "d28150f0f44ce854e942b671fc2620a98aae1b1e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/d28150f0f44ce854e942b671fc2620a98aae1b1e", "reference": "d28150f0f44ce854e942b671fc2620a98aae1b1e", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-08-17T14:20:01+00:00"}, {"name": "symfony/validator", "version": "v4.4.35", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "629f420d8350634fd8ed686d4472c1f10044b265"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/629f420d8350634fd8ed686d4472c1f10044b265", "reference": "629f420d8350634fd8ed686d4472c1f10044b265", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1|^2"}, "conflict": {"doctrine/lexer": "<1.0.2", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.3", "symfony/translation": ">=5.0", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "^1.0|^2.0", "egulias/email-validator": "^2.1.10|^3", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/http-foundation": "^4.1|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^4.3|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the mapping cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v4.4.35"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-22T21:43:32+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.0", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "89ab66eaef230c9cd1992de2e9a1b26652b127b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/89ab66eaef230c9cd1992de2e9a1b26652b127b9", "reference": "89ab66eaef230c9cd1992de2e9a1b26652b127b9", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-29T15:30:56+00:00"}, {"name": "symfony/yaml", "version": "v4.4.34", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "2c309e258adeb9970229042be39b360d34986fad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/2c309e258adeb9970229042be39b360d34986fad", "reference": "2c309e258adeb9970229042be39b360d34986fad", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.34"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-18T18:49:23+00:00"}, {"name": "twig/twig", "version": "v2.14.7", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "8e202327ee1ed863629de9b18a5ec70ac614d88f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/8e202327ee1ed863629de9b18a5ec70ac614d88f", "reference": "8e202327ee1ed863629de9b18a5ec70ac614d88f", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.14-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v2.14.7"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2021-09-17T08:39:54+00:00"}, {"name": "typo3/phar-stream-wrapper", "version": "v3.1.7", "source": {"type": "git", "url": "https://github.com/TYPO3/phar-stream-wrapper.git", "reference": "5cc2f04a4e2f5c7e9cc02a3bdf80fae0f3e11a8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/phar-stream-wrapper/zipball/5cc2f04a4e2f5c7e9cc02a3bdf80fae0f3e11a8c", "reference": "5cc2f04a4e2f5c7e9cc02a3bdf80fae0f3e11a8c", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.0 || ^8.0"}, "require-dev": {"ext-xdebug": "*", "phpspec/prophecy": "^1.10", "symfony/phpunit-bridge": "^5.1"}, "suggest": {"ext-fileinfo": "For PHP builtin file type guessing, otherwise uses internal processing"}, "type": "library", "extra": {"branch-alias": {"dev-master": "v3.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\PharStreamWrapper\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Interceptors for PHP's native phar:// stream handling", "homepage": "https://typo3.org/", "keywords": ["phar", "php", "security", "stream-wrapper"], "support": {"issues": "https://github.com/TYPO3/phar-stream-wrapper/issues", "source": "https://github.com/TYPO3/phar-stream-wrapper/tree/v3.1.7"}, "time": "2021-09-20T19:19:13+00:00"}, {"name": "webflo/drupal-finder", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/webflo/drupal-finder.git", "reference": "c8e5dbe65caef285fec8057a4c718a0d4138d1ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webflo/drupal-finder/zipball/c8e5dbe65caef285fec8057a4c718a0d4138d1ee", "reference": "c8e5dbe65caef285fec8057a4c718a0d4138d1ee", "shasum": ""}, "require": {"ext-json": "*"}, "require-dev": {"mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^4.8"}, "type": "library", "autoload": {"classmap": ["src/DrupalFinder.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Helper class to locate a Drupal installation from a given path.", "support": {"issues": "https://github.com/webflo/drupal-finder/issues", "source": "https://github.com/webflo/drupal-finder/tree/1.2.2"}, "time": "2020-10-27T09:42:17+00:00"}, {"name": "webmozart/assert", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/6964c76c7804814a842473e0c8fd15bab0f18e25", "reference": "6964c76c7804814a842473e0c8fd15bab0f18e25", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.10.0"}, "time": "2021-03-09T10:59:23+00:00"}, {"name": "webmozart/path-util", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/webmozart/path-util.git", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozart/path-util/zipball/d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "reference": "d939f7edc24c9a1bb9c0dee5cb05d8e859490725", "shasum": ""}, "require": {"php": ">=5.3.3", "webmozart/assert": "~1.0"}, "require-dev": {"phpunit/phpunit": "^4.6", "sebastian/version": "^1.0.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Webmozart\\PathUtil\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A robust cross-platform utility for normalizing, comparing and modifying file paths.", "support": {"issues": "https://github.com/webmozart/path-util/issues", "source": "https://github.com/webmozart/path-util/tree/2.3.0"}, "abandoned": "symfony/filesystem", "time": "2015-12-17T08:42:14+00:00"}, {"name": "webonyx/graphql-php", "version": "v0.12.6", "source": {"type": "git", "url": "https://github.com/webonyx/graphql-php.git", "reference": "4c545e5ec4fc37f6eb36c19f5a0e7feaf5979c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/4c545e5ec4fc37f6eb36c19f5a0e7feaf5979c95", "reference": "4c545e5ec4fc37f6eb36c19f5a0e7feaf5979c95", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^4.8", "psr/http-message": "^1.0", "react/promise": "2.*"}, "suggest": {"psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "type": "library", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "support": {"issues": "https://github.com/webonyx/graphql-php/issues", "source": "https://github.com/webonyx/graphql-php/tree/0.12.x"}, "time": "2018-09-02T14:59:54+00:00"}], "packages-dev": [{"name": "chi-teck/drupal-code-generator", "version": "1.33.1", "source": {"type": "git", "url": "https://github.com/Chi-teck/drupal-code-generator.git", "reference": "5f814e980b6f9cf1ca8c74cc9385c3d81090d388"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Chi-teck/drupal-code-generator/zipball/5f814e980b6f9cf1ca8c74cc9385c3d81090d388", "reference": "5f814e980b6f9cf1ca8c74cc9385c3d81090d388", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.5.9", "symfony/console": "^3.4 || ^4.0", "symfony/filesystem": "^2.7 || ^3.4 || ^4.0", "twig/twig": "^1.41 || ^2.12"}, "conflict": {"drush/drush": "< 10.3.2"}, "bin": ["bin/dcg"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["src/bootstrap.php"], "psr-4": {"DrupalCodeGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Drupal code generator", "support": {"issues": "https://github.com/Chi-teck/drupal-code-generator/issues", "source": "https://github.com/Chi-teck/drupal-code-generator/tree/1.33.1"}, "time": "2020-12-05T05:59:11+00:00"}, {"name": "consolidation/config", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/consolidation/config.git", "reference": "cac1279bae7efb5c7fb2ca4c3ba4b8eb741a96c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/config/zipball/cac1279bae7efb5c7fb2ca4c3ba4b8eb741a96c1", "reference": "cac1279bae7efb5c7fb2ca4c3ba4b8eb741a96c1", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "grasmash/expander": "^1", "php": ">=5.4.0"}, "require-dev": {"g1a/composer-test-scenarios": "^3", "php-coveralls/php-coveralls": "^1", "phpunit/phpunit": "^5", "squizlabs/php_codesniffer": "2.*", "symfony/console": "^2.5|^3|^4", "symfony/yaml": "^2.8.11|^3|^4"}, "suggest": {"symfony/yaml": "Required to use Consolidation\\Config\\Loader\\YamlConfigLoader"}, "type": "library", "extra": {"scenarios": {"symfony4": {"require-dev": {"symfony/console": "^4.0"}, "config": {"platform": {"php": "7.1.3"}}}, "symfony2": {"require-dev": {"symfony/console": "^2.8", "symfony/event-dispatcher": "^2.8", "phpunit/phpunit": "^4.8.36"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.4.8"}}}}, "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provide configuration services for a commandline tool.", "support": {"issues": "https://github.com/consolidation/config/issues", "source": "https://github.com/consolidation/config/tree/master"}, "time": "2019-03-03T19:37:04+00:00"}, {"name": "consolidation/filter-via-dot-access-data", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/consolidation/filter-via-dot-access-data.git", "reference": "a53e96c6b9f7f042f5e085bf911f3493cea823c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/filter-via-dot-access-data/zipball/a53e96c6b9f7f042f5e085bf911f3493cea823c6", "reference": "a53e96c6b9f7f042f5e085bf911f3493cea823c6", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "php": ">=5.5.0"}, "require-dev": {"consolidation/robo": "^1.2.3", "g1a/composer-test-scenarios": "^3", "knplabs/github-api": "^2.7", "php-coveralls/php-coveralls": "^1", "php-http/guzzle6-adapter": "^1.1", "phpunit/phpunit": "^5", "squizlabs/php_codesniffer": "^2.8", "symfony/console": "^2.8|^3|^4"}, "type": "library", "extra": {"scenarios": {"phpunit5": {"require-dev": {"phpunit/phpunit": "^5.7.27"}, "remove": ["php-coveralls/php-coveralls"], "config": {"platform": {"php": "5.6.33"}}}}, "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Filter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "This project uses dflydev/dot-access-data to provide simple output filtering for applications built with annotated-command / Robo.", "support": {"source": "https://github.com/consolidation/filter-via-dot-access-data/tree/1.0.0"}, "time": "2019-01-18T06:05:07+00:00"}, {"name": "consolidation/log", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/consolidation/log.git", "reference": "fc9ec5476ba13a31778695bd2d4f2fa0b0684356"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/log/zipball/fc9ec5476ba13a31778695bd2d4f2fa0b0684356", "reference": "fc9ec5476ba13a31778695bd2d4f2fa0b0684356", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1.0", "symfony/console": "^4 || ^5 || ^6"}, "require-dev": {"phpunit/phpunit": ">=7.5.20", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Improved Psr-3 / Psr\\Log logger based on Symfony Console components.", "support": {"issues": "https://github.com/consolidation/log/issues", "source": "https://github.com/consolidation/log/tree/2.0.4"}, "time": "2021-12-30T19:05:18+00:00"}, {"name": "consolidation/robo", "version": "3.0.7", "source": {"type": "git", "url": "https://github.com/consolidation/robo.git", "reference": "57012db2a93c904ed0a7b9d8676c0325c0366bc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/robo/zipball/57012db2a93c904ed0a7b9d8676c0325c0366bc8", "reference": "57012db2a93c904ed0a7b9d8676c0325c0366bc8", "shasum": ""}, "require": {"consolidation/annotated-command": "^4.3", "consolidation/config": "^1.2.1 || ^2.0.1", "consolidation/log": "^1.1.1 || ^2.0.2", "consolidation/output-formatters": "^4.1.2", "consolidation/self-update": "^2.0", "league/container": "^3.3.1", "php": ">=7.1.3", "symfony/console": "^4.4.19 || ^5 || ^6", "symfony/event-dispatcher": "^4.4.19 || ^5 || ^6", "symfony/filesystem": "^4.4.9 || ^5 || ^6", "symfony/finder": "^4.4.9 || ^5 || ^6", "symfony/process": "^4.4.9 || ^5", "symfony/yaml": "^4.4 || ^5 || ^6"}, "conflict": {"codegyre/robo": "*"}, "require-dev": {"natxet/cssmin": "3.0.4", "patchwork/jsqueeze": "^2", "pear/archive_tar": "^1.4.4", "phpunit/phpunit": "^7.5.20 || ^8", "squizlabs/php_codesniffer": "^3.6", "yoast/phpunit-polyfills": "^0.2.0"}, "suggest": {"natxet/cssmin": "For minifying CSS files in taskMinify", "patchwork/jsqueeze": "For minifying JS files in taskMinify", "pear/archive_tar": "Allows tar archives to be created and extracted in taskPack and taskExtract, respectively.", "totten/lurkerlite": "For monitoring filesystem changes in taskWatch"}, "bin": ["robo"], "type": "library", "extra": {"scenarios": {"symfony4": {"require": {"symfony/console": "^4.4.11", "symfony/event-dispatcher": "^4.4.11", "symfony/filesystem": "^4.4.11", "symfony/finder": "^4.4.11", "symfony/process": "^4.4.11", "phpunit/phpunit": "^6", "nikic/php-parser": "^2"}, "remove": ["codeception/phpunit-wrapper"], "config": {"platform": {"php": "7.1.3"}}}}, "branch-alias": {"dev-master": "2.x-dev", "dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Robo\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Modern task runner", "support": {"issues": "https://github.com/consolidation/robo/issues", "source": "https://github.com/consolidation/robo/tree/3.0.7"}, "time": "2021-12-31T01:01:31+00:00"}, {"name": "consolidation/self-update", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/consolidation/self-update.git", "reference": "117dcc9494dc970a6ae307103c41d654e6253bc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/self-update/zipball/117dcc9494dc970a6ae307103c41d654e6253bc4", "reference": "117dcc9494dc970a6ae307103c41d654e6253bc4", "shasum": ""}, "require": {"composer/semver": "^3.2", "php": ">=5.5.0", "symfony/console": "^2.8 || ^3 || ^4 || ^5 || ^6", "symfony/filesystem": "^2.5 || ^3 || ^4 || ^5 || ^6"}, "bin": ["scripts/release"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"SelfUpdate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}], "description": "Provides a self:update command for Symfony Console applications.", "support": {"issues": "https://github.com/consolidation/self-update/issues", "source": "https://github.com/consolidation/self-update/tree/2.0.3"}, "time": "2021-12-30T19:08:32+00:00"}, {"name": "consolidation/site-alias", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/consolidation/site-alias.git", "reference": "e2784362e98f315c996fb2b9ed80a9118a0ba8b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-alias/zipball/e2784362e98f315c996fb2b9ed80a9118a0ba8b7", "reference": "e2784362e98f315c996fb2b9ed80a9118a0ba8b7", "shasum": ""}, "require": {"consolidation/config": "^1.2.1|^2", "php": ">=5.5.0", "symfony/finder": "~2.3|^3|^4.4|^5"}, "require-dev": {"php-coveralls/php-coveralls": "^2.4.2", "phpunit/phpunit": ">=7", "squizlabs/php_codesniffer": "^3", "symfony/var-dumper": "^4", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteAlias\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Manage alias records for local and remote sites.", "support": {"issues": "https://github.com/consolidation/site-alias/issues", "source": "https://github.com/consolidation/site-alias/tree/3.1.3"}, "time": "2022-01-03T19:00:28+00:00"}, {"name": "consolidation/site-process", "version": "4.1.1", "source": {"type": "git", "url": "https://github.com/consolidation/site-process.git", "reference": "4817b35b2f98a2e3ad82956a968b49f7b257d26c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/consolidation/site-process/zipball/4817b35b2f98a2e3ad82956a968b49f7b257d26c", "reference": "4817b35b2f98a2e3ad82956a968b49f7b257d26c", "shasum": ""}, "require": {"consolidation/config": "^1.2.1|^2", "consolidation/site-alias": "^3", "php": ">=7.1.3", "symfony/console": "^2.8.52|^3|^4.4|^5", "symfony/process": "^4.3.4"}, "require-dev": {"phpunit/phpunit": "^7.5.20|^8.5.14", "squizlabs/php_codesniffer": "^3", "yoast/phpunit-polyfills": "^0.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.x-dev"}}, "autoload": {"psr-4": {"Consolidation\\SiteProcess\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A thin wrapper around the Symfony Process Component that allows applications to use the Site Alias library to specify the target for a remote call.", "support": {"issues": "https://github.com/consolidation/site-process/issues", "source": "https://github.com/consolidation/site-process/tree/4.1.1"}, "time": "2022-01-03T18:57:42+00:00"}, {"name": "drush/drush", "version": "10.6.2", "source": {"type": "git", "url": "https://github.com/drush-ops/drush.git", "reference": "0a570a16ec63259eb71195aba5feab532318b337"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/drush-ops/drush/zipball/0a570a16ec63259eb71195aba5feab532318b337", "reference": "0a570a16ec63259eb71195aba5feab532318b337", "shasum": ""}, "require": {"chi-teck/drupal-code-generator": "^1.32.1", "composer/semver": "^1.4 || ^3", "consolidation/config": "^1.2", "consolidation/filter-via-dot-access-data": "^1", "consolidation/robo": "^1.4.11 || ^2 || ^3", "consolidation/site-alias": "^3.0.0@stable", "consolidation/site-process": "^2.1 || ^4", "enlightn/security-checker": "^1", "ext-dom": "*", "grasmash/yaml-expander": "^1.1.1", "guzzlehttp/guzzle": "^6.3 || ^7.0", "league/container": "^2.5 || ^3.4", "php": ">=7.1.3", "psr/log": "~1.0", "psy/psysh": ">=0.6 <0.11", "symfony/event-dispatcher": "^3.4 || ^4.0", "symfony/finder": "^3.4 || ^4.0 || ^5", "symfony/var-dumper": "^3.4 || ^4.0 || ^5.0", "symfony/yaml": "^3.4 || ^4.0", "webflo/drupal-finder": "^1.2", "webmozart/path-util": "^2.1.0"}, "conflict": {"drupal/migrate_run": "*", "drupal/migrate_tools": "<= 5"}, "require-dev": {"composer/installers": "^1.7", "cweagans/composer-patches": "~1.0", "david-garcia/phpwhois": "4.3.0", "drupal/alinks": "1.0.0", "drupal/core-recommended": "^8.8", "phpunit/phpunit": ">=7.5.20", "squizlabs/php_codesniffer": "^2.7 || ^3", "vlucas/phpdotenv": "^2.4", "yoast/phpunit-polyfills": "^0.2.0"}, "bin": ["drush"], "type": "library", "extra": {"installer-paths": {"sut/core": ["type:drupal-core"], "sut/libraries/{$name}": ["type:drupal-library"], "sut/modules/unish/{$name}": ["drupal/devel"], "sut/themes/unish/{$name}": ["drupal/empty_theme"], "sut/modules/contrib/{$name}": ["type:drupal-module"], "sut/profiles/contrib/{$name}": ["type:drupal-profile"], "sut/themes/contrib/{$name}": ["type:drupal-theme"], "sut/drush/contrib/{$name}": ["type:drupal-drush"]}}, "autoload": {"psr-4": {"Drush\\": "src/", "Drush\\Internal\\": "src/internal-forks"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "greg.1.and<PERSON>@greenknowe.org"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "j<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Drush is a command line shell and scripting interface for <PERSON><PERSON><PERSON>, a veritable Swiss Army knife designed to make life easier for those of us who spend some of our working hours hacking away at the command prompt.", "homepage": "http://www.drush.org", "support": {"forum": "http://drupal.stackexchange.com/questions/tagged/drush", "irc": "irc://irc.freenode.org/drush", "issues": "https://github.com/drush-ops/drush/issues", "slack": "https://drupal.slack.com/messages/C62H9CWQM", "source": "https://github.com/drush-ops/drush/tree/10.6.2"}, "funding": [{"url": "https://github.com/weitzman", "type": "github"}], "time": "2021-12-15T17:09:54+00:00"}, {"name": "enlightn/security-checker", "version": "v1.9.0", "source": {"type": "git", "url": "https://github.com/enlightn/security-checker.git", "reference": "dc5bce653fa4d9c792e9dcffa728c0642847c1e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/enlightn/security-checker/zipball/dc5bce653fa4d9c792e9dcffa728c0642847c1e1", "reference": "dc5bce653fa4d9c792e9dcffa728c0642847c1e1", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.6", "symfony/console": "^3.4|^4|^5", "symfony/finder": "^3|^4|^5", "symfony/process": "^3.4|^4|^5", "symfony/yaml": "^3.4|^4|^5"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^2.18", "phpunit/phpunit": "^5.5|^6|^7|^8|^9"}, "bin": ["security-checker"], "type": "library", "autoload": {"psr-4": {"Enlightn\\SecurityChecker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PHP dependency vulnerabilities scanner based on the Security Advisories Database.", "keywords": ["package", "php", "scanner", "security", "security advisories", "vulnerability scanner"], "support": {"issues": "https://github.com/enlightn/security-checker/issues", "source": "https://github.com/enlightn/security-checker/tree/v1.9.0"}, "time": "2021-05-06T09:03:35+00:00"}, {"name": "grasmash/expander", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/grasmash/expander.git", "reference": "95d6037344a4be1dd5f8e0b0b2571a28c397578f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/expander/zipball/95d6037344a4be1dd5f8e0b0b2571a28c397578f", "reference": "95d6037344a4be1dd5f8e0b0b2571a28c397578f", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "php": ">=5.4"}, "require-dev": {"greg-1-anderson/composer-test-scenarios": "^1", "phpunit/phpunit": "^4|^5.5.4", "satooshi/php-coveralls": "^1.0.2|dev-master", "squizlabs/php_codesniffer": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\Expander\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Expands internal property references in PHP arrays file.", "support": {"issues": "https://github.com/grasmash/expander/issues", "source": "https://github.com/grasmash/expander/tree/master"}, "time": "2017-12-21T22:14:55+00:00"}, {"name": "grasmash/yaml-expander", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/grasmash/yaml-expander.git", "reference": "3f0f6001ae707a24f4d9733958d77d92bf9693b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grasmash/yaml-expander/zipball/3f0f6001ae707a24f4d9733958d77d92bf9693b1", "reference": "3f0f6001ae707a24f4d9733958d77d92bf9693b1", "shasum": ""}, "require": {"dflydev/dot-access-data": "^1.1.0", "php": ">=5.4", "symfony/yaml": "^2.8.11|^3|^4"}, "require-dev": {"greg-1-anderson/composer-test-scenarios": "^1", "phpunit/phpunit": "^4.8|^5.5.4", "satooshi/php-coveralls": "^1.0.2|dev-master", "squizlabs/php_codesniffer": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Grasmash\\YamlExpander\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Expands internal property references in a yaml file.", "support": {"issues": "https://github.com/grasmash/yaml-expander/issues", "source": "https://github.com/grasmash/yaml-expander/tree/master"}, "time": "2017-12-16T16:06:03+00:00"}, {"name": "league/container", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/thephpleague/container.git", "reference": "84ecbc2dbecc31bd23faf759a0e329ee49abddbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/container/zipball/84ecbc2dbecc31bd23faf759a0e329ee49abddbd", "reference": "84ecbc2dbecc31bd23faf759a0e329ee49abddbd", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/container": "^1.0.0"}, "provide": {"psr/container-implementation": "^1.0"}, "replace": {"orno/di": "~2.0"}, "require-dev": {"phpunit/phpunit": "^6.0 || ^7.0", "roave/security-advisories": "dev-latest", "scrutinizer/ocular": "^1.8", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev", "dev-3.x": "3.x-dev", "dev-2.x": "2.x-dev", "dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"League\\Container\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.philipobenito.com", "role": "Developer"}], "description": "A fast and intuitive dependency injection container.", "homepage": "https://github.com/thephpleague/container", "keywords": ["container", "dependency", "di", "injection", "league", "provider", "service"], "support": {"issues": "https://github.com/thephpleague/container/issues", "source": "https://github.com/thephpleague/container/tree/3.4.1"}, "funding": [{"url": "https://github.com/philipobenito", "type": "github"}], "time": "2021-07-09T08:23:52+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {"drupal/better_exposed_filters": 10, "drupal/cookie_samesite_support": 20, "drupal/flag": 10, "drupal/gin": 15, "drupal/inline_entity_form": 5, "drupal/media_bulk_upload": 15, "drupal/media_library_bulk_upload": 15, "drupal/migrate_file": 20, "drupal/private_file_token": 15, "drupal/s3fs": 10, "drupal/term_merge": 20, "drupal/typed_data": 15}, "prefer-stable": true, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}