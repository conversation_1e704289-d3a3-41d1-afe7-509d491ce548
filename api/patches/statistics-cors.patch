diff --git a/core/modules/statistics/statistics.php b/core/modules/statistics/statistics.php
index 8090e02..c211942 100644
--- a/core/modules/statistics/statistics.php
+++ b/core/modules/statistics/statistics.php
@@ -16,6 +16,34 @@
 $kernel->boot();
 $container = $kernel->getContainer();
 
+// Allow from any origin
+if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'], [
+  'https://graphql-v3jryei-lxdk5jrhb4wc6.de-2.platformsh.site',
+  'http://localhost:4200',
+  'https://localhost:4200',
+  'http://playboy-premium.docksal',
+  'https://premium.playboy.de'
+])) {
+  // Decide if the origin in $_SERVER['HTTP_ORIGIN'] is one
+  // you want to allow, and if so:
+  header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
+  header('Access-Control-Allow-Credentials: true');
+  header('Access-Control-Max-Age: 86400');    // cache for 1 day
+}
+
+// Access-Control headers are received during OPTIONS requests
+if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
+
+  if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
+    // may also be using PUT, PATCH, HEAD etc
+    header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
+
+  if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
+    header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
+
+  exit(0);
+}
+
 $entity_types = $container
   ->get('config.factory')
   ->get('statistics.settings')
