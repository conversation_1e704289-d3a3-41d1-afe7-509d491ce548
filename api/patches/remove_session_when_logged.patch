diff --git a/src/OpenIDConnect.php b/src/OpenIDConnect.php
index ca0911b3..710da7d5 100644
--- a/src/OpenIDConnect.php
+++ b/src/OpenIDConnect.php
@@ -300,7 +300,8 @@ private function buildContext(OpenIDConnectClientInterface $client, array $token
    */
   public function completeAuthorization(OpenIDConnectClientInterface $client, array $tokens, &$destination) {
     if ($this->currentUser->isAuthenticated()) {
-      throw new \RuntimeException('User already logged in');
+      $session_manager = \Drupal::service('session_manager');
+      $session_manager->delete($this->currentUser->id());
     }
 
     $context = $this->buildContext($client, $tokens);
