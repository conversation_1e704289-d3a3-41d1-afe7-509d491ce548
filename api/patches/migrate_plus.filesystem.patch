diff --git a/src/Plugin/migrate/source/Url.php b/src/Plugin/migrate/source/Url.php
index d924177..4060ca8 100644
--- a/src/Plugin/migrate/source/Url.php
+++ b/src/Plugin/migrate/source/Url.php
@@ -2,7 +2,7 @@
 
 namespace Drupal\migrate_plus\Plugin\migrate\source;
 
-use Drupal\Core\File\FileSystem;
+use Drupal\Core\File\FileSystemInterface;
 use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
 use Drupal\migrate\Plugin\MigrationInterface;
 use Symfony\Component\DependencyInjection\ContainerInterface;
@@ -43,7 +43,7 @@ class Url extends SourcePluginExtension implements ContainerFactoryPluginInterfa
   /**
    * {@inheritdoc}
    */
-  public function __construct(array $configuration, $plugin_id, $plugin_definition, MigrationInterface $migration, FileSystem $file_system) {
+  public function __construct(array $configuration, $plugin_id, $plugin_definition, MigrationInterface $migration, FileSystemInterface $file_system) {
     if (!is_array($configuration['urls'])) {
       $configuration['urls'] = [$configuration['urls']];
     }
