{"version": "0.2.0", "configurations": [{"name": "Launch Chrome", "request": "launch", "type": "pwa-chrome", "preLaunchTask": "npm: start", "url": "http://localhost:4200", "webRoot": "${workspaceFolder}/app"}, {"name": "Listen for XDebug", "type": "php", "request": "launch", "port": 9000, "pathMappings": {"/var/www/": "${workspaceFolder}/api", "/usr/local/bin/drush": "${workspaceFolder}/api/vendor/bin/drush"}}, {"name": "Launch currently open script", "type": "php", "request": "launch", "program": "${file}", "cwd": "${fileDirname}", "port": 9000, "pathMappings": {"/var/www/": "${workspaceFolder}/api", "/usr/local/bin/drush": "${workspaceFolder}/api/vendor/bin/drush"}}]}