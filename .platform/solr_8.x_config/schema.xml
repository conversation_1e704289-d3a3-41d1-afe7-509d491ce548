<?xml version="1.0" encoding="UTF-8" ?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<!DOCTYPE schema [
  <!ENTITY extrafields SYSTEM "schema_extra_fields.xml">
  <!ENTITY extratypes SYSTEM "schema_extra_types.xml">
]>

<!--
 This is the Solr schema file. This file should be named "schema.xml" and
 should be in the conf directory under the solr home
 (i.e. ./solr/conf/schema.xml by default)
 or located where the classloader for the Solr webapp can find it.

 This example schema is the recommended starting point for users.
 It should be kept correct and concise, usable out-of-the-box.

 For more information, on how to customize this file, please see
 http://wiki.apache.org/solr/SchemaXml

 PERFORMANCE NOTE: this schema includes many optional features and should not
 be used for benchmarking.  To improve performance one could
  - set stored="false" for all fields possible (esp large fields) when you
    only need to search on the field but don't need to return the original
    value.
  - set indexed="false" if you don't need to search on the field, but only
    return the field as a result of searching on other indexed fields.
  - remove all unneeded copyField statements
  - for best index size and searching performance, set "index" to false
    for all general text fields, use copyField to copy them to the
    catchall "text" field, and use that for searching.
  - For maximum indexing performance, use the ConcurrentUpdateSolrServer
    java client.
  - Remember to run the JVM in server mode, and use a higher logging level
    that avoids logging every request
-->

<schema name="drupal-4.2.1-solr-8.x-0" version="1.6">
  <!-- attribute "name" is the name of this schema and is only used for display purposes.
       version="x.y" is Solr's version number for the schema syntax and
       semantics.  It should not normally be changed by applications.

       1.0: multiValued attribute did not exist, all fields are multiValued
            by nature
       1.1: multiValued attribute introduced, false by default
       1.2: omitTermFreqAndPositions attribute introduced, true by default
            except for text fields.
       1.3: removed optional field compress feature
       1.4: autoGeneratePhraseQueries attribute introduced to drive QueryParser
            behavior when a single string produces multiple tokens.  Defaults
            to off for version >= 1.4
       1.5: omitNorms defaults to true for primitive field types
            (int, float, boolean, string...)
       1.6: useDocValuesAsStored defaults to true.
     -->


  <!-- Valid attributes for fields:
    name: mandatory - the name for the field
    type: mandatory - the name of a field type from the
      fieldTypes
    indexed: true if this field should be indexed (searchable or sortable)
    stored: true if this field should be retrievable
    docValues: true if this field should have doc values. Doc values are
      useful (required, if you are using *Point fields) for faceting,
      grouping, sorting and function queries. Doc values will make the index
      faster to load, more NRT-friendly and more memory-efficient.
      They however come with some limitations: they are currently only
      supported by StrField, UUIDField, all *PointFields, and depending
      on the field type, they might require the field to be single-valued,
      be required or have a default value (check the documentation
      of the field type you're interested in for more information)
    multiValued: true if this field may contain multiple values per document
    omitNorms: (expert) set to true to omit the norms associated with
      this field (this disables length normalization and index-time
      boosting for the field, and saves some memory).  Only full-text
      fields or fields that need an index-time boost need norms.
      Norms are omitted for primitive (non-analyzed) types by default.
    termVectors: [false] set to true to store the term vector for a
      given field.
      When using MoreLikeThis, fields used for similarity should be
      stored for best performance.
    termPositions: Store position information with the term vector.
      This will increase storage costs.
    termOffsets: Store offset information with the term vector. This
      will increase storage costs.
    termPayloads: Store payload information with the term vector. This
      will increase storage costs.
    required: The field is required.  It will throw an error if the
      value does not exist
    default: a value that should be used if no value is specified
      when adding a document.
  -->

  <!-- field names should consist of alphanumeric or underscore characters only and
     not start with a digit.  This is not currently strictly enforced,
     but other field names will not have first class support from all components
     and back compatibility is not guaranteed.  Names with both leading and
     trailing underscores (e.g. _version_) are reserved.
  -->

  <!-- If you remove this field, you must _also_ disable the update log in solrconfig.xml
     or Solr won't start. _version_ and update log are required for SolrCloud
  -->
  <!-- doc values are enabled by default for primitive types such as long so we don't index the version field  -->
  <field name="_version_" type="plong" indexed="false" stored="false"/>

  <!-- points to the root document of a block of nested documents. Required for nested
     document support, may be removed otherwise
  -->
  <field name="_root_" type="string" indexed="true" stored="false" docValues="false"/>

  <!-- Only remove the "id" field if you have a very good reason to. While not strictly
  required, it is highly recommended. A <uniqueKey> is present in almost all Solr
  installations. See the <uniqueKey> declaration below where <uniqueKey> is set to "id".
  -->
  <!-- The document id is usually derived from a site-specific key (hash) and the
    entity type and ID like:
    Search Api 7.x:
      The format used is $document->id = $index_id . '-' . $item_id
    Search Api 8.x:
      The format used is $document->id = $site_hash . '-' . $index_id . '-' . $item_id
    Apache Solr Search Integration 7.x:
      The format used is $document->id = $site_hash . '/' . $entity_type . '/' . $entity->id;
  -->
  <!-- The Highlighter Component requires the id field to be "stored" even if docValues are set. -->
  <field name="id" type="string" indexed="true" stored="true" required="true" multiValued="false" docValues="true"/>

  <!-- Search Api specific fields -->
  <!-- index_id is the machine name of the search index this entry belongs to. -->
  <field name="index_id" type="string" indexed="true" stored="false" multiValued="false" docValues="true"/>

  <!-- Here, default is used to create a "timestamp" field indicating
       when each document was indexed.-->
  <field name="timestamp" type="pdate" indexed="true" stored="false" default="NOW" multiValued="false" docValues="true"/>

  <field name="site" type="string" indexed="true" stored="false" multiValued="false" docValues="true"/>
  <field name="hash" type="string" indexed="true" stored="false" multiValued="false" docValues="true"/>

  <field name="boost_document" type="pfloat" indexed="true" stored="false" multiValued="false" docValues="true"/>
  <field name="boost_term" type="boost_term_payload" indexed="true" stored="false" multiValued="true"/>

  <!-- Currently the suggester context filter query (suggest.cfq) accesses the tags using the stored values, neither the indexed terms nor the docValues.
       Therefore the dynamicField sm_* isn't suitable at the moment -->
  <field name="sm_context_tags" type="string" indexed="true" stored="true" multiValued="true" docValues="false"/>

  <!-- Dynamic field definitions.  If a field name is not found, dynamicFields
       will be used if the name matches any of the patterns.
       RESTRICTION: the glob-like pattern in the name attribute must have
       a "*" only at the start or the end.
       EXAMPLE:  name="*_i" will match any field ending in _i (like myid_i, z_i)
       Longer patterns will be matched first.  if equal size patterns
       both match, the first appearing in the schema will be used.  -->

  <!-- For 2 and 3 letter prefix dynamic fields, the 1st letter indicates the data type and
       the last letter is 's' for single valued, 'm' for multi-valued -->

  <!-- We use plong for integer since 64 bit ints are now common in PHP. -->
  <dynamicField name="is_*"  type="plong"    indexed="true"  stored="false" multiValued="false" docValues="true" termVectors="true"/>
  <dynamicField name="im_*"  type="plong"    indexed="true"  stored="false" multiValued="true" docValues="true" termVectors="true"/>
  <!-- List of floats can be saved in a regular float field -->
  <dynamicField name="fs_*"  type="pfloat"   indexed="true"  stored="false" multiValued="false" docValues="true"/>
  <dynamicField name="fm_*"  type="pfloat"   indexed="true"  stored="false" multiValued="true" docValues="true"/>
  <!-- List of doubles can be saved in a regular double field -->
  <dynamicField name="ps_*"  type="pdouble"   indexed="true"  stored="false" multiValued="false" docValues="true"/>
  <dynamicField name="pm_*"  type="pdouble"   indexed="true"  stored="false" multiValued="true" docValues="true"/>
  <!-- List of booleans can be saved in a regular boolean field -->
  <dynamicField name="bm_*"  type="boolean" indexed="true"  stored="false" multiValued="true" docValues="true" termVectors="true"/>
  <dynamicField name="bs_*"  type="boolean" indexed="true"  stored="false" multiValued="false" docValues="true" termVectors="true"/>
  <!-- Regular text (without processing) can be stored in a string field-->
  <dynamicField name="ss_*"  type="string"  indexed="true"  stored="false" multiValued="false" docValues="true" termVectors="true"/>
  <!-- For field types using SORTED_SET, multiple identical entries are collapsed into a single value.
       Thus if I insert values 4, 5, 2, 4, 1, my return will be 1, 2, 4, 5 when enabling docValues.
       If you need to preserve the order and duplicate entries, consider to store the values as zm_* (twice). -->
  <dynamicField name="sm_*"  type="string"  indexed="true"  stored="false" multiValued="true" docValues="true" termVectors="true"/>
  <!-- Special-purpose text fields -->
  <dynamicField name="tws_*" type="text_ws" indexed="true" stored="true" multiValued="false"/>
  <dynamicField name="twm_*" type="text_ws" indexed="true" stored="true" multiValued="true"/>

  <!-- Trie fields are deprecated. Point fields solve all needs. But we keep the dedicated field names for backward compatibility. -->
  <dynamicField name="ds_*"  type="pdate"   indexed="true"  stored="false" multiValued="false" docValues="true"/>
  <dynamicField name="dm_*"  type="pdate"   indexed="true"  stored="false" multiValued="true" docValues="true"/>
  <!-- This field is used to store date ranges -->
  <dynamicField name="drs_*" type="date_range" indexed="true" stored="true" multiValued="false"/>
  <dynamicField name="drm_*" type="date_range" indexed="true" stored="true" multiValued="true"/>
  <dynamicField name="its_*" type="plong"   indexed="true"  stored="false" multiValued="false" docValues="true" termVectors="true"/>
  <dynamicField name="itm_*" type="plong"   indexed="true"  stored="false" multiValued="true" docValues="true" termVectors="true"/>
  <dynamicField name="fts_*" type="pfloat"  indexed="true"  stored="false" multiValued="false" docValues="true"/>
  <dynamicField name="ftm_*" type="pfloat"  indexed="true"  stored="false" multiValued="true" docValues="true"/>
  <dynamicField name="pts_*" type="pdouble" indexed="true"  stored="false" multiValued="false" docValues="true"/>
  <dynamicField name="ptm_*" type="pdouble" indexed="true"  stored="false" multiValued="true" docValues="true"/>
  <!-- Binary fields can be populated using base64 encoded data. Useful e.g. for embedding
       a small image in a search result using the data URI scheme -->
  <dynamicField name="xs_*"  type="binary"  indexed="false" stored="true" multiValued="false"/>
  <dynamicField name="xm_*"  type="binary"  indexed="false" stored="true" multiValued="true"/>
  <!-- Trie fields are deprecated. Point fields solve all needs. But we keep the dedicated field names for backward compatibility. -->
  <dynamicField name="dds_*" type="pdate"    indexed="true"  stored="false" multiValued="false" docValues="true"/>
  <dynamicField name="ddm_*" type="pdate"    indexed="true"  stored="false" multiValued="true" docValues="true"/>
  <!-- In case a 32 bit int is really needed, we provide these fields. 'h' is mnemonic for 'half word', i.e. 32 bit on 64 arch -->
  <dynamicField name="hs_*" type="pint" indexed="true"  stored="false" multiValued="false" docValues="true"/>
  <dynamicField name="hm_*" type="pint" indexed="true"  stored="false" multiValued="true" docValues="true"/>
  <!-- Trie fields are deprecated. Point fields solve all needs. But we keep the dedicated field names for backward compatibility. -->
  <dynamicField name="hts_*" type="pint"   indexed="true"  stored="false" multiValued="false" docValues="true"/>
  <dynamicField name="htm_*" type="pint"   indexed="true"  stored="false" multiValued="true" docValues="true"/>

  <!-- Unindexed string fields that can be used to store values that won't be searchable -->
  <dynamicField name="zs_*" type="string"   indexed="false"  stored="true" multiValued="false"/>
  <dynamicField name="zm_*" type="string"   indexed="false"  stored="true" multiValued="true"/>

  <!-- Fields for location searches.
       http://wiki.apache.org/solr/SpatialSearch#geodist_-_The_distance_function -->
  <dynamicField name="points_*" type="point" indexed="true"  stored="true" multiValued="false"/>
  <dynamicField name="pointm_*" type="point" indexed="true"  stored="true" multiValued="true"/>
  <dynamicField name="locs_*" type="location" indexed="true"  stored="true" multiValued="false"/>
  <dynamicField name="locm_*" type="location" indexed="true"  stored="true" multiValued="true"/>
  <!-- GeoHash fields are deprecated. LatLonPointSpatial fields solve all needs. But we keep the dedicated field names for backward compatibility. -->
  <dynamicField name="geos_*" type="location" indexed="true"  stored="true" multiValued="false"/>
  <dynamicField name="geom_*" type="location" indexed="true"  stored="true" multiValued="true"/>
  <dynamicField name="bboxs_*" type="bbox" indexed="true" stored="true" multiValued="false" />
  <dynamicField name="bboxm_*" type="bbox" indexed="true" stored="true" multiValued="true" />
  <dynamicField name="rpts_*" type="location_rpt" indexed="true" stored="true" multiValued="false" />
  <dynamicField name="rptm_*" type="location_rpt" indexed="true" stored="true" multiValued="true" />

  <!-- External file fields -->
  <dynamicField name="eff_*" type="file"/>

  <!-- A random sort field -->
  <dynamicField name="random_*" type="random" indexed="true" stored="true"/>

  <!-- This field is used to store access information (e.g. node access grants), as opposed to field data -->
  <dynamicField name="access_*" type="pint" indexed="true" stored="false" multiValued="true" docValues="true"/>

  <!-- The following causes solr to ignore any fields that don't already match an existing
       field name or dynamic field, rather than reporting them as an error.
       Alternately, change the type="ignored" to some other type e.g. "text" if you want
       unknown fields indexed and/or stored by default -->
  <dynamicField name="*" type="ignored" multiValued="true" />


    <!-- field type definitions. The "name" attribute is
       just a label to be used by field definitions.  The "class"
       attribute and any other attributes determine the real
       behavior of the fieldType.
         Class names starting with "solr" refer to java classes in a
       standard package such as org.apache.solr.analysis
    -->

    <!-- The StrField type is not analyzed, but indexed/stored verbatim.
       It supports doc values but in that case the field needs to be
       single-valued and either required or have a default value.
      -->
    <fieldType name="string" class="solr.StrField"/>

    <!-- boolean type: "true" or "false" -->
    <fieldType name="boolean" class="solr.BoolField"/>

    <!-- sortMissingLast and sortMissingFirst attributes are optional attributes are
         currently supported on types that are sorted internally as strings
         and on numeric types.
         This includes "string", "boolean", "pint", "pfloat", "plong", "pdate", "pdouble".
       - If sortMissingLast="true", then a sort on this field will cause documents
         without the field to come after documents with the field,
         regardless of the requested sort order (asc or desc).
       - If sortMissingFirst="true", then a sort on this field will cause documents
         without the field to come before documents with the field,
         regardless of the requested sort order.
       - If sortMissingLast="false" and sortMissingFirst="false" (the default),
         then default lucene sorting will be used which places docs without the
         field first in an ascending sort and last in a descending sort.
    -->

    <!--
      Numeric field types that index values using KD-trees.
      Point fields don't support FieldCache, so they must have docValues="true" if needed for sorting, faceting, functions, etc.
    -->
    <fieldType name="pint" class="solr.IntPointField" docValues="true"/>
    <fieldType name="pfloat" class="solr.FloatPointField" docValues="true"/>
    <fieldType name="plong" class="solr.LongPointField" docValues="true"/>
    <fieldType name="pdouble" class="solr.DoublePointField" docValues="true"/>

    <fieldType name="pints" class="solr.IntPointField" docValues="true" multiValued="true"/>
    <fieldType name="pfloats" class="solr.FloatPointField" docValues="true" multiValued="true"/>
    <fieldType name="plongs" class="solr.LongPointField" docValues="true" multiValued="true"/>
    <fieldType name="pdoubles" class="solr.DoublePointField" docValues="true" multiValued="true"/>

    <!--
     The ExternalFileField type gets values from an external file instead of the
     index. This is useful for data such as rankings that might change frequently
     and require different update frequencies than the documents they are
     associated with.
    -->
    <fieldType name="file" keyField="id" defVal="1" stored="false" indexed="false" class="solr.ExternalFileField"/>

    <!-- The format for this date field is of the form 1995-12-31T23:59:59Z, and
         is a more restricted form of the canonical representation of dateTime
         http://www.w3.org/TR/xmlschema-2/#dateTime
         The trailing "Z" designates UTC time and is mandatory.
         Optional fractional seconds are allowed: 1995-12-31T23:59:59.999Z
         All other components are mandatory.

         Expressions can also be used to denote calculations that should be
         performed relative to "NOW" to determine the value, ie...

               NOW/HOUR
                  ... Round to the start of the current hour
               NOW-1DAY
                  ... Exactly 1 day prior to now
               NOW/DAY+6MONTHS+3DAYS
                  ... 6 months and 3 days in the future from the start of
                      the current day

         Consult the DatePointField javadocs for more information.
      -->
    <!-- KD-tree versions of date fields -->
    <fieldType name="pdate" class="solr.DatePointField" docValues="true"/>
    <fieldType name="pdates" class="solr.DatePointField" docValues="true" multiValued="true"/>

    <!-- A date range field -->
    <fieldType name="date_range" class="solr.DateRangeField"/>

    <!--Binary data type. The data should be sent/retrieved in as Base64 encoded Strings -->
    <fieldType name="binary" class="solr.BinaryField"/>

    <!-- The "RandomSortField" is not used to store or search any
         data.  You can declare fields of this type it in your schema
         to generate pseudo-random orderings of your docs for sorting
         or function purposes.  The ordering is generated based on the field
         name and the version of the index. As long as the index version
         remains unchanged, and the same field name is reused,
         the ordering of the docs will be consistent.
         If you want different psuedo-random orderings of documents,
         for the same version of the index, use a dynamicField and
         change the field name in the request.
     -->
    <fieldType name="random" class="solr.RandomSortField" indexed="true" />

    <!-- solr.TextField allows the specification of custom text analyzers
         specified as a tokenizer and a list of token filters. Different
         analyzers may be specified for indexing and querying.

         The optional positionIncrementGap puts space between multiple fields of
         this type on the same document, with the purpose of preventing false phrase
         matching across fields.

         For more info on customizing your analyzer chain, please see
         http://wiki.apache.org/solr/AnalyzersTokenizersTokenFilters
     -->

    <!-- One can also specify an existing Analyzer class that has a
         default constructor via the class attribute on the analyzer element.
         Example:
    <fieldType name="text_greek" class="solr.TextField">
      <analyzer class="org.apache.lucene.analysis.el.GreekAnalyzer"/>
    </fieldType>
    -->

    <!-- A text field that only splits on whitespace for exact matching of words -->
    <fieldType name="text_ws" class="solr.TextField" omitNorms="true" positionIncrementGap="100">
      <analyzer>
        <tokenizer class="solr.WhitespaceTokenizerFactory"/>
        <filter class="solr.LowerCaseFilterFactory"/>
      </analyzer>
    </fieldType>

    <fieldType name="boost_term_payload" stored="false" indexed="true" class="solr.TextField" >
      <analyzer>
        <tokenizer class="solr.WhitespaceTokenizerFactory"/>
        <filter class="solr.LengthFilterFactory" min="2" max="100"/>
        <filter class="solr.LowerCaseFilterFactory"/>
        <filter class="solr.RemoveDuplicatesTokenFilterFactory"/>
        <!--
        The DelimitedPayloadTokenFilter can put payloads on tokens... for example,
        a token of "foo|1.4"  would be indexed as "foo" with a payload of 1.4f
        Attributes of the DelimitedPayloadTokenFilterFactory :
         "delimiter" - a one character delimiter. Default is | (pipe)
         "encoder" - how to encode the following value into a playload
           float -> org.apache.lucene.analysis.payloads.FloatEncoder,
           integer -> o.a.l.a.p.IntegerEncoder
           identity -> o.a.l.a.p.IdentityEncoder
             Fully Qualified class name implementing PayloadEncoder, Encoder must have a no arg constructor.
         -->
        <filter class="solr.DelimitedPayloadTokenFilterFactory" encoder="float"/>
      </analyzer>
    </fieldType>

    <!-- since fields of this type are by default not stored or indexed,
         any data added to them will be ignored outright.  -->
    <fieldType name="ignored" stored="false" indexed="false" multiValued="true" class="solr.StrField" />

    <!-- This point type indexes the coordinates as separate fields (subFields)
      If subFieldType is defined, it references a type, and a dynamic field
      definition is created matching *___<typename>.  Alternately, if
      subFieldSuffix is defined, that is used to create the subFields.
      Example: if subFieldType="double", then the coordinates would be
        indexed in fields myloc_0___double,myloc_1___double.
      Example: if subFieldSuffix="_d" then the coordinates would be indexed
        in fields myloc_0_d,myloc_1_d
      The subFields are an implementation detail of the fieldType, and end
      users normally should not need to know about them.
     -->
    <!-- In Drupal we only use prefixes for dynmaic fields. Tht might change in
      the future but for now we keep this pattern.
    -->
    <fieldType name="point" class="solr.PointType" dimension="2" subFieldType="pdouble"/>

    <!-- A specialized field for geospatial search. If indexed, this fieldType must not be multivalued. -->
    <fieldType name="location" class="solr.LatLonPointSpatialField" docValues="true"/>

    <!-- An alternative geospatial field type new to Solr 4.  It supports multiValued and polygon shapes.
      For more information about this and other Spatial fields new to Solr 4, see:
      http://wiki.apache.org/solr/SolrAdaptersForLuceneSpatial4
    -->
    <fieldType name="location_rpt" class="solr.SpatialRecursivePrefixTreeFieldType"
        geo="true" distErrPct="0.025" maxDistErr="0.001" distanceUnits="kilometers" />

    <!-- Spatial rectangle (bounding box) field. It supports most spatial predicates, and has
    special relevancy modes: score=overlapRatio|area|area2D (local-param to the query).  DocValues is recommended for
    relevancy. -->
    <fieldType name="bbox" class="solr.BBoxField"
               geo="true" distanceUnits="kilometers" numberType="_bbox_coord" />
    <fieldType name="_bbox_coord" class="solr.DoublePointField" docValues="true" stored="false"/>

  <!-- Money/currency field type. See http://wiki.apache.org/solr/MoneyFieldType
       Parameters:
           amountLongSuffix: Required. Refers to a dynamic field for the raw amount sub-field.
                               The dynamic field must have a field type that extends LongValueFieldType.
                               Note: If you expect to use Atomic Updates, this dynamic field may not be stored.
           codeStrSuffix:    Required. Refers to a dynamic field for the currency code sub-field.
                               The dynamic field must have a field type that extends StrField.
                               Note: If you expect to use Atomic Updates, this dynamic field may not be stored.
           defaultCurrency:  Specifies the default currency if none specified. Defaults to "USD"
           providerClass:    Lets you plug in other exchange provider backend:
                             solr.FileExchangeRateProvider is the default and takes one parameter:
                               currencyConfig: name of an xml file holding exchange rates
                             solr.OpenExchangeRatesOrgProvider uses rates from openexchangerates.org:
                               ratesFileLocation: URL or path to rates JSON file (default latest.json on the web)
                               refreshInterval: Number of minutes between each rates fetch (default: 1440, min: 60)
  -->
<!--  <fieldType name="currency" class="solr.CurrencyFieldType" amountLongSuffix="_l_ns" codeStrSuffix="_s_ns"
                 defaultCurrency="USD" currencyConfig="currency.xml" /> -->

  <!-- Following is a dynamic way to include other fields, added by other contrib modules -->
  &extrafields;

  <!-- Following is a dynamic way to include other types, added by other contrib modules -->
  &extratypes;

  <!-- Field to use to determine and enforce document uniqueness.
       Unless this field is marked with required="false", it will be a required field
    -->
  <uniqueKey>id</uniqueKey>

  <!-- Similarity is the scoring routine for each document vs. a query.
       A custom Similarity or SimilarityFactory may be specified here, but
       the default is fine for most applications.
       For more info: http://wiki.apache.org/solr/SchemaXml#Similarity
    -->
  <!--
     <similarity class="com.example.solr.CustomSimilarityFactory">
       <str name="paramkey">param value</str>
     </similarity>
    -->

</schema>
