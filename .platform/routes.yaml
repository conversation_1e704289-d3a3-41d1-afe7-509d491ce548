# The routes of the project.
#
# Each route describes how an incoming URL is going
# to be processed by Platform.sh.

'https://{default}/':
  type: upstream
  upstream: 'app:http'
  cache:
    enabled: false
  redirects:
    paths:
      '/member':
        to: 'https://{default}/'
      '/user/profile':
        to: 'https://{default}/profile'
      '/profile':
        to: 'https://login.playboy.de/service'

'https://api.{default}/':
  type: upstream
  upstream: 'api:http'
  redirects:
    paths:
      '/frontend':
        to: 'https://{default}/'
      '/profile':
        to: 'https://{default}/profile'
      '/girl':
        to: 'https://{default}/girl'
      '/p':
        to: 'https://{default}/p'
      '/favorites':
        to: 'https://{default}/favorites'
      '/categories':
        to: 'https://{default}/categories'
      '/user/login':
        to: 'https://login.playboy.de'
  cache:
    enabled: true
    default_ttl: 60

    # Base the cache on the session cookie and custom Drupal cookies. Ignore all other cookies.
    cookies: ['/^SS?ESS/']
