# This file describes an application. You can have multiple applications
# in the same project.
#
# See https://docs.platform.sh/user_guide/reference/platform-app-yaml.html

# The name of this app. Must be unique within a project.
name: app

# The runtime the application uses.
type: nodejs:22

# Set the timezone to Europe/Paris
timezone: "Europe/Paris"

# The size of the persistent disk of the application (in MB).
disk: 5000

resources:
  base_memory: 128
  memory_ratio: 180

# The hooks executed at various points in the lifecycle of the application.
hooks:
  build: npm run build:prod
web:
  commands:
    start: node dist/premium/server/server.mjs
