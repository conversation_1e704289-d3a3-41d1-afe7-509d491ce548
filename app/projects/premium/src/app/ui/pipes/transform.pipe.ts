import { Pipe, PipeTransform } from '@angular/core';

export interface ITransform {
  x?: number;
  y?: number;
  z: number;
}

@Pipe({
  name: 'transform',
  standalone: true,
})
export class TransformPipe implements PipeTransform {
  translate(x?: number, y?: number, z?: number): string {
    if (x && y) {
      return `translate(${x.toFixed(4)}px, ${y.toFixed(4)}px) scale(${z})`;
    }
    if (x) {
      return `translateX(${x.toFixed(4)}px) scale(${z})`;
    }
    if (y) {
      return `translateY(${y.toFixed(4)}px) scale(${z})`;
    }
    return `scale(${z})`;
  }

  transform(transform?: ITransform): string {
    if (!transform) {
      return undefined;
    }
    return this.translate(transform.x, transform.y, transform.z);
    const translate = this.translate(transform.x, transform.y);
    return `translate3D(${transform.x}px, ${transform.z}px, 0px) ${
      translate || ''
    } scale(${transform.z.toFixed(4)})`;
  }
}
