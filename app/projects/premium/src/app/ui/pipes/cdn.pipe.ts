import { CdnService } from './../../services/cdn.service';
import { Pipe, PipeTransform } from '@angular/core';
import { environment } from 'projects/premium/src/environments/environment';

const SIZES = [300, 1200, 2000, 3000];

function NormalizeSize(size?: number): number {
  if (!size) {
    return undefined;
  }
  return SIZES.find((v) => v >= size) || SIZES[SIZES.length - 1];
}

export function GetCDNImage(
  url: string,
  imageWidth?: number,
  imageHeight?: number,
): string {
  if (!url) {
    return url;
  }

  const apiUrl = environment.serverUrl + '/system/files/';

  if (url.search(apiUrl) === -1) {
    return url;
  }

  const width = NormalizeSize(Math.max(imageHeight || 0, imageWidth || 0));

  let img = `${CdnService.url}${url.replace(apiUrl, '').split('?')[0]}`;
  if (width) {
    const index = img.indexOf('&token_path=');
    return img.substring(0, index) + `&width=${width}` + img.substring(index);
  }
  return img;
  // https://playboy-premium.b-cdn.net/bcdn_token=vEJkQGG7nhYepySXGzuxIbifJjWNLtE8QTiGi6wzz4E&expires=**********&width=300&token_path=%2F/current/2019/02/20/container-images-00000141232/c288c16d05634caa83951de4fdd7e7cc_1550680870.9237.jpeg
  // return `${img.split('?')[0]}?token=${token}&expires=${CdnService.expires}${!!width ? '&width=' + width : ''}`;
}

@Pipe({
  name: 'cdn',
  standalone: true,
})
export class CdnPipe implements PipeTransform {
  transform(url: string, imageWidth?: number, imageHeight?: number): string {
    return GetCDNImage(url, imageWidth, imageHeight);
  }
}
