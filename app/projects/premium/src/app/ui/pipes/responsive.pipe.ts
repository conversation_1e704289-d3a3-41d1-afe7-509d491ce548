import { distinctJSON } from '../utils';
import { Pipe, PipeTransform } from '@angular/core';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';

import {
  NameScreenMapping,
  ScreenName,
  ScreenObservable,
} from '../utils/screen';

@Pipe({
  name: 'responsive',
  standalone: true,
})
export class ResponsivePipe implements PipeTransform {
  transform(screenName: ScreenName): Observable<boolean> {
    const screen = NameScreenMapping[screenName];
    return ScreenObservable.pipe(
      map((v) => v > screen),
      distinctJSON(),
    );
  }
}
