import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'month',
  standalone: true,
})
export class MonthPipe implements PipeTransform {
  transform(value: number | undefined): string | undefined {
    if (!value) {
      return '';
    }
    return [
      '',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      'April',
      '<PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      'August',
      'September',
      'Ok<PERSON><PERSON>',
      'November',
      'Dezember',
    ][value];
  }
}
