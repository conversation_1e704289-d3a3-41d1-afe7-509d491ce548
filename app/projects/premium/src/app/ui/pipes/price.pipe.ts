import { Pipe, PipeTransform } from '@angular/core';

import { Currency, RenderCurrency } from './currency-symbol.pipe';

export interface IPrice {
  value: string | number;
  currency: Currency;
}

export function RenderPrice({ value, currency }: IPrice): string {
  return `${(
    (typeof value === 'string' ? parseFloat(value) : value) || 0
  ).toFixed(2)} ${RenderCurrency(currency)}`;
}

@Pipe({
  name: 'price',
  standalone: true,
})
export class PricePipe implements PipeTransform {
  transform(price: IPrice): unknown {
    return RenderPrice(price);
  }
}
