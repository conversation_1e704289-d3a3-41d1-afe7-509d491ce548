import { Pipe, PipeTransform } from '@angular/core';

export enum Currency {
  Euro = 'EUR',
}

export function RenderCurrency(value: Currency): string {
  switch (value) {
    case Currency.Euro:
      return '€';
    default:
      return value;
  }
}

@Pipe({
  name: 'currencySymbol',
  standalone: true,
})
export class CurrencySymbolPipe implements PipeTransform {
  transform(value: Currency): string {
    return RenderCurrency(value);
  }
}
