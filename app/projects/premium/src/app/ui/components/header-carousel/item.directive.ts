import { Directive, Input, TemplateRef } from '@angular/core';
import { IPreview } from '../../../models/preview';

@Directive({
  selector: '[libHeaderCarouselItem]',
  standalone: true,
})
export class HeaderCarouselItemDirective {
  @Input() image: string;
  @Input() lowResImage?: string;
  @Input() focalPoint?: {
    x: number;
    y: number;
  };
  @Input() nexxID?: string;
  @Input() title: string;
  @Input() text?: string;
  @Input() link?: string | number | (string | number)[];
  @Input() linkText?: string;
  @Input() isNew?: boolean;

  @Input() set previewData(data: IPreview) {
    this.image = data.image;
    this.lowResImage = data.imageLowRes;
    this.focalPoint = data.focalPoint;
    this.nexxID = data.nexxID;
    this.title = data.title;
    this.text = data.text;
    this.link = data.link;
    this.isNew = data.isNew;
  }

  constructor(public templateRef: TemplateRef<any>) {}
}
