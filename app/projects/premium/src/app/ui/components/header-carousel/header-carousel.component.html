<ng-template
  #slideTemplate
  let-image="image"
  let-lowResImage="lowResImage"
  let-focalPoint="focalPoint"
  let-nexxID="nexxID"
>
  @if (nexxID) {
    <lib-preview-video
      class="absolute h-full w-screen"
      [poster]="image"
      [nexxID]="nexxID"
      [autoSize]="false"
    >
    </lib-preview-video>
  }
  @if (!nexxID) {
    <app-preload-image
      class="absolute h-full w-screen"
      [fill]="true"
      [src]="image"
      [focalPoint]="focalPoint"
    >
    </app-preload-image>
  }
</ng-template>

@if (slides?.length > 1 && !hideSwiper) {
  <swiper-container
    #swiperContainer
    class="absolute left-0 w-screen h-full top-0 z-0"
    [attr.loop]="true"
    slides-per-view="1"
    space-between="0"
    autoplay-delay="5000"
    autoplay-disable-on-interaction="false"
    init="false"
    (swiperactiveindexchange)="
      swiperContainer?.swiper ? ch(swiperContainer.swiper.realIndex) : null
    "
    initialslide="0"
  >
    @for (slide of slides; track slide) {
      <swiper-slide>
        <a [routerLink]="slide.link" class="flex w-screen h-full">
          <ng-container *ngTemplateOutlet="slideTemplate; context: slide">
          </ng-container>
        </a>
      </swiper-slide>
    }
  </swiper-container>
} @else {
  <div class="absolute inset-0 z-0">
    <ng-container *ngTemplateOutlet="slideTemplate; context: slides.get(0)">
    </ng-container>
  </div>
}

@if (slides.get(currentSlide); as slide) {
  @if (slide.title || slide.linkText) {
    <div
      class="absolute flex bottom-0 z-10 inset-x-0 flex-row justify-center sm:justify-start cursor-pointer"
    >
      <div
        class="hidden w-1/12"
        [ngClass]="{ 'lg:flex': isWide, 'md:flex': !isWide }"
      ></div>
      <div
        [ngClass]="{ 'w-full': isWide, 'w-4/5 md:w-1/2': !isWide }"
        class="backdrop-filter backdrop-blur-xl bg-black bg-opacity-20 flex flex-col p-4 sm:px-5 sm:py-8 overflow-hidden group lg:w-1/3"
      >
        <ng-container>
          <hr class="w-15 mb-2" />
          <h4 class="mb-1.5 sm:mb-3">{{ slide.title }}</h4>
          <h2 class="mb-2 sm:mb-7">
            {{ slide.text }}
            <ng-template *ngTemplateOutlet="slide.templateRef"></ng-template>
          </h2>
          @if (slide.link && slide.linkText) {
            <a
              class="flex-grow-0 mt-1 sm:mt-4 inline-flex"
              [routerLink]="slide.link"
            >
              <lib-button [outline]="true" [class.mb-7]="slides?.length > 1">
                {{ slide.linkText }}
              </lib-button>
            </a>
          }
        </ng-container>
        @if (swiperContainer?.nativeElement.swiper && slides?.length > 1) {
          <lib-pagination
            [amount]="slides?.length"
            [selected]="currentSlide"
            class="h-px md:h-2"
            (selectIndex)="
              swiperContainer?.nativeElement.swiper.slideTo($event)
            "
          ></lib-pagination>
        }
      </div>
    </div>
  }
}
