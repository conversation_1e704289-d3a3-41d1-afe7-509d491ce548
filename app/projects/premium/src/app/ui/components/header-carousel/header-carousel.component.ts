import {
  Component,
  ContentChildren,
  ElementRef,
  inject,
  Input,
  NgZone,
  OnInit,
  PLATFORM_ID,
  QueryList,
  ViewChild,
} from '@angular/core';

import { isPlatformBrowser, Ng<PERSON>lass, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ButtonComponent } from '@pb/ui/components/button/button.component';
import { PaginationComponent } from '@pb/ui/components/pagination/pagination.component';
import { PreloadImageComponent } from '@pb/ui/components/preload-image/preload-image.component';
import { PreviewVideoComponent } from '@pb/ui/components/preview-video/preview-video.component';
import { SwiperContainer } from 'swiper/swiper-element';
import { HeaderCarouselItemDirective } from './item.directive';

@Component({
  selector: 'lib-header-carousel',
  templateUrl: './header-carousel.component.html',
  styleUrls: ['./header-carousel.component.css'],
  imports: [
    PreviewVideoComponent,
    PreloadImageComponent,
    NgClass,
    ButtonComponent,
    RouterLink,
    PaginationComponent,
    NgTemplateOutlet,
  ],
})
export class HeaderCarouselComponent implements OnInit {
  readonly #platformId: Object = inject(PLATFORM_ID);
  @ContentChildren(HeaderCarouselItemDirective)
  slides: QueryList<HeaderCarouselItemDirective>;

  @ViewChild('swiperContainer') swiperContainer: ElementRef<SwiperContainer>;

  // helper flag for re-initializing swiper after the slides have been changed
  hideSwiper = false;

  currentSlide = 0;

  @Input() isWide = false;

  constructor(private zone: NgZone) {}

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    if (isPlatformBrowser(this.#platformId)) {
      this.initSwiper();
      this.slides.changes.subscribe(() => {
        // watch for slides change to react and re-init swiper
        this.hideSwiper = true;
        setTimeout(() => {
          // force next lifecycle
          this.hideSwiper = false;
          if (this.slides?.toArray().length > 0) {
            setTimeout(() => {
              // force next lifecycle
              this.initSwiper();
            });
          }
        });
      });
    }
  }
  initSwiper(): void {
    // initialize swiper only if swipercontainer is available
    this.swiperContainer?.nativeElement?.initialize();
  }

  ch(index: number) {
    if (isPlatformBrowser(this.#platformId)) {
      this.zone.run(() => {
        this.currentSlide = index;
      });
    }
  }
}

export { HeaderCarouselItemDirective };
