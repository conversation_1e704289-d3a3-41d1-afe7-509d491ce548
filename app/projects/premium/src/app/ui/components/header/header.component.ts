import { map, distinctUntilChanged } from 'rxjs/operators';
import { Component, Input, OnInit } from '@angular/core';
import { GetScreen, Screen, ScreenObservable } from '@pb/ui/utils/screen';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { NgClass } from '@angular/common';

@Component({
  selector: 'lib-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css'],
  imports: [RouterLink, RouterLinkActive, NgClass],
})
export class HeaderComponent implements OnInit {
  @Input() urls: {
    href?: string;
    routerLink?: string | string[];
    title: string;
  }[] = [];

  burgerOpen = false;
  isBurger = GetScreen() < Screen.xlm;

  constructor() {
    ScreenObservable.pipe(
      map((v) => v <= Screen.xlm),
      distinctUntilChanged((a, b) => a === b),
    ).subscribe((isBurger) => {
      this.burgerOpen = false;
      this.isBurger = isBurger;
    });
  }

  ngOnInit(): void {}
}
