<header>
  @if (!isBurger) {
    <ul>
      <li>
        <a [routerLink]="['/']" class="font-sans uppercase font-bold text-xs">
          <img
            src="assets/logo/pb_all_ac.png"
            srcset="assets/logo/<EMAIL> 2x, assets/logo/<EMAIL> 3x"
          />
        </a>
      </li>
      @for (url of urls; track url) {
        <li>
          @if (url.routerLink) {
            <a
              [routerLink]="url.routerLink"
              class="font-sans uppercase font-bold text-xs border-b border-transparent hover:border-white no-underline"
              routerLinkActive="border-white"
              >{{ url.title }}</a
            >
          }
          @if (url.href) {
            <a
              [href]="url.href"
              class="font-sans uppercase font-bold text-xs border-b border-transparent"
              >{{ url.title }}</a
            >
          }
        </li>
      }
    </ul>
    <div class="flex flex-1"></div>
    <ul>
      <li>
        <a
          target="blank"
          href="https://www.playboy.de/?utm_source=playboy&utm_medium=premium&utm_campaign=navigation"
          class="font-sans uppercase font-bold text-xs"
          >playboy.de</a
        >
      </li>
      <li>
        <a
          target="blank"
          href="https://premium.playboy.de/?utm_source=playboy&utm_medium=premium&utm_campaign=navigation"
          class="font-sans uppercase font-bold text-xs"
          >Premium</a
        >
      </li>
      <li>
        <a
          target="blank"
          href="https://shop.playboy.de/abo?utm_campaign=navigation&utm_medium=website&utm_source=playboy"
          class="font-sans uppercase font-bold text-xs"
          >Shop</a
        >
      </li>
      <!-- <li>
      <a target="blank"
        href="https://weinshop.playboy.de?utm_source=playboy&utm_medium=premium&utm_campaign=navigation"
      class="font-sans uppercase font-bold text-xs">Wein</a>
    </li> -->
    </ul>
  } @else {
    <a [routerLink]="['/']" class="font-sans uppercase font-bold text-xs">
      <img
        src="assets/logo/pb_all_ac.png"
        srcset="assets/logo/<EMAIL> 2x, assets/logo/<EMAIL> 3x"
      />
    </a>
    <div class="flex flex-1"></div>
  }

  <ng-content></ng-content>

  @if (isBurger) {
    <div
      class="flex flex-col w-8 ml-4 h-full py-2 justify-between"
      (click)="mobileOpen = !mobileOpen"
    >
      <div
        class="duration-100 origin-bottom-right transition w-full h-px border-b-2 border-white"
        [ngClass]="{ 'transform -rotate-45 translate-y-px': mobileOpen }"
      ></div>
      <div
        class="duration-100 transition-opacity w-full h-px border-b-2 border-white opacity-0"
        [class.opacity-100]="!mobileOpen"
      ></div>
      <div
        class="duration-100 origin-top-right transition w-full h-px border-b-2 border-white"
        [ngClass]="{ 'transform rotate-45 -translate-y-px': mobileOpen }"
      ></div>
    </div>
  }
</header>

@if (burgerOpen && isBurger) {
  <div
    class="mobile-menu fixed top-0 left-0 z-40 w-screen h-screen bg-black bg-opacity-84 text-white"
  >
    <div class="flex flex-col m-8 pl-8 border-l text-base border-white">
      @for (url of urls; track url; let last = $last; let first = $first) {
        @if (url.routerLink) {
          <a
            [routerLink]="url.routerLink"
            class="font-sans uppercase font-bold border-b border-transparent"
            [class.mt-4]="!first"
            [class.mb-4]="!last"
            routerLinkActive="underline"
            >{{ url.title }}</a
          >
        }
        @if (url.href) {
          <a
            [href]="url.href"
            class="font-sans uppercase font-bold border-b border-transparent my-4"
            >{{ url.title }}</a
          >
        }
      }
    </div>
    <div
      class="flex flex-col m-8 pl-8 border-l text-base border-golden text-golden"
    >
      <a href="//premium.playboy.de/" class="font-sans uppercase font-bold mb-4"
        >Premium</a
      >
      <a href="//shop.playboy.de" class="font-sans uppercase font-bold mt-4"
        >Abo</a
      >
    </div>
  </div>
}
