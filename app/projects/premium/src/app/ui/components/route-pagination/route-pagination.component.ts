import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { Component, OnInit, Input } from '@angular/core';
import { NgClickOutsideDirective } from 'ng-click-outside2';

import { PopupComponent } from '@pb/ui/components/route-pagination/popup/popup.component';

// TODO: This is definetly not the best way to do the dot ranges
function BuildPageDots(
  pages: number,
  current: number,
): (number | [number, number])[] {
  const maxCells = 10;
  if (pages < maxCells) {
    return new Array(pages).fill(0).map((_, i) => i);
  }
  const bufferForSignleDotRange = Math.floor((maxCells - 1) / 2);
  // Find out if current would be if one dot range is in the middle
  if (
    current < bufferForSignleDotRange ||
    current > pages - 1 - bufferForSignleDotRange
  ) {
    const singleDotRange: [number, number] = [
      bufferForSignleDotRange,
      pages - bufferForSignleDotRange - 1,
    ];
    return [
      ...new Array(singleDotRange[0]).fill(0).map((_, i) => i),
      singleDotRange,
      ...new Array(pages - singleDotRange[1] - 1)
        .fill(0)
        .map((_, i) => singleDotRange[1] + i + 1),
    ];
  }
  const requiredStart = Math.floor(current - bufferForSignleDotRange / 2);
  const requiredRange = [
    requiredStart,
    requiredStart + bufferForSignleDotRange,
  ];

  return [
    0,
    requiredRange[0] - 1 < 2 ? 1 : [1, requiredRange[0] - 1],
    ...new Array(requiredRange[1] - requiredRange[0] + 1)
      .fill(0)
      .map((_, i) => i + requiredRange[0]),
    requiredRange[1] > pages - 2
      ? pages - 2
      : [requiredRange[1] + 1, pages - 2],
    // requiredRange[1] > pages - 2 ? pages - 2 : new Array(pages - 2 - requiredRange[1]).fill(0).map((_, i) => i + requiredRange[1] + 1),
    pages - 1,
  ];
}

@Component({
  selector: 'app-route-pagination',
  templateUrl: './route-pagination.component.html',
  styleUrls: ['./route-pagination.component.css'],
  imports: [
    RouterLink,
    RouterLinkActive,
    NgClickOutsideDirective,
    PopupComponent,
  ],
})
export class RoutePaginationComponent implements OnInit {
  public dots: (number | number[])[] = [];
  public prev: number;
  public next: number;

  openIndex = -1;

  @Input() baseRoute?: string;
  @Input() type: 'route' | 'query' = 'route';

  private _screenAmount: number;
  @Input() set screenAmount(screens: number) {
    this._screenAmount = Math.ceil(screens);
    this.refresh();
  }

  private _screen: number;
  @Input() set screen(screen: number) {
    this._screen = screen;
    this.prev = Math.max(0, screen - 1);
    this.refresh();
  }

  Array = Array;

  @Input() animationElementSelector = '#title-proposal';

  refresh(): void {
    if (
      typeof this._screenAmount !== 'number' ||
      typeof this._screen !== 'number'
    ) {
      return;
    }
    this.dots = BuildPageDots(this._screenAmount, this._screen);
    this.next = Math.min(this._screenAmount - 1, this._screen + 1);
  }

  animate(): void {
    document?.querySelector(this.animationElementSelector)?.scrollIntoView();
  }

  ngOnInit(): void {
    // this.$paginationMeta = combineLatest([
    //   this.$page,
    //   this.$pageAmount,
    //   this.$type
    // ]).pipe(
    //   map(([currentPage, pages, type]) => ({
    //     prev: Math.max(0, currentPage - 1),
    //     next: Math.min(pages - 1, currentPage + 1),
    //     type,
    //     shown: BuildPageDots(pages, currentPage)
    //   })));
  }
}
