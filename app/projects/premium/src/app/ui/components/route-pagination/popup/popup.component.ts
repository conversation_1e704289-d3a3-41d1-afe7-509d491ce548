import { Component, Input } from '@angular/core';
import {
  UntypedFormGroup,
  UntypedFormControl,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { NgClass, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-route-pagination-popup',
  templateUrl: './popup.component.html',
  styleUrls: ['./popup.component.css'],
  imports: [ReactiveFormsModule, RouterLink, SlicePipe, NgClass],
})
export class PopupComponent {
  public min: number;
  public max: number;
  public items: number[];
  public localIndex = 0;

  formControl = new UntypedFormControl([undefined, Validators.required]);
  form = new UntypedFormGroup({ screen: this.formControl });

  @Input() type: 'route' | 'query' = 'route';

  @Input() set indexes([min, max]: [number, number]) {
    this.min = min;
    this.max = max;

    this.localIndex = 0;
    this.formControl.setValue(
      typeof this.formControl.value === 'number' ? min : undefined,
    );
    this.formControl.setValidators([
      Validators.min(min),
      Validators.max(max),
      Validators.required,
    ]);
    this.items = new Array(max - min + 1).fill(null).map((_, i) => min + i + 1);
  }

  @Input() baseRoute: string;

  private static keys = 0;

  key = ++PopupComponent.keys;

  Math = Math;

  goTo(screen: number | string) {
    switch (this.type) {
      case 'route':
        this.router.navigate([this.baseRoute, screen]);
        return;
      case 'query':
        this.router.navigate(['.'], {
          queryParams: { page: screen },
          queryParamsHandling: 'merge',
        });
        return;
    }
  }

  constructor(private readonly router: Router) {}
}
