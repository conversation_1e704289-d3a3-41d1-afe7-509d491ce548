<div class="flex flex-row w-full items-center">
  @if (items.length > 12) {
    <div
      class="w-10 h-10 rounded-lg cursor-pointer bg-opacity-50 leading-none inline-flex hover:text-gray-200 justify-center items-center p-2 hover:bg-gray-750 hover:shadow-md transition"
      [ngClass]="{ 'pointer-events-none opacity-50': localIndex <= 0 }"
      (click)="localIndex = Math.max(0, localIndex - 1)"
    >
      <img src="assets/arrow-left-small.svg" class="" />
    </div>
  }
  <div [class.w-full]="items.length <= 4">
    <div
      [ngClass]="{
        'grid grid-cols-4 w-40': items.length > 4,
        'flex flex-row w-full justify-center items-center': items.length <= 4,
      }"
    >
      @for (
        subItem of items | slice: localIndex * 4 * 3 : (localIndex + 1) * 4 * 3;
        track subItem
      ) {
        <a
          class="w-10 h-10 rounded-lg cursor-pointer bg-opacity-50 leading-none inline-flex hover:text-gray-200 justify-center items-center p-2 hover:bg-gray-750 hover:shadow-md transition"
          [routerLink]="type === 'route' ? [baseRoute, subItem - 1] : '.'"
          [queryParams]="{ page: subItem - 1 }"
          queryParamsHandling="merge"
        >
          {{ subItem }}
        </a>
      }
    </div>
  </div>
  @if (items.length > 12) {
    <div
      class="w-10 h-10 rounded-lg cursor-pointer bg-opacity-50 leading-none inline-flex hover:text-gray-200 justify-center items-center p-2 hover:bg-gray-750 hover:shadow-md transition"
      [ngClass]="{
        'pointer-events-none opacity-50':
          localIndex >= Math.floor(items.length / 4 / 3),
      }"
      (click)="
        localIndex = Math.min(Math.floor(items.length / 4 / 3), localIndex + 1)
      "
    >
      <img src="assets/arrow-right-small.svg" class="" />
    </div>
  }
</div>

<form
  class="border-t border-gray-750 w-full p-2"
  [formGroup]="form"
  (ngSubmit)="goTo(form.value.screen - 1)"
>
  <label
    [for]="'open-index-' + key"
    class="no-suffix font-stag font-light my-0 normal-case w-full flex justify-center items-center"
    style="letter-spacing: normal"
  >
    Gehe zur Seite
    <input
      [id]="'open-index-' + key"
      formControlName="screen"
      class="text-white bg-gray-750 focus:bg-gray-700 focus:outline-none plain border-0 rounded-l text-sm w-12 ml-2 h-6 pr-0 py-0 pl-2 -mr-4"
      [min]="max + 1"
      [max]="max + 1"
      type="number"
      [step]="1"
    />
    <button
      type="submit"
      class="bg-gray-750 cursor-pointer h-6 bg-transparent rounded-r w-6 flex justify-center items-center"
      [disabled]="formControl.disabled"
    >
      <img
        src="assets/arrow-right-small.svg"
        [class.opacity-25]="formControl.disabled"
      />
    </button>
  </label>
</form>
