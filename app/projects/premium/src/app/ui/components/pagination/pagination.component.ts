import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { NgClass } from '@angular/common';

@Component({
  selector: 'lib-pagination',
  template: `
    @for (slide of Pages; track slide) {
      <div
        class="py-1 md:py-2 cursor-pointer mr-2"
        (click)="selectIndex.emit(slide * this.slidesPerView)"
      >
        <div
          class="w-6 sm:w-8 h-1/2 sm:h-1"
          [ngClass]="{ 'bg-white': !golden, 'bg-golden': golden }"
          [class.opacity-50]="getNormalizedIndex(selected) !== slide"
        ></div>
      </div>
    }
  `,
  styleUrls: ['./pagination.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass],
})
export class PaginationComponent implements OnInit {
  @Input() amount: number;
  @Input() selected = 0;
  @Input() slidesPerView = 1;

  @Input() golden = false;

  @Output() selectIndex = new EventEmitter<number>();

  get Pages(): number[] {
    return new Array(Math.ceil(this.amount / this.slidesPerView))
      .fill(0)
      .map((_, i) => i);
  }

  getNormalizedIndex(index: number) {
    return Math.floor(index / this.slidesPerView);
  }

  constructor() {}

  ngOnInit(): void {}
}
