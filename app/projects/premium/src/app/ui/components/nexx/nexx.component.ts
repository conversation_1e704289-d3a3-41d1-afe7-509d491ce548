/// <reference path="./nexx.d.ts" />
import {
  afterNextRender,
  AfterViewInit,
  Directive,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnDestroy,
  Output,
} from '@angular/core';
import { distinctJSON } from '@pb/ui';
import {
  BehaviorSubject,
  from,
  fromEvent,
  Observable,
  Subscription,
} from 'rxjs';
import { filter, first, map, mergeMap } from 'rxjs/operators';

let instances = 0;

@Directive({
  selector: 'div[nexxID]',
  standalone: true,
})
export class NexxComponent implements OnDestroy {
  private uriBehav = new BehaviorSubject<string | undefined>(undefined);
  private player = new BehaviorSubject<NexxVideo | undefined>(undefined);
  private intendedPlayState = new BehaviorSubject(true);

  @Input() type: 'video' | 'playlist' = 'video';

  private Player: Observable<NexxVideo> = this.player.pipe(
    filter((v) => !!v),
    mergeMap((v) =>
      from(
        new Promise<INexxVideoPlaystateEvent>((res) =>
          v.addPlaystateListener((playstate) => res(playstate)),
        ),
      ).pipe(
        // tap(console.log),
        filter((v) => v.event === 'playeradded'),
        map(() => v),
      ),
    ),
  );
  // pollUntil(100, 900, (p) => !!p.isReady)

  private get URI() {
    return this.uriBehav.pipe(
      distinctJSON(),
      filter((v) => !!v),
    );
  }

  private _uri: string;
  @Input() set nexxID(uri: string) {
    if (uri === this._uri) {
      return;
    }
    this.uriBehav.next(uri);
    this._uri = uri;
  }

  @Output() onPlayingChange = new EventEmitter<boolean>();

  private subURI?: Subscription;
  private subPlayer?: Subscription;
  private subIntendedPlayState?: Subscription;

  public static players: { [key: string]: NexxVideo } = {};

  private didInit = false;

  constructor(
    private readonly element: ElementRef<HTMLElement>,
    private zone: NgZone,
  ) {
    this.element.nativeElement.id = `nexx-player-element-${instances++}`;

    // this.subIntendedPlayState = combineLatest([this.Player, this.intendedPlayState]).pipe(
    //   filter(([player]) => !!player)
    // ).subscribe(([player, intendedPlayState]) => {
    //   console.log('asf', intendedPlayState, player);
    //   // @ts-ignore
    //   player.play();
    // })

    afterNextRender(() => {
      if (this.didInit) {
        return;
      }
      this.didInit = true;
      try {
        if (
          // @ts-ignore
          typeof window._play == 'object' &&
          window._play.control.sdkIsReady()
        ) {
          this.init();
        } else {
          this.subPlayer = fromEvent(window, 'nexxplay.ready').subscribe(() =>
            this.zone.run(() => {
              this.init();
            }),
          );
        }
      } catch (e) {
        console.warn('Error while initializing nexx player', e);
      }
    });
  }

  init() {
    this.subURI?.unsubscribe();
    this.subURI = this.URI.subscribe((v) => {
      if (!this.element.nativeElement.id) {
        return;
      }
      if (this.player.getValue()) {
        this.player.getValue();
        window._play.control.interact.swapToMediaItem(
          this.element.nativeElement.id,
          v,
          this.type,
        );
      } else {
        this.zone.runOutsideAngular(() => {
          // @ts-ignore
          NexxComponent.players[v] = window._play.control.addPlayer(
            this.element.nativeElement.id,
            v,
            this.type,
          );
          this.player.next(NexxComponent.players[v]);
        });
      }
    });
  }

  set Playing(playing: boolean) {
    this.intendedPlayState.next(playing);
  }

  ngOnDestroy(): void {
    this.subURI?.unsubscribe();
    this.subPlayer?.unsubscribe();
    this.subIntendedPlayState?.unsubscribe();
    this.uriBehav.complete();
    this.player.getValue()?.remove();
  }
}
