type NexxPlayerMediaType = 'video' | 'playlist';

interface NexxPlayer {
  control: {
    sdkIsReady(): boolean;
    addPlayer(
      element_id: string,
      nexx_id: string,
      type: NexxPlayerMediaType,
    ): NexxVideo;
    interact: {
      swapToMediaItem(
        element_id: string,
        nexx_id: string,
        type: NexxPlayerMediaType,
      );
    };
  };
}

interface INexxVideoPlaystateEvent {
  event: 'playeradded';
}

interface NexxVideo {
  addPlaystateListener(
    callback: (playState: INexxVideoPlaystateEvent) => void,
  ): void;
  play(): void;
  remove(): void;
}

declare interface Window {
  _play?: NexxPlayer;
}
