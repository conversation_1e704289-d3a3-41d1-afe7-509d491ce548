import { CommonModule, KeyValuePipe } from '@angular/common'; // Füge dies hinzu
import {
  animate,
  keyframes,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';
import { ChipComponent } from '@pb/ui/components/chip/chip.component';
import { SortOptions } from '../../../services/girl-info/models';

type FilterType = 'sorting' | 'filter';

export type FilterValue = { title: string; value: string };

export interface IFilterOptions {
  [key: string]: {
    options: FilterValue[];
    type: 'single' | 'multi';
  };
}

export interface IFilterSelection {
  [key: string]: string[] | string;
}

@Component({
  selector: 'lib-filter-dropdown',
  templateUrl: './filter-dropdown.component.html',
  styleUrls: ['./filter-dropdown.component.css'],
  animations: [
    trigger('dropdownAnimation', [
      transition('void => *', [
        animate(
          '150ms ease-out',
          keyframes([
            style({ transform: 'translateY(-100%)' }),
            style({ transform: 'translateY(0)' }),
          ]),
        ),
      ]),
      transition('* => void', [
        animate(
          '150ms ease-in',
          keyframes([
            style({ transform: 'translateY(0)' }),
            style({ transform: 'translateY(-100%)' }),
          ]),
        ),
      ]),
    ]),
  ],
  imports: [
    CommonModule, // Füge hier das CommonModule hinzu
    KeyValuePipe,
    ChipComponent,
  ],
})
export class FilterDropdownComponent {
  isSortingOpen = false;
  isFilterOpen = false;

  @Input() selectedSorting?: SortOptions;
  @Input() selectedFilter: IFilterSelection = {};

  @Input() sortingOptions: string[];
  @Input() filterOptions: IFilterOptions;

  @Output() selectedSortChange = new EventEmitter<SortOptions | undefined>();
  @Output() selectedFilterChange = new EventEmitter<IFilterSelection>();
  protected readonly Object = Object;

  constructor(private elRef: ElementRef) {}

  public toggleDropdown(type: FilterType): void {
    switch (type) {
      case 'sorting':
        this.isSortingOpen = !this.isSortingOpen;
        break;
      case 'filter':
        this.isFilterOpen = !this.isFilterOpen;
        break;
    }
  }

  public selectOption(
    clickedOption: { category: string; choice: SortOptions },
    type: FilterType,
  ): void {
    const choice = clickedOption.choice;
    switch (type) {
      case 'sorting':
        this.selectedSorting = choice;
        this.selectedSortChange.emit(choice);
        break;
      case 'filter':
        const f = JSON.parse(
          JSON.stringify(this.selectedFilter),
        ) as IFilterSelection;
        switch (this.filterOptions[clickedOption.category].type) {
          case 'multi':
            if (
              !f[clickedOption.category] ||
              !Array.isArray(f[clickedOption.category])
            ) {
              f[clickedOption.category] = [clickedOption.choice];
            } else {
              if (
                f[clickedOption.category].indexOf(clickedOption.choice) >= 0
              ) {
                f[clickedOption.category] = (
                  f[clickedOption.category] as string[]
                ).filter((v) => v !== clickedOption.choice);
              } else {
                f[clickedOption.category] = [
                  ...f[clickedOption.category],
                  clickedOption.choice,
                ];
              }
            }
            break;
          case 'single':
            if (Array.isArray(f[clickedOption.category])) {
              delete f[clickedOption.category];
            }
            f[clickedOption.category] =
              (f[clickedOption.category] as string) === clickedOption.choice
                ? undefined
                : clickedOption.choice;
            break;
        }
        this.selectedFilter = JSON.parse(JSON.stringify(f));
        this.selectedFilterChange.emit(JSON.parse(JSON.stringify(f)));
        break;
    }
  }

  public originalOrder(): number {
    return 0;
  }

  isFilterEnabled(cat: string, choice: string): boolean {
    return this.selectedFilter[cat]?.indexOf(choice) >= 0;
  }

  getNameOfFilterValue(key: any, title: any): string {
    const filterOption = this.filterOptions[key].options.find(
      (i) => i.value === title,
    );
    if (filterOption) {
      return filterOption.title;
    }

    return '';
  }

  /**
   * Clear all filter pills.
   */
  clearAllFilters(): void {
    this.selectedFilter = {};
    this.selectedFilterChange.emit({});
  }

  @HostListener('document:click', ['$event'])
  private closeDropdown(event: Event): void {
    if (
      this.isSortingOpen &&
      !this.elRef.nativeElement.contains(event.target)
    ) {
      this.isSortingOpen = false;
      this.isFilterOpen = false;
    }
  }
}
