<div class="relative flex justify-between md:mb-15">
  <ng-template #pill let-key="key" let-title="title" let-type="type">
    <button
      (click)="selectOption({ category: key, choice: title }, 'filter')"
      aria-label="'{{ title }}' entfernen"
      class="flex-shrink-0 uppercase tracking-widest inline-flex flex-row items-center justify-center px-2 py-1 bg-white border rounded text-black text-xs"
    >
      @if (type === "multi" || key === "Vorliebe") {
        <span>{{ getNameOfFilterValue(key, title) }}</span>
      } @else {
        <span>{{ title }}</span>
      }
      <svg
        fill="none"
        height="24"
        viewBox="0 0 24 24"
        width="24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          height="24"
          id="mask0_2089_62894"
          maskUnits="userSpaceOnUse"
          style="mask-type: alpha"
          width="24"
          x="0"
          y="0"
        >
          <rect fill="#D9D9D9" height="24" width="24" />
        </mask>
        <g mask="url(#mask0_2089_62894)">
          <path
            d="M8.22707 16.8355L7.16357 15.772L10.9366 11.999L7.16357 8.251L8.22707 7.1875L12.0001 10.9605L15.7481 7.1875L16.8116 8.251L13.0386 11.999L16.8116 15.772L15.7481 16.8355L12.0001 13.0625L8.22707 16.8355Z"
            fill="black"
          />
        </g>
      </svg>
    </button>
  </ng-template>

  <div class="flex flex-row justify-end items-center w-full">
    <!-- Filter pills -->
    <div class="flex-row gap-4 flex-wrap flex-1 hidden md:flex">
      @for (filter of selectedFilter | keyvalue; track filter.key) {
        @if (typeof filter.value === "string") {
          <ng-container
            *ngTemplateOutlet="
              pill;
              context: { key: filter.key, title: filter.value, type: 'single' }
            "
          >
          </ng-container>
        } @else if (
          filter.key === "Haarfarbe" ||
          filter.key === "Kategorie" ||
          filter.key === "Augenfarbe"
        ) {
          @for (item of filter.value; track item) {
            <ng-container
              *ngTemplateOutlet="
                pill;
                context: { key: filter.key, title: item, type: 'multi' }
              "
            >
            </ng-container>
          }
        }
      }

      @if ((selectedFilter | keyvalue).length > 0) {
        <button
          class="mx-2 text-gray-400 text-sm underline"
          (click)="clearAllFilters()"
        >
          Alle löschen
        </button>
      }
    </div>
    <div class="p-4 flex items-center cursor-pointer">
      @if (isFilterOpen) {
        <div
          (click)="toggleDropdown('filter')"
          class="p-4 flex items-center cursor-pointer bg-black text-white"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <mask
              id="mask0_2657_10935"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="0"
              y="0"
              width="24"
              height="24"
            >
              <rect width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2657_10935)">
              <path
                d="M6.40043 18.6552L5.34668 17.6014L10.9467 12.0014L5.34668 6.40141L6.40043 5.34766L12.0004 10.9477L17.6004 5.34766L18.6542 6.40141L13.0542 12.0014L18.6542 17.6014L17.6004 18.6552L12.0004 13.0552L6.40043 18.6552Z"
                fill="white"
              />
            </g>
          </svg>
          <a
            class="text-white text-sm tracking-widest uppercase ml-2 hidden md:block"
            >Schließen</a
          >
          <a class="text-white text-sm tracking-widest uppercase ml-2 md:hidden"
            >Filter</a
          >
        </div>
      } @else {
        <div
          (click)="toggleDropdown('filter')"
          class="p-4 flex items-center cursor-pointer"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <mask
              id="mask0_2648_1690"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="0"
              y="0"
              width="24"
              height="24"
            >
              <rect width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2648_1690)">
              <path
                d="M11.3839 19.5C11.1326 19.5 10.9223 19.4153 10.7532 19.246C10.5838 19.0768 10.4992 18.8666 10.4992 18.6152V12.827L4.90116 5.7155C4.70883 5.459 4.68091 5.19233 4.81741 4.9155C4.95408 4.6385 5.18458 4.5 5.50891 4.5H18.4894C18.8137 4.5 19.0442 4.6385 19.1809 4.9155C19.3174 5.19233 19.2895 5.459 19.0972 5.7155L13.4992 12.827V18.6152C13.4992 18.8666 13.4145 19.0768 13.2452 19.246C13.076 19.4153 12.8657 19.5 12.6144 19.5H11.3839ZM11.9992 12.3L16.9492 6H7.04916L11.9992 12.3Z"
                fill="white"
              />
            </g>
          </svg>

          <a class="text-white text-sm tracking-widest uppercase ml-2"
            >Filter</a
          >
        </div>
      }
      <!--  Filter flyout  -->
      <div
        class="absolute -left-8 -right-8 z-20 overflow-hidden"
        style="top: 4.235rem"
      >
        <div
          *ngIf="isFilterOpen"
          [@dropdownAnimation]
          class="flex flex-col justify-start p-4 px-8 md:py-20 w-full backdrop-filter backdrop-blur-md backdrop-brightness-50 bg-black bg-opacity-80"
        >
          <!--        <div-->
          <!--          (click)="toggleDropdown('filter')"-->
          <!--          class="flex items-center mb-4 cursor-pointer select-none"-->
          <!--        >-->
          <!--          <div class="hidden md:flex w-1/6"></div>-->
          <!--          <img class="mr-2" src="assets/close-circle.svg" />-->
          <!--          <a class="headline-4 select-none">Filter schließen</a>-->
          <!--        </div>-->
          <div
            *ngIf="selectedFilter as selectedFilter"
            class="flex mx-auto flex-col lg:flex-row"
          >
            <div
              *ngFor="let option of filterOptions | keyvalue: originalOrder"
              class="mx-4 mt-8 lg:mt-0"
            >
              <p>{{ option.key }}</p>
              <div
                class="flex flex-row flex-wrap lg:flex-nowrap lg:flex-col items-start lg:mt-5"
              >
                <app-chip
                  (click)="
                    selectOption(
                      { category: option.key, choice: choice.value },
                      'filter'
                    )
                  "
                  *ngFor="let choice of option.value.options"
                  [class.selected]="isFilterEnabled(option.key, choice.value)"
                  class="mt-2 mr-2 lg:my-1 lg:mr-0 flex"
                >
                  {{ choice.title }}
                  <img
                    *ngIf="isFilterEnabled(option.key, choice.value)"
                    class="hide-image ml-2"
                    alt=""
                    src="/assets/close-white.svg"
                  />
                </app-chip>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- sorting -->
      <div class="relative z-20 sortBtn">
        <div
          (click)="toggleDropdown('sorting')"
          class="flex items-center p-4 cursor-pointer select-none"
        >
          <a class="pr-4 text-white text-sm tracking-widest uppercase">{{
            selectedSorting || "Sortieren"
          }}</a>
          <img class="transform rotate-90" alt="" src="assets/chevron_forward.svg" />
        </div>
        <div
          [style.display]="isSortingOpen ? '' : 'none'"
          class="absolute top-0 left-0 right-0 flex flex-col border border-solid border-gray-400 bg-gray-900 p-2 mt-12 cursor-pointer select-none"
        >
          <a
            (click)="
              selectOption({ choice: option }, 'sorting');
              toggleDropdown('sorting')
            "
            *ngFor="let option of sortingOptions"
            class="pr-4 text-gray-400 py-1"
          >
            {{ option }}
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
