import { Component, Input, OnInit } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'lib-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.css'],
  imports: [RouterLink, NgTemplateOutlet],
})
export class CardComponent implements OnInit {
  @Input() image?: string;

  @Input() link?: string | string[];

  constructor() {}

  ngOnInit(): void {}
}
