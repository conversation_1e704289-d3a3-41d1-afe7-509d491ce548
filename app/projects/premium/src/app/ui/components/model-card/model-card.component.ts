import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'lib-model-card',
  templateUrl: './model-card.component.html',
  styleUrls: ['./model-card.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
})
export class ModelCardComponent {
  @Input() name = '';
  @Input() image: string | null = null;
  @Input() loading = false;
}
