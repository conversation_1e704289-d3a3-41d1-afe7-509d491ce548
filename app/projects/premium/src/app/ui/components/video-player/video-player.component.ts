import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  Output,
  ViewChild,
} from '@angular/core';
import { distinctJSON } from '../../utils';
import { environment } from 'projects/premium/src/environments/environment';
import {
  combineLatest,
  merge,
  Observable,
  Subscription,
  timer,
  of,
  fromEvent,
  BehaviorSubject,
  Subject,
} from 'rxjs';
import { filter, map, mergeMap, tap, first, startWith } from 'rxjs/operators';
import { NexxComponent } from '@pb/ui/components/nexx/nexx.component';

@Component({
  selector: 'lib-video-player',
  templateUrl: './video-player.component.html',
  styleUrls: ['./video-player.component.css'],
  imports: [NexxComponent],
})
export class VideoPlayerComponent implements AfterViewInit, OnDestroy {
  @Input() poster: string;
  @Input() nexx: string;
  @Input() autoplay = false;

  @ViewChild('videoElement') videoElement: ElementRef<HTMLDivElement>;

  timeStampObs: Observable<number>;
  durationObs: Observable<number>;
  percObs: Observable<number>;
  volumeObs: Observable<number>;
  mutedObs: Observable<boolean>;
  playingObs: Observable<boolean>;
  public _onFinishBehav = new Subject();
  public onFinish = this._onFinishBehav.asObservable();
  showPoster = true;
  public initialized = false;

  private posterSub: Subscription;
  private endedSub: Subscription;
  private nexxReadySub: Subscription;

  public debug = !environment.production;
  public showDebug = false;

  @HostListener('document:fullscreenchange', ['$event'])
  private fs() {
    this._fullscreen = document.fullscreenElement === this.ele.nativeElement;
  }

  public seek(to: number) {
    this.showPoster = false;
    // this.videoElement.nativeElement.currentTime = to;
  }

  private _fullscreen = false;
  public get Fullscreen(): boolean {
    return this._fullscreen;
  }
  public set Fullscreen(fullscreen: boolean) {
    if (fullscreen === this._fullscreen) {
      return;
    }
    if (!fullscreen) {
      document.exitFullscreen();
    } else {
      this.ele.nativeElement.requestFullscreen();
    }
  }

  constructor(private ele: ElementRef<HTMLElement>) {}

  @Output() next = new EventEmitter();
  @Output() prev = new EventEmitter();

  @HostListener('swipeleft', ['$event'])
  private swipeleft(e: any) {
    this.prev.emit();
  }

  @HostListener('swiperight', ['$event'])
  private swiperight(e: any) {
    this.next.emit();
  }

  ngOnDestroy(): void {
    this.posterSub?.unsubscribe();
    this.endedSub?.unsubscribe();
  }

  play() {
    // this.videoElement.nativeElement.play();
  }
  pause() {
    // this.videoElement.nativeElement.pause();
  }
  toggle() {
    // this[this.videoElement?.nativeElement?.paused ? 'play' : 'pause']();
  }

  setVolume(volume: number) {
    // this.videoElement.nativeElement.volume = volume;
    if (volume > 0) {
      this.setMuted(false);
    }
  }

  setMuted(muted: boolean) {
    // this.videoElement.nativeElement.muted = muted;
  }

  prevBtn() {
    // this.videoElement.nativeElement.currentTime = 0;
    this.play();
  }

  nextBtn() {
    this.pause();
    // this.videoElement.nativeElement.currentTime = this.videoElement.nativeElement.duration;
  }

  ngAfterViewInit(): void {
    if (this.initialized || !this.videoElement?.nativeElement) {
      return;
    }
    this.initialized = true;
    const v = this.videoElement.nativeElement;

    // this.playingObs = merge(
    //   of(false),
    //   fromEvent(v, 'playing'),
    //   fromEvent(v, 'pause'),
    //   fromEvent(v, 'ended')
    // ).pipe(
    //   map(() => !v.paused),
    //   distinctJSON()
    // );

    // this.durationObs = fromEvent(v, 'durationchange').pipe(
    //   startWith(v.duration), map(() => v.duration), distinctJSON()
    // );

    // this.volumeObs = fromEvent(v, 'volumechange').pipe(
    //   startWith(v.volume),
    //   map(() => v.volume), distinctJSON()
    // );

    // this.mutedObs = fromEvent(v, 'volumechange').pipe(
    //   startWith(v.muted),
    //   map(() => v.muted),
    //   distinctJSON()
    // );

    // this.endedSub = fromEvent(v, 'ended')
    //   .subscribe(() => this._onFinishBehav.next());

    // this.timeStampObs = timer(0, 1000 / 60).pipe(map(() => v.currentTime), distinctJSON());

    // this.percObs = combineLatest([this.durationObs, this.timeStampObs]).pipe(
    //   map(([duration, timestamp]) => timestamp / duration),
    //   distinctJSON()
    // );

    this.posterSub = this.playingObs
      .pipe(filter((v) => v))
      .subscribe(() => (this.showPoster = false));
  }
}
