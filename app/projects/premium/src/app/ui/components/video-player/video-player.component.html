@if (nexx) {
  <div [nexxID]="nexx" class="w-full h-full flex flex-1"></div>
}

<!-- <div *ngIf="debug" class="absolute top-0 left-0 bg-black bg-opacity-50 z-50 text-xs p-2">
<a (click)="showDebug = !showDebug" class="cursor-pointer flex" [class.mb-2]="showDebug">{{showDebug ? 'hide' :
'show'}} debug</a>

<ng-container *ngIf="showDebug">

  <p>
    showPoster: {{showPoster}}<br>nexx
    initialized: {{initialized}}<br>
    scrubbing: {{scrubbing}}<br>
    scrubContinue: {{scrubContinue}}<br>
  </p>

  <p>
    <ng-container *ngIf="timeStampObs">timeStampObs: {{timeStampObs | async}}<br></ng-container>
    <ng-container *ngIf="durationObs">durationObs: {{durationObs | async}}<br></ng-container>
    <ng-container *ngIf="percObs">percObs: {{percObs | async}}<br></ng-container>
    <ng-container *ngIf="volumeObs">volumeObs: {{volumeObs | async}}<br></ng-container>
    <ng-container *ngIf="mutedObs">mutedObs: {{mutedObs | async}}<br></ng-container>
    <ng-container *ngIf="playingObs">playingObs: {{playingObs | async}}<br></ng-container>
  </p>

</ng-container>
</div>

<div class="flex flex-1 relative cursor-pointer" (click)="toggle()">

  <div class="absolute h-full left-0 w-full top-0" #videoWrapper></div>


  <video class="absolute h-full left-0 w-full top-0" [class.opacity-0]="poster && showPoster" [autoplay]="autoplay"
    #videoElement>
    <source [src]="video">
  </video>

  <img *ngIf="poster && showPoster" class="absolute left-0 top-0 w-full h-full object-contain" [src]="poster">

</div>

<lib-video-player-controls *ngIf="initialized" [percentage]="percObs | async" [fullscreen]="Fullscreen"
  [playing]="playingObs | async" (setFullscreen)="Fullscreen = $event" (prev)="prevBtn()" (next)="nextBtn()"
  (seek)="seek($event)" [timeStamp]="timeStampObs | async" [duration]="durationObs | async" [volume]="volumeObs | async"
  (setPlaying)="$event ? play() : pause()" (setVolume)="setVolume($event)" [muted]="mutedObs | async" (setMuted)="setMuted($event)">
</lib-video-player-controls> -->
