@if (percentage) {
  <div
    class="bg-golden h-2 relative cursor-ew-resize"
    (mousedown)="startScrubbing($event)"
    (click)="seekClick($event)"
    [class.opacity-75]="scrubbing"
  >
    <div
      class="bg-gray-800 absolute inset-y-0 right-0"
      [style.left.%]="percentage * 100"
    ></div>
  </div>
}

<div class="flex flex-row px-10 py-1 items-center bg-black">
  <img
    (click)="toggle()"
    src="assets/video-player/play+pause.svg"
    class="w-8 h-8 cursor-pointer mr-12"
  />
  <img
    (click)="prev.emit()"
    src="assets/video-player/previous.svg"
    class="w-8 h-8 cursor-pointer mr-12"
  />
  <img
    (click)="next.emit()"
    src="assets/video-player/next.svg"
    class="w-8 h-8 cursor-pointer mr-12"
  />
  <img
    (click)="setFullscreen.emit(!fullscreen)"
    src="assets/video-player/fullscreen-control.svg"
    class="w-8 h-8 cursor-pointer mr-12"
    [class.opacity-50]="fullscreen"
  />

  <div class="relative group mr-12">
    <img
      src="assets/video-player/volume-controls.svg"
      class="w-8 h-8 cursor-pointer"
      (click)="volume === 0 ? setVolume.emit(0.5) : setMuted.emit(!muted)"
    />

    @if (volume === 0 || muted) {
      <div
        class="rounded-full w-full h-1 bg-red-500 absolute left-1/2 top-1/2 transform rotate-45 pointer-events-none -translate-x-1/2 -translate-y-1/2"
      ></div>
    }

    @if (volume > 0.3) {
      <div
        class="rounded-full h-3 w-1 bg-white absolute left-6 -ml-px top-1/2 transform pointer-events-none -translate-y-1/2"
      ></div>
    }
    @if (volume > 0.6) {
      <div
        class="rounded-full h-5 w-1 bg-white absolute left-7 ml-px top-1/2 transform pointer-events-none -translate-y-1/2"
      ></div>
    }

    <div
      class="hidden group-hover:flex absolute bottom-full left-1/2 transform -translate-x-1/2 bg-black py-2 w-full rounded-t-2xl"
    >
      <input
        type="range"
        min="0"
        max="100"
        [value]="volume * 100"
        (input)="setVolume.emit($event.target.value / 100)"
        class="w-full"
      />
    </div>
  </div>

  <p>{{ timeStamp | time }}/{{ duration | time }}</p>

  <div class="flex flex-1"></div>
  <img src="assets/video-player/fav.svg" class="w-8 h-8 mr-12" />
  <p class="mr-12">000 Views</p>
  <div class="relative">
    <img src="assets/video-player/settings.svg" class="w-8 h-8" />
    <span
      class="bg-golden absolute top-0 right-0 transform translate-x-1/2 text-xs px-1 rounded-full"
      >HD</span
    >
  </div>
</div>
