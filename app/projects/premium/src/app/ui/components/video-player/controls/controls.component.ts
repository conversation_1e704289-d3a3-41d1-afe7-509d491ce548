import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { TimePipe } from '@pb/ui';

@Component({
  selector: 'lib-video-player-controls',
  templateUrl: './controls.component.html',
  styleUrls: ['./controls.component.css'],
  standalone: true,
  imports: [TimePipe],
})
export class ControlsComponent implements OnInit {
  @Input() percentage = 0;
  @Input() playing = false;
  @Input() fullscreen = false;
  @Input() muted = false;
  @Input() timeStamp = 0;
  @Input() duration = 0;
  @Input() volume = 0;

  @Output() setPlaying = new EventEmitter<boolean>();
  @Output() setFullscreen = new EventEmitter<boolean>();
  @Output() prev = new EventEmitter();
  @Output() next = new EventEmitter();
  @Output() seek = new EventEmitter<number>();
  @Output() setVolume = new EventEmitter<number>();
  @Output() setMuted = new EventEmitter<boolean>();

  toggle() {
    this.setPlaying.emit(!this.playing);
  }

  public scrubbing = false;
  private scrubContinue = false;

  seekClick({ pageX, ...e }: MouseEvent, isScrub = false) {
    const percTo = pageX / window.innerWidth;
    this.seek.emit(percTo * this.duration);
    if (!isScrub) {
      this.scrubbing = false;
    }
    return;
  }

  startScrubbing(event: MouseEvent) {
    this.scrubContinue = this.playing;
    this.scrubbing = true;
    this.setPlaying.emit(false);
    this.seekClick(event, true);
    event.preventDefault();
  }

  @HostListener('window:mousemove', ['$event'])
  scrub(event: MouseEvent) {
    if (!this.scrubbing) {
      return;
    }
    this.seekClick(event, true);
  }

  @HostListener('window:mouseup')
  endScrub() {
    if (!this.scrubbing) {
      return;
    }
    if (this.scrubContinue) {
      this.setPlaying.emit(true);
    }
    this.scrubbing = false;
  }

  constructor() {}

  ngOnInit(): void {}
}
