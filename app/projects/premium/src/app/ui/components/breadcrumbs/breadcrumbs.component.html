<ng-template #breadcrumb let-label="label" let-link="link" let-action="action">
  @if (action) {
    <span (click)="action()" class="cursor-pointer whitespace-nowrap">{{
      label
    }}</span>
  }
  @if (link) {
    <a [routerLink]="link" class="whitespace-nowrap">{{ label }}</a>
  }
  @if (!action && !link) {
    <p class="whitespace-nowrap">
      {{ label }}
    </p>
  }
</ng-template>

<ng-container
  *ngTemplateOutlet="
    breadcrumb;
    context: { label: 'Startseite', link: '/', action: null }
  "
></ng-container>

@for (bc of breadcrumbs || []; track bc.link; let last = $last) {
  <span class="caret" [class.last]="last"></span>
  <ng-container *ngTemplateOutlet="breadcrumb; context: bc"></ng-container>
}
