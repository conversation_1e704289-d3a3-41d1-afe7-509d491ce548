import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  Input,
  HostBinding,
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgTemplateOutlet } from '@angular/common';

export interface IBreadcrumb {
  action?: () => void;
  link?: string | string[];
  label: string;
}
export type Breadcrumbs = IBreadcrumb[];

@Component({
  selector: 'lib-breadcrumbs',
  templateUrl: './breadcrumbs.component.html',
  styleUrls: ['./breadcrumbs.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgTemplateOutlet],
})
export class BreadcrumbsComponent implements OnInit {
  @Input() breadcrumbs: IBreadcrumb[];
  @HostBinding('class') private readonly cls = 'headline-4';

  constructor() {}

  ngOnInit(): void {}
}
