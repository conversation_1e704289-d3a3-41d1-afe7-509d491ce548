import {
  animate,
  keyframes,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  Input,
  HostBinding,
} from '@angular/core';

@Component({
  selector: 'app-chip',
  template: `
    <ng-content></ng-content>
    @if (selected) {
      <div class="flex justify-center items-center max-w-none" [@closeIcon]>
        <img src="assets/close-white.svg" [style.width.px]="7" />
      </div>
    }
  `,
  styleUrls: ['./chip.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('closeIcon', [
      state('*', style({ width: 7, marginLeft: 8 })),
      transition('void => *', [
        animate(
          '250ms ease-out',
          keyframes([
            style({ width: 0, marginLeft: 0, opacity: 0 }),
            style({ width: 7, marginLeft: 8, opacity: 1 }),
          ]),
        ),
      ]),
      transition('* => void', [
        animate(
          '250ms ease-out',
          keyframes([
            style({ width: 7, marginLeft: 8, opacity: 1 }),
            style({ width: 0, marginLeft: 0, opacity: 0 }),
          ]),
        ),
      ]),
    ]),
  ],
  standalone: true,
})
export class ChipComponent implements OnInit {
  @HostBinding('class.selected')
  @Input()
  selected = false;

  constructor() {}

  ngOnInit(): void {}
}
