import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Input,
  OnDestroy,
  ViewChild,
} from '@angular/core';

import { NexxComponent } from '../nexx/nexx.component';
import { NgClass } from '@angular/common';

@Component({
  selector: 'lib-preview-video',
  templateUrl: './preview-video.component.html',
  styleUrls: ['./preview-video.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass],
})
export class PreviewVideoComponent implements AfterViewInit, OnDestroy {
  ngAfterViewInit(): void {}

  ngOnDestroy(): void {}

  @Input() poster: string;
  @Input() posterAlt?: string;
  @Input() nexxID: string;
  @Input() autoSize = true;

  @Input() views?: number | undefined;
  @ViewChild('videoElement') videoElement: NexxComponent;

  play() {}
  // private readonly playingBehav = new BehaviorSubject(false);
  // private readonly playingSub: Subscription;
  // private loopSub: Subscription;
  // previewing = false;
  // duration: number;

  // @ViewChild('videoElement') videoElement: ElementRef<HTMLVideoElement>;

  // get PreviewStartTime(): number {
  //   return this.videoElement?.nativeElement.duration / 2 || 0;
  // }

  // get PreviewEndTime(): number {
  //   return this.PreviewStartTime + 2;
  // }

  // constructor() {
  //   this.playingSub = this.playingBehav
  //     .pipe(
  //       distinctUntilChanged((a, b) => a === b)
  //       // debounce(10),
  //     )
  //     .subscribe((play) => {
  //       if (!this.videoElement) {
  //         return;
  //       }
  //       this.previewing = play;
  //       this.videoElement.nativeElement.currentTime = this.PreviewStartTime;
  //       this.videoElement.nativeElement[play ? 'play' : 'pause']();
  //     });
  // }

  // ngAfterViewInit(): void {
  //   if (!this.videoElement) {
  //     return;
  //   }
  //   this.duration = this.videoElement.nativeElement.duration;
  //   this.loopSub?.unsubscribe();
  //   this.loopSub = this.playingBehav
  //     .pipe(
  //       distinctUntilChanged((a, b) => a === b),
  //       filter((v) => v),
  //       mergeMap(() =>
  //         fromEvent(this.videoElement.nativeElement, 'timeupdate')
  //       ),
  //       map(
  //         (v) =>
  //           this.videoElement.nativeElement.currentTime < this.PreviewEndTime
  //       ),
  //       distinctUntilChanged((a, b) => a === b),
  //       filter((v) => !v)
  //     )
  //     .subscribe(
  //       () =>
  //         (this.videoElement.nativeElement.currentTime = this.PreviewStartTime)
  //     );
  // }

  // ngOnDestroy(): void {
  //   this.playingSub?.unsubscribe();
  //   this.loopSub?.unsubscribe();
  //   this.playingBehav.complete();
  // }

  // @HostListener('mouseenter')
  // play(e?: MouseEvent) {
  //   e?.stopPropagation();
  //   e?.preventDefault();
  //   this.playingBehav.next(true);
  // }

  // @HostListener('mouseleave')
  // pause(e?: MouseEvent) {
  //   e?.stopPropagation();
  //   e?.preventDefault();
  //   this.playingBehav.next(false);
  // }
}
