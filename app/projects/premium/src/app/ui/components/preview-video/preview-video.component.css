:host {
  @apply flex justify-center items-center w-full overflow-hidden;
  aspect-ratio: 16/9;
}

:host:not(.absolute) {
  @apply relative;
}

:host-context(swiper-slide app-gallery-card) {
  @apply h-full w-full;
}

:host-context(swiper-slide app-gallery-card) img.z-0 {
  @apply object-cover object-center h-full w-full;
}

:host-context(.video-list) {
  aspect-ratio: 1;
}

:host-context(.video-list) img.video-preview {
  @apply object-cover object-center h-full w-full;
}
