import {
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { GetCDNImage } from '@pb/ui/pipes/cdn.pipe';
import moment from 'moment';

import { ITransform, TransformPipe } from '../../pipes/transform.pipe';
import { Moment } from 'moment';
import { PreloadImageComponent } from '@pb/ui/components/preload-image/preload-image.component';

@Component({
  selector: 'lib-zoomable-image',
  template: `
    <app-preload-image
      class="absolute left-0 w-full top-0 h-full z-0 pointer-events-none"
      [image]="image"
      [lowResSrc]="preview"
      [contain]="true"
      [class.cursor-grab]="image && transform?.z && !drag"
      [class.transition-transform]="!drag"
      [style.transform]="transform | transform"
      style="transform: translate(0, 0) scale(1);"
    ></app-preload-image>
  `,
  styleUrls: ['./zoomable-image.component.css'],
  imports: [PreloadImageComponent, TransformPipe],
})
export class ZoomableImageComponent {
  disableTransition = false;

  @ViewChild('imageElement', { static: true })
  imageElement: ElementRef<HTMLImageElement>;

  @Input() swipeDurationMS = 200;
  @Input() swipeMinDist = 40;

  @Input() preview: string;
  private _image: HTMLImageElement;
  @Input() set image(imageOrSrc: string | HTMLImageElement) {
    if (typeof imageOrSrc === 'string') {
      this._image = new Image();
      this._image.src = GetCDNImage(imageOrSrc);
    } else {
      this._image = imageOrSrc;
    }
  }
  get image(): HTMLImageElement {
    return this._image;
  }

  @Output() prev = new EventEmitter();
  @Output() next = new EventEmitter();

  private getRenderedSize(contains, cWidth, cHeight, width, height, pos) {
    var oRatio = width / height,
      cRatio = cWidth / cHeight;
    return function () {
      if (contains ? oRatio > cRatio : oRatio < cRatio) {
        this.width = cWidth;
        this.height = cWidth / oRatio;
      } else {
        this.width = cHeight * oRatio;
        this.height = cHeight;
      }
      this.left = (cWidth - this.width) * (pos / 100);
      this.right = this.width + this.left;
      return this;
    }.call({});
  }

  private getImgSizeInfo(img) {
    var pos = window
      .getComputedStyle(img)
      .getPropertyValue('object-position')
      .split(' ');
    return this.getRenderedSize(
      true,
      img.width,
      img.height,
      img.naturalWidth,
      img.naturalHeight,
      parseInt(pos[0]),
    );
  }

  private translate({ x = 0, y = 0, z = 0 }: Partial<ITransform>) {
    const t: ITransform = { x: 0, y: 0, z: 1, ...this.transform };
    t.z += z;
    t.x += x;
    t.y += y;
    this.transform = {
      ...t,
      z: Math.max(0, t.z),
    };
  }

  transform: ITransform = {
    x: 0,
    y: 0,
    z: 1,
  };

  @HostBinding('class.cursor-grabbing')
  drag:
    | (ITransform & {
        transformBefore: ITransform;
        initiator: 'touch' | 'mouse';
        type: 'pan' | 'gesture';
        start: Moment;
      })
    | undefined;

  @HostListener('touchstart', ['$event'])
  gesturestart(e: TouchEvent) {
    console.log('touchstart');
    if (e.touches.length === 0) {
      return;
    }
    e.preventDefault();
    const isSwipe = e.touches.length === 1;
    const midPoint = {
      x:
        new Array(e.touches.length)
          .fill(null)
          .map((_, i) => e.touches[i].clientX / window.innerWidth)
          .reduce((all, c) => c + all, 0) / e.touches.length,
      y:
        new Array(e.touches.length)
          .fill(null)
          .map((_, i) => e.touches[i].clientY / window.innerHeight)
          .reduce((all, c) => c + all, 0) / e.touches.length,
    };
    const zoomDist = isSwipe
      ? 0
      : Math.sqrt(
          Math.pow(
            new Array(e.touches.length)
              .fill(null)
              .map((_, i) => e.touches[i].clientX / window.innerWidth)
              .sort((a, b) => b - a)
              .reduce((all, c) => c - all, 0),
            2,
          ) +
            Math.pow(
              new Array(e.touches.length)
                .fill(null)
                .map((_, i) => e.touches[i].clientY / window.innerHeight)
                .sort((a, b) => b - a)
                .reduce((all, c) => c - all, 0),
              2,
            ),
        );
    this.drag = {
      initiator: 'touch',
      x: window.innerWidth * midPoint.x - (this.transform?.x || 0),
      y: window.innerHeight * midPoint.y - (this.transform?.y || 0),
      z: (this.transform?.z || 1) - zoomDist,
      type: isSwipe ? 'gesture' : 'pan',
      transformBefore: { ...(this.transform || { x: 0, y: 0, z: 0 }) },
      start: moment(),
    };
  }

  @HostListener('touchmove', ['$event'])
  touchMove(e: TouchEvent) {
    console.log('touchmove');
    e.preventDefault();
    if (this.drag?.initiator !== 'touch') {
      return;
    }
    let z = this.drag.z;
    const midPoint = {
      x:
        new Array(e.touches.length)
          .fill(null)
          .map((_, i) => e.touches[i].clientX / window.innerWidth)
          .reduce((all, c) => c + all, 0) / e.touches.length,
      y:
        new Array(e.touches.length)
          .fill(null)
          .map((_, i) => e.touches[i].clientY / window.innerHeight)
          .reduce((all, c) => c + all, 0) / e.touches.length,
    };
    let x = window.innerWidth * midPoint.x - this.drag.x;
    let y = window.innerHeight * midPoint.y - this.drag.y;
    console.log(x, 'x');
    console.log(y, 'y');
    if (this.drag.type !== 'gesture') {
      z += Math.sqrt(
        Math.pow(
          new Array(e.touches.length)
            .fill(null)
            .map((_, i) => e.touches[i].clientX / window.innerWidth)
            .sort((a, b) => b - a)
            .reduce((all, c) => c - all, 0),
          2,
        ) +
          Math.pow(
            new Array(e.touches.length)
              .fill(null)
              .map((_, i) => e.touches[i].clientY / window.innerHeight)
              .sort((a, b) => b - a)
              .reduce((all, c) => c - all, 0),
            2,
          ),
      );
    }
    let windowWidth = window.screen.width;
    let windowHeight = window.screen.height;
    let elWidth = this.getImgSizeInfo(this.image).width;
    let elHeight = this.getImgSizeInfo(this.image).height;
    let maxX = (windowWidth - elWidth) / 2 - elWidth / 2;
    let maxY = windowHeight - elHeight * 2;

    if (Math.abs(x) < Math.abs(maxX) && Math.abs(y) < Math.abs(maxY)) {
      this.transform = { x, y, z };
    }
  }

  @HostListener('mousedown', ['$event'])
  mousedown(e) {
    console.log(this.transform);
    console.log('pageX2', e.pageX);
    console.log('transform', this.transform?.x || 0);
    e.preventDefault();
    this.drag = {
      initiator: 'mouse',
      x: e.pageX - (this.transform?.x || 0),
      y: e.pageY - (this.transform?.y || 0),
      z: this.transform?.z || 1,
      transformBefore: { ...(this.transform || { x: 0, y: 0, z: 0 }) },
      type: 'pan',
      start: moment(),
    };
  }

  @HostListener('mousemove', ['$event'])
  @HostListener('gesturechange', ['$event'])
  gesturechange(e: MouseEvent) {
    e.preventDefault();
    if (this.drag?.initiator !== 'mouse') {
      return;
    }
    const z = this.drag.z;
    let x = e.pageX - this.drag.x;
    let y = e.pageY - this.drag.y;
    let windowWidth = window.screen.width;
    let windowHeight = window.screen.height;
    let elWidth = this.getImgSizeInfo(this.image).width;
    let elHeight = this.getImgSizeInfo(this.image).height;
    let maxX = (windowWidth - elWidth) / 2 - elWidth / 2;
    let maxY = windowHeight - elHeight * 2;

    if (Math.abs(x) < Math.abs(maxX) && Math.abs(y) < Math.abs(maxY)) {
      this.transform = { x, y, z };
    }
  }

  @HostListener('mouseup', ['$event'])
  @HostListener('mouseleave', ['$event'])
  @HostListener('touchend', ['$event'])
  private gestureend(e) {
    e.preventDefault();
    const delta = this.drag.transformBefore.x - this.transform.x;
    if (
      this.drag?.type === 'gesture' &&
      Math.abs(delta) > this.swipeMinDist &&
      moment().diff(this.drag.start, 'milliseconds') < this.swipeDurationMS
    ) {
      if (delta < 0) {
        this.prev.emit();
      } else if (delta > 0) {
        this.next.emit();
      }
      this.transform = undefined;
    }
    this.drag = undefined;
  }

  zoom(z: number) {
    this.translate({ z });
  }

  setZoom(z: number) {
    this.transform = { x: 0, y: 0, ...this.transform, z };
  }

  reset() {
    this.transform = undefined;
  }

  constructor() {}
}
