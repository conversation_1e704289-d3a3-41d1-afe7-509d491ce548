<header #headerBar class="relative z-0 justify-between h-full max-h-full">
  <button
    #trigger="cdkOverlayOrigin"
    (click)="isOpen = !isOpen"
    aria-label="Menu"
    cdkOverlayOrigin
    class="box max-h-full flex flex-row content-center justify-start items-center p-2 sm:p-5 rounded hover:bg-white hover:bg-opacity-10 cursor-pointer text-sm uppercase tracking-w25"
    type="button"
  >
    <div aria-hidden="true" class="flex justify-center items-center max-w-none">
      <img
        alt="Burger Menu"
        class="w-6 h-6 flex-none"
        src="assets/hamburger.svg"
      />
    </div>
    <div aria-hidden="true" class="pl-2 hidden sm:block">Menu</div>
  </button>
  <a
    [routerLink]="['/']"
    aria-label="Home"
    class="font-sans max-h-full uppercase font-bold text-xs flex flex-row content-center justify-start items-center"
  >
    <img
      class="h-8 hidden sm:block"
      alt="Playboy"
      src="assets/logo/playboy-logo-white-full.png"
    />
    <img
      class="h-8 block sm:hidden logo-white"
      alt="Playboy"
      src="assets/playboy-large.svg"
    />
    @if (subscriptionType() && subscriptionType() === "all-access") {
      <div
        class="pill bg-golden h-8 rounded-4xl px-3 ml-2 hidden flex-row items-center tracking-normal sm:flex"
      >
        All access
      </div>
    } @else if (subscriptionType() && subscriptionType() === "plus") {
      <div
        class="pill bg-white text-black h-8 rounded-4xl px-3 ml-2 hidden flex-row items-center tracking-normal sm:flex"
      >
        +Plus
      </div>
    }
  </a>
  <div
    class="box max-h-full flex flex-row content-center justify-start items-center"
  >
    @if (!subscriptionType() || subscriptionType() !== "all-access") {
      <a
        href="//playboy.de/bestellen"
        target="_blank"
        class="p-3 md:p-4 rounded text-sm bg-golden"
        >Abo</a
      >
    }
    <a
      [href]="
        accountInfo?.loggedIn ? accountInfo?.profileUrl : accountInfo?.loginUrl
      "
      class="flex justify-center items-center max-w-none pl-3"
      target="_blank"
    >
      <img alt="Nutzer" class="w-6 h-6 flex-none" src="assets/user.svg" />
    </a>
  </div>
  <ng-content></ng-content>
</header>

<!-- This template displays the overlay content and is connected to the button -->
<ng-template
  (detach)="isOpen = false"
  (overlayOutsideClick)="isOpen = false"
  [cdkConnectedOverlayOpen]="isOpen"
  [cdkConnectedOverlayOrigin]="trigger"
  [cdkConnectedOverlayPositions]="overlayPositions"
  [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
  cdkConnectedOverlay
>
  <lib-navbar-mobile
    (close)="isOpen = false"
    [accountInfo]="accountInfo"
    [cdkTrapFocusAutoCapture]="true"
    [external]="external"
    [headerBarHeight]="headerBarHeight"
    [urlsRight]="urlsRight"
    [urls]="urls"
    cdkTrapFocus
  ></lib-navbar-mobile>
</ng-template>
