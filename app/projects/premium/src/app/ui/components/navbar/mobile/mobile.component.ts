import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import {
  Event,
  NavigationEnd,
  Router,
  RouterLink,
  RouterLinkActive,
} from '@angular/router';
import { INavbarUrl } from '@pb/ui';
import { AccountService } from 'projects/premium/src/app/services/account.service';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

import { INavbarUrlExternal } from '../definitions';
import { INavbarAccountInfo } from '../upper/upper.component';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'lib-navbar-mobile',
  templateUrl: './mobile.component.html',
  styleUrls: ['./mobile.component.css'],
  imports: [RouterLink, NgClass, RouterLinkActive, NgTemplateOutlet],
})
export class MobileComponent implements OnD<PERSON>roy {
  @Input() urls: INavbarUrl[];
  @Input() urlsRight: INavbarUrl[];
  @Input() external: INavbarUrlExternal[];
  @Input() accountInfo: INavbarAccountInfo;
  currentYear: number = new Date().getFullYear();

  @Input() headerBarHeight: number;

  @Output() close = new EventEmitter();
  openSubMenu: INavbarUrl | undefined;
  public detailOpen = [];
  $account = this.accService.Account;
  protected readonly environment = environment;
  protected scrollY = 0;
  private readonly sub: Subscription;

  constructor(
    router: Router,
    private readonly accService: AccountService,
  ) {
    this.sub = router.events
      .pipe(filter((r: Event) => r instanceof NavigationEnd))
      .subscribe(() => this.close.emit());
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
  }

  openPrivacyPopup(): void {
    // @ts-ignore
    if (typeof window.__tcfapi === 'function') {
      // @ts-ignore
      window.__tcfapi('showConsentManager', 2, function (result) {});
    }
  }

  protected onScrollContent(event): void {
    this.scrollY = event.currentTarget.scrollTop;
  }

  protected toggleDetail(event: Event, identifier: string, level = 0): void {
    // @ts-ignore
    const isOpen = event.currentTarget.open;

    if (this.detailOpen[level] && this.detailOpen[level] === identifier) {
      this.detailOpen[level] = undefined;
    } else {
      if (isOpen) {
        this.detailOpen[level] = identifier;
      }
    }
  }
}
