<div
  class="flex flex-col w-full"
  [style.height.px]="upperHeaderBarHeight + headerBarHeight"
  [style.transform]="'translate3D(0, ' + -scrollProgressAbs + 'px, 0)'"
>
  <div [style.height.px]="headerBarHeight">
    <div
      class="flex flex-row items-center justify-end"
      [style.height.px]="upperHeaderBarHeight"
    >
      @for (ex of external; track ex; let last = $last) {
        <a
          target="blank"
          [href]="ex.href"
          class="mr-7 font-sans uppercase font-bold text-xs"
          [class.mr-7]="!last"
        >
          {{ ex.title }}
        </a>
      }
    </div>
  </div>

  <div class="flex flex-row h-10" [style.height.px]="upperHeaderBarHeight">
    <div
      class="flex flex-row overflow-hidden h-full whitespace-nowrap justify-start items-center"
    >
      <a [routerLink]="['/']" class="self-end">
        <img
          class="self-end mr-4"
          src="assets/logo/logo-header-half-rabbit.png"
          srcset="
            assets/logo/<EMAIL> 2x,
            assets/logo/<EMAIL> 3x
          "
        />
      </a>
      @for (url of urls; track url) {
        <lib-navbar-desktop-link
          [url]="url"
          direction="left"
        ></lib-navbar-desktop-link>
      }
    </div>

    <div class="flex flex-1"></div>

    <div class="flex flex-row justify-end items-center">
      @for (url of urlsRight; track url) {
        <lib-navbar-desktop-link
          [url]="url"
          direction="right"
        ></lib-navbar-desktop-link>
      }
    </div>
  </div>
</div>

<div class="flex flex-row ml-6 items-center search-wrapper">
  @if ($account | async; as acc) {
    <a
      class="relative flex items-center justify-center w-7 h-7 p-1 mr-5 bg-golden rounded-full pl-0.35"
      [href]="accountInfo?.profileUrl"
    >
      <img style="margin-left: 1px" src="assets/profile.svg" />
    </a>
    <div
      class="bg-gray-700 h-7 w-28 rounded-2xl p-2 cursor-pointer search"
      [routerLink]="['search']"
    >
      <a class="w-full h-full search"
        ><img
          src="assets/search.svg"
          after="Suche"
          class="pl-2 w-full -ml-8 h-full"
      /></a>
    </div>
  } @else {
    <div
      class="bg-gray-700 h-7 w-28 rounded-2xl p-2 cursor-pointer search"
      [routerLink]="['p/search']"
    >
      <a class="w-full h-full search"
        ><img
          src="assets/search.svg"
          after="Suche"
          class="pl-2 w-full -ml-8 h-full"
      /></a>
    </div>
  }
</div>
