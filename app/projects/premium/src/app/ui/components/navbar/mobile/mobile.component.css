:host {
  /* how much is the container scrolled to end (y-axis) */
  --pb-nav-scrollYRatio: 0;

  width: 564px;
  height: auto;
  background: rgba(0, 0, 0, 0.9);
  color: #fff;
  border-radius: 16px;
  max-height: 98vh;
  min-height: 0;
  overflow: hidden;
  backdrop-filter: blur(100px);

  @media (max-width: 640px) {
    width: calc(100vw - 32px);
  }

  .link-container {
    padding-right: 8px;

    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.400") transparent;

    /* only when scrollbar is there */

    &.scroll {
      /* remove mask when scrolled to bottom */
      mask-image: linear-gradient(
        to bottom,
        black calc(100% - 5px * 100 * var(--pb-nav-scrollYRatio)),
        transparent 100%
      );
    }

    a {
      padding: 16px;
      border-radius: 8px;
      letter-spacing: 2.5px;

      &:hover,
      &:focus-visible {
        background: rgba(102, 102, 102, 0.2);
        outline: none;
        text-decoration: underline;
      }
    }
  }

  ul.footer-columns {
    columns: 2;
    column-fill: balance;

    li {
      margin-top: 0;
      @apply mb-2;
    }

    @media (min-width: 640px) {
      columns: 3;
    }
  }

  .link-details {
    border-radius: 8px;
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;

    .link-summary {
      border-radius: 8px;
      padding: 16px;
      letter-spacing: 2.5px;
      outline: none;
      list-style: none;

      &::marker,
      &::-webkit-details-marker {
        display: none;
      }

      &:has(a) {
        padding: 0 16px 0 0;
      }
    }

    &[open="true"],
    &[open] {
      padding-bottom: 8px;

      .chevron {
        transform: rotate(180deg);
      }
    }

    > ul {
      margin-left: 32px;
      margin-right: 8px;
    }

    &:hover, &:focus-visible,
    &:has(summary:hover), &:has(summary:focus-visible), &:has(a:focus-visible)
      /*&:focus-within */ {
      background: rgba(102, 102, 102, 0.2);
      outline: none;
    }
  }
}
