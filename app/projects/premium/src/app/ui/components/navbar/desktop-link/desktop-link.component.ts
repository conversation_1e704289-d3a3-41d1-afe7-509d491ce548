import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  ActivatedRoute,
  Router,
  NavigationEnd,
  RouterLink,
  RouterLinkActive,
} from '@angular/router';
import { AccountService } from 'projects/premium/src/app/services/account.service';
import { Observable } from 'rxjs';
import {
  map,
  distinctUntilChanged,
  tap,
  filter,
  startWith,
} from 'rxjs/operators';

import { INavbarUrl } from '../definitions';
import { AsyncPipe, NgClass } from '@angular/common';

@Component({
  selector: 'lib-navbar-desktop-link',
  template: `
    @if (localUrl.routerLink) {
      <a
        [routerLink]="localUrl.routerLink"
        [ngClass]="{
          'mr-4 lg:mr-2 xl:mr-4': direction !== 'right',
          'ml-4 lg:ml-2 xl:ml-4': direction === 'right',
          'underline hover:no-underline': $activeChild | async,
          'hover:underline': !($activeChild | async),
        }"
        routerLinkActive="underline"
        (mouseenter)="
          localUrl.children?.length
            ? openSubMenu.emit(localUrl)
            : this.close.emit()
        "
        class="font-sans uppercase font-bold text-xs"
      >
        {{ localUrl.title }}
      </a>
    }

    @if (localUrl.href) {
      <a
        [href]="localUrl.href"
        [ngClass]="{
          'mr-4 lg:mr-2 xl:mr-4': direction !== 'right',
          'ml-4 lg:ml-2 xl:ml-4': direction === 'right',
        }"
        [class.underline]="$activeChild | async"
        class="font-sans uppercase font-bold text-xs border-b border-transparent"
      >
        {{ localUrl.title }}
      </a>
    }
  `,
  styleUrls: ['./desktop-link.component.css'],
  imports: [RouterLink, AsyncPipe, NgClass, RouterLinkActive],
})
export class NavbarDesktopLinkComponent implements OnInit {
  localUrl: INavbarUrl;
  @Input() set url(url: INavbarUrl) {
    this.localUrl = url;
    this.$activeChild = this.router.events.pipe(
      filter((v) => v instanceof NavigationEnd),
      startWith({ urlAfterRedirects: window.location.pathname }),
      map((v: NavigationEnd) => v.urlAfterRedirects),
      map(
        (route) =>
          (url.children || [])
            .map((v) =>
              typeof v.routerLink === 'string' ? [v.routerLink] : v.routerLink,
            )
            .map((v) => `/${window.encodeURI(v.join('/'))}`)
            .indexOf(route) >= 0,
      ),
      distinctUntilChanged((a, b) => a === b),
    );
  }
  @Input() direction: 'left' | 'right';

  @Output() openSubMenu = new EventEmitter<INavbarUrl>();
  @Output() close = new EventEmitter();

  $activeChild: Observable<boolean>;
  $account = this.accService.Account;

  constructor(
    private readonly accService: AccountService,
    private readonly router: Router,
  ) {}

  ngOnInit(): void {}
}
