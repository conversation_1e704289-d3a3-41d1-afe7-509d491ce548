import {
  Component,
  ElementRef,
  HostBinding,
  HostListener,
  inject,
  Input,
  ViewChildren,
} from '@angular/core';
import { GetScreen, Screen } from '@pb/ui/utils/screen';

import { INavbarUrl, INavbarUrlExternal } from './definitions';
import { INavbarAccountInfo } from './upper/upper.component';

import { RouterLink } from '@angular/router';
import { MobileComponent } from '@pb/ui/components/navbar/mobile/mobile.component';
import { AccountService } from '../../../services/account.service';
import {
  CdkConnectedOverlay,
  CdkOverlayOrigin,
  ConnectedPosition,
  Overlay,
  ScrollStrategy,
} from '@angular/cdk/overlay';
import { CdkTrapFocus } from '@angular/cdk/a11y';

@Component({
  selector: 'lib-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css'],
  imports: [
    RouterLink,
    MobileComponent,
    CdkConnectedOverlay,
    CdkOverlayOrigin,
    CdkTrapFocus,
  ],
})
export class NavbarComponent {
  isHidden = false;
  @Input() urls: INavbarUrl[] = [];
  @Input() urlsRight: INavbarUrl[] = [];
  @Input() external: INavbarUrlExternal[] = [
    {
      href: '//playboy.de/?utm_source=playboy&utm_medium=website&utm_campaign=navigation',
      title: 'playboy.de',
    },
    {
      href: '//shop.playboy.de/abo?utm_campaign=navigation&utm_medium=website&utm_source=playboy',
      title: 'Shop',
    },
    // {
    //   href: '//weinshop.playboy.de/?utm_source=playboy&utm_medium=website&utm_campaign=navigation',
    //   title: 'wein',
    // },
  ];
  @Input() accountInfo: INavbarAccountInfo;
  @ViewChildren('headerBar') headerBar: ElementRef<HTMLHeadElement>;
  readonly headerBarHeight = GetScreen() < Screen.xlm ? 66 : 80;

  /**
   * Position above trigger button
   */
  protected overlayPositions: ConnectedPosition[] = [
    // desktop (trigger left)
    {
      // padding of navigation overlay
      offsetX: -8,
      offsetY: -8,
      originX: 'start',
      originY: 'top',
      overlayX: 'start',
      overlayY: 'top',
      panelClass: 'navigation-overlay',
    },
    // mobile (trigger right)
    {
      offsetX: 16,
      offsetY: -16,
      originX: 'end',
      originY: 'top',
      overlayX: 'end',
      overlayY: 'top',
      panelClass: 'navigation-overlay--mobile',
    },
  ];
  /**
   * Whether menu is open.
   */
  protected isOpen = false;

  private overlay: Overlay = inject(Overlay);

  /**
   * Scroll strateg of navigation overlay
   */
  scrollStrategy: ScrollStrategy = this.overlay.scrollStrategies.block();

  private readonly accountService = inject(AccountService);
  readonly subscriptionType = this.accountService.subscriptionType;
  private _lastScrollTop = 0;

  @HostBinding('style.transform') get translateYFull(): string {
    return this.isHidden ? 'translateY(-100%)' : 'translateY(0%)';
  }

  @HostListener('window:scroll', [])
  onWindowScroll(): void {
    const currentScroll = window.scrollY || document.documentElement.scrollTop;
    this.isHidden = currentScroll > this._lastScrollTop;

    this._lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
  }
}
