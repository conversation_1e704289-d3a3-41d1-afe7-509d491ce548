import {
  Component,
  ElementRef,
  EventEmitter,
  HostBinding,
  HostListener,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import {
  Event,
  NavigationEnd,
  Router,
  RouterEvent,
  RouterLink,
  RouterLinkActive,
} from '@angular/router';
import { INavbarUrl } from '@pb/ui';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { NgClass, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'lib-navbar-desktop-sub-menu',
  templateUrl: './desktop-sub-menu.component.html',
  styleUrls: ['./desktop-sub-menu.component.css'],
  imports: [RouterLink, NgClass, RouterLinkActive, NgTemplateOutlet],
})
export class NavbarDesktopSubMenuComponent implements OnDestroy {
  @HostBinding('style.paddingTop.px')
  @Input()
  top: number;

  @Input() subMenuOpen: INavbarUrl;

  @Output() close = new EventEmitter();

  private readonly sub: Subscription;

  constructor(
    private ele: ElementRef,
    router: Router,
  ) {
    this.sub = router.events
      .pipe(filter((r: Event) => r instanceof NavigationEnd))
      .subscribe(() => this.close.emit());
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
  }

  @HostListener('mousemove', ['$event'])
  @HostListener('click', ['$event'])
  private content({ type, target }: MouseEvent) {
    if (type === 'click' && target === this.ele.nativeElement) {
      this.close.emit();
    }
  }
}
