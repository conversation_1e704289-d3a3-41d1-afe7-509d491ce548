<ng-template #link let-url="url" let-first="first" let-last="last">
  @if (url.routerLink && !(url.children && url.children.length > 0)) {
    <a
      [routerLink]="url.routerLink"
      [ngClass]="url.color ? 'text-' + url.color : undefined"
      class="font-sans uppercase font-bold border-b border-transparent"
      [class.mt-4]="!first"
      [class.mb-4]="!last"
      (click)="openSubMenu = url"
      routerLinkActive="underline"
      >{{ url.title }}</a
    >
  }
  @if (url.href && !(url.children && url.children.length > 0)) {
    <a
      [href]="url.href"
      class="font-sans uppercase font-bold border-b border-transparent my-4"
      >{{ url.title }}</a
    >
  }
  @if (url.children && url.children.length > 0) {
    <a
      (click)="openSubMenu = url"
      class="font-sans uppercase font-bold border-b border-transparent my-4"
    >
      {{ url.title }}
    </a>
  }
</ng-template>

<div
  class="flex flex-col items-end h-full backdrop-filter backdrop-blur-xl bg-gray-900 bg-opacity-80 justify-start w-1/3 mr-8 overflow-auto"
>
  <img
    src="./assets/close-big.svg"
    class="cursor-pointer"
    (click)="close.emit()"
  />
  <div class="w-3/4 flex flex-col">
    @for (
      url of subMenuOpen.children;
      track url;
      let last = $last;
      let first = $first
    ) {
      <ng-container
        *ngTemplateOutlet="
          link;
          context: { url: url, first: first, last: last }
        "
      ></ng-container>
    }
  </div>
</div>
