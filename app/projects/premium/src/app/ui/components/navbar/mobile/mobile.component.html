<ng-template
  #link
  let-first="first"
  let-last="last"
  let-level="level"
  let-url="url"
>
  <!--  simple item without children-->
  @if (!url.children || (url.children && url.children.length <= 0)) {
    <li>
      @if (url.routerLink) {
        <a
          class="block py-4"
          [routerLink]="url.routerLink"
          [ngClass]="url.color ? 'text-' + url.color : undefined"
          routerLinkActive="underline"
          >{{ url.title }}</a
        >
      } @else if (url.href) {
        <a class="block py-4" [href]="url.href">{{ url.title }}</a>
      }
    </li>
  } @else {
    <!--  item with children  -->
    <details
      class="link-details"
      [open]="detailOpen[level] === url.title"
      (toggle)="toggleDetail($event, url.title, level)"
    >
      <summary class="link-summary">
        <div class="flex flex-row justify-between items-center">
          @if (url.href) {
            <a [href]="url.href">{{ url.title }}</a>
          } @else if (url.routerLink) {
            <a
              [routerLink]="url.routerLink"
              [ngClass]="url.color ? 'text-' + url.color : undefined"
              routerLinkActive="underline"
              >{{ url.title }}</a
            >
          } @else {
            <span class="cursor-default">
              {{ url.title }}
            </span>
          }
          <div class="chevron">
            <svg
              width="20"
              height="11"
              viewBox="0 0 20 11"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M10.3406 10.6101L9.95184 10.9988L0.359375 1.40638L1.6684 0.0973532L9.95219 8.38114L18.3323 0.000976562L19.6414 1.31L10.3409 10.6105L10.3406 10.6101Z"
                fill="white"
              />
            </svg>
          </div>
        </div>
      </summary>

      <ul class="uppercase font-medium tracking-wide">
        @for (
          child of url.children;
          track child;
          let last = $last;
          let first = $first
        ) {
          <ng-container
            *ngTemplateOutlet="
              link;
              context: {
                url: child,
                first: first,
                last: last,
                level: level + 1,
              }
            "
          >
          </ng-container>
        }
      </ul>
    </details>
  }
</ng-template>

<div class="p-2 pb-6 h-full flex flex-col">
  <!--  top row -->
  <div
    class="grid grid-flow-col gap-2 auto-cols-auto justify-between items-baseline top-row mb-3"
  >
    <!--  close button -->
    <button
      (click)="close.emit()"
      aria-label="schließen"
      class="order-2 sm:order-1 focus:outline-2 bg-white text-black rounded grid grid-flow-col gap-2 auto-cols-auto items-center py-1 px-3 sm:pr-6 uppercase text-sm tracking-widest"
    >
      <svg
        aria-hidden="true"
        fill="none"
        height="46"
        viewBox="0 0 40 46"
        width="40"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect
          fill="black"
          height="2"
          transform="rotate(-45 12 29)"
          width="20"
          x="12"
          y="29"
        />
        <rect
          fill="black"
          height="2"
          transform="rotate(45 13 15)"
          width="20"
          x="13"
          y="15"
        />
      </svg>
      <span class="hidden sm:inline"> Close </span>
    </button>

    <!-- search link -->
    <a
      (click)="close.emit()"
      [routerLink]="['search']"
      class="order-1 sm:order-2 focus:outline-2 text-white rounded grid grid-flow-col gap-2 auto-cols-auto items-center py-1 pl-3 pr-6 uppercase text-sm tracking-widest"
    >
      <svg
        aria-hidden="true"
        fill="none"
        height="46"
        viewBox="0 0 44 46"
        width="44"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21 30C25.4183 30 29 26.4183 29 22C29 17.5817 25.4183 14 21 14C16.5817 14 13 17.5817 13 22C13 26.4183 16.5817 30 21 30Z"
          stroke="white"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <path
          d="M30.9984 31.9984L26.6484 27.6484"
          stroke="white"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
      </svg>
      <span> Suche </span>
    </a>
  </div>

  <!-- navigation -->
  <div
    #scrollContainer
    (scroll)="onScrollContent($event)"
    [class.scroll]="
      scrollContainer.scrollHeight !== scrollContainer.clientHeight
    "
    [style.--pb-nav-scrollYRatio]="
      1 -
      (scrollY + scrollContainer.clientHeight) / scrollContainer.scrollHeight
    "
    class="overflow-auto link-container"
  >
    <ul class="uppercase font-medium tracking-wide">
      @for (url of urls; track url; let last = $last; let first = $first) {
        <ng-container
          *ngTemplateOutlet="
            link;
            context: { url: url, first: first, last: last, level: 0 }
          "
        >
        </ng-container>
      }
    </ul>

    <div class="h-px my-6 mx-3 bg-gray-750"></div>
    <!--    golden links -->
    <ul class="uppercase text-golden font-medium tracking-wide">
      @for (url of urlsRight; track url; let last = $last) {
        <li>
          @if (url.routerLink) {
            <a class="block py-4" [routerLink]="url.routerLink">{{
              url.title
            }}</a>
          } @else {
            <a class="block py-4" [href]="url.href">{{ url.title }}</a>
          }
        </li>
      }
    </ul>

    <div class="h-px my-6 mx-3 bg-gray-750"></div>
    <!--   static links  -->
    <ul class="uppercase font-medium tracking-wide">
      @for (url of external; track url; let last = $last; let first = $first) {
        <li>
          <a class="block py-4" [href]="url.href" target="_blank">{{
            url.title
          }}</a>
        </li>
      }
    </ul>
  </div>

  <!--  social media -->
  <div
    class="my-6 flex flex-row flex-wrap gap-2 justify-between mx-6 items-center"
  >
    <ul class="grid grid-flow-col grid- grid-rows-1 justify-start gap-2">
      <li>
        <a href="https://www.instagram.com/playboygermany/" target="_blank">
          <svg
            fill="none"
            height="32"
            viewBox="0 0 32 32"
            width="32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>Instagram</title>
            <path
              d="M10.6638 3.19922C6.54754 3.19922 3.19922 6.55064 3.19922 10.668V21.3346C3.19922 25.4509 6.55064 28.7992 10.668 28.7992H21.3346C25.4509 28.7992 28.7992 25.4478 28.7992 21.3305V10.6638C28.7992 6.54754 25.4478 3.19922 21.3305 3.19922H10.6638ZM23.4659 7.46589C24.0547 7.46589 24.5326 7.94375 24.5326 8.53255C24.5326 9.12135 24.0547 9.59922 23.4659 9.59922C22.8771 9.59922 22.3992 9.12135 22.3992 8.53255C22.3992 7.94375 22.8771 7.46589 23.4659 7.46589ZM15.9992 9.59922C19.5288 9.59922 22.3992 12.4696 22.3992 15.9992C22.3992 19.5288 19.5288 22.3992 15.9992 22.3992C12.4696 22.3992 9.59922 19.5288 9.59922 15.9992C9.59922 12.4696 12.4696 9.59922 15.9992 9.59922ZM15.9992 11.7326C14.8676 11.7326 13.7824 12.1821 12.9822 12.9822C12.1821 13.7824 11.7326 14.8676 11.7326 15.9992C11.7326 17.1308 12.1821 18.2161 12.9822 19.0162C13.7824 19.8164 14.8676 20.2659 15.9992 20.2659C17.1308 20.2659 18.2161 19.8164 19.0162 19.0162C19.8164 18.2161 20.2659 17.1308 20.2659 15.9992C20.2659 14.8676 19.8164 13.7824 19.0162 12.9822C18.2161 12.1821 17.1308 11.7326 15.9992 11.7326Z"
              fill="white"
            />
          </svg>
        </a>
      </li>

      <li>
        <a href="https://www.tiktok.com/@playboygermany" target="_blank">
          <svg
            fill="none"
            height="32"
            viewBox="0 0 32 32"
            width="32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>Tiktok</title>
            <path
              d="M25.599 4.26562H6.39896C5.22029 4.26562 4.26562 5.22029 4.26562 6.39896V25.599C4.26562 26.7776 5.22029 27.7323 6.39896 27.7323H25.599C26.7776 27.7323 27.7323 26.7776 27.7323 25.599V6.39896C27.7323 5.22029 26.7766 4.26562 25.599 4.26562ZM24.2006 14.3712C24.0619 14.384 23.9222 14.3926 23.7814 14.3926C22.1867 14.3926 20.7851 13.5734 19.9691 12.3328C19.9691 15.5851 19.9691 19.2864 19.9691 19.3483C19.9691 22.2123 17.647 24.5334 14.784 24.5334C11.9211 24.5323 9.59896 22.2102 9.59896 19.3472C9.59896 16.4832 11.9211 14.1622 14.784 14.1622C14.8928 14.1622 14.9984 14.1718 15.104 14.1782V16.7339C14.9974 16.7211 14.8939 16.7019 14.784 16.7019C13.3227 16.7019 12.1376 17.887 12.1376 19.3483C12.1376 20.8096 13.3216 21.9947 14.784 21.9947C16.2464 21.9947 17.5371 20.8427 17.5371 19.3814C17.5371 19.3227 17.5627 7.46669 17.5627 7.46669H20.0043C20.2336 9.65016 21.9968 11.3739 24.2006 11.5318V14.3712Z"
              fill="white"
            />
          </svg>
        </a>
      </li>

      <li>
        <a
          href="https://www.youtube.com/user/playboydeutschland"
          target="_blank"
        >
          <svg
            fill="none"
            height="32"
            viewBox="0 0 32 32"
            width="32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>Youtube</title>
            <path
              d="M15.9995 4.26562C11.5344 4.26562 5.73906 5.38438 5.73906 5.38438L5.72448 5.40104C3.69085 5.72628 2.13281 7.47376 2.13281 9.59896V15.999V16.001V22.399V22.401C2.1348 23.416 2.49854 24.397 3.15873 25.168C3.81892 25.9389 4.73231 26.4492 5.7349 26.6073L5.73906 26.6135C5.73906 26.6135 11.5344 27.7344 15.9995 27.7344C20.4645 27.7344 26.2599 26.6135 26.2599 26.6135L26.262 26.6115C27.2657 26.4537 28.1801 25.943 28.8408 25.1711C29.5015 24.3993 29.8651 23.417 29.8661 22.401V22.399V16.001V15.999V9.59896C29.8647 8.58363 29.5011 7.60211 28.8409 6.83075C28.1807 6.0594 27.267 5.54877 26.2641 5.39062L26.2599 5.38438C26.2599 5.38438 20.4645 4.26562 15.9995 4.26562ZM12.7995 11.0906L21.3328 15.999L12.7995 20.9073V11.0906Z"
              fill="white"
            />
          </svg>
        </a>
      </li>

      <li>
        <a href="https://x.com/playboy_d" target="_blank">
          <svg
            fill="none"
            height="32"
            viewBox="0 0 32 32"
            width="32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>X</title>
            <path
              d="M6.39896 4.26562C5.22029 4.26562 4.26562 5.22029 4.26562 6.39896V25.599C4.26562 26.7776 5.22029 27.7323 6.39896 27.7323H25.599C26.7776 27.7323 27.7323 26.7776 27.7323 25.599V6.39896C27.7323 5.22029 26.7776 4.26562 25.599 4.26562H6.39896ZM9.22396 9.59896H14.1427L17.0135 13.7031L20.5656 9.59896H22.1135L17.7094 14.699L23.0969 22.399H18.1781L14.9927 17.8448L11.0594 22.399H9.48646L14.2927 16.8469L9.22396 9.59896ZM11.6031 10.8615L18.8073 21.1302H20.7156L13.5094 10.8615H11.6031Z"
              fill="white"
            />
          </svg>
        </a>
      </li>

      <li>
        <a href="https://www.facebook.com/PlayboyGermany" target="_blank">
          <svg
            fill="none"
            height="32"
            viewBox="0 0 32 32"
            width="32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>facebook</title>
            <path
              d="M15.9992 3.19922C8.93042 3.19922 3.19922 8.93042 3.19922 15.9992C3.19922 22.4163 7.92669 27.7155 14.0856 28.6414V19.3912H10.9187V16.027H14.0856V13.788C14.0856 10.0814 15.8915 8.45469 18.972 8.45469C20.4472 8.45469 21.228 8.56455 21.5971 8.61362V11.5502H19.4958C18.188 11.5502 17.7315 12.7907 17.7315 14.188V16.027H21.564L21.0446 19.3912H17.7315V28.668C23.979 27.8211 28.7992 22.4792 28.7992 15.9992C28.7992 8.93042 23.068 3.19922 15.9992 3.19922Z"
              fill="white"
            />
          </svg>
        </a>
      </li>

      <li>
        <a
          href="https://www.linkedin.com/company/playboydeutschland/mycompany/"
          target="_blank"
        >
          <svg
            fill="none"
            height="32"
            viewBox="0 0 32 32"
            width="32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <title>Linkedin</title>
            <path
              d="M25.599 4.26562H6.39896C5.22029 4.26562 4.26562 5.22029 4.26562 6.39896V25.599C4.26562 26.7776 5.22029 27.7323 6.39896 27.7323H25.599C26.7776 27.7323 27.7323 26.7776 27.7323 25.599V6.39896C27.7323 5.22029 26.7776 4.26562 25.599 4.26562ZM11.6832 23.4656H8.53656V13.3408H11.6832V23.4656ZM10.0779 11.8934C9.06349 11.8934 8.24323 11.071 8.24323 10.0587C8.24323 9.04642 9.06456 8.22509 10.0779 8.22509C11.0891 8.22509 11.9115 9.04749 11.9115 10.0587C11.9115 11.071 11.0891 11.8934 10.0779 11.8934ZM23.4699 23.4656H20.3254V18.5419C20.3254 17.3675 20.304 15.8571 18.6902 15.8571C17.0528 15.8571 16.8011 17.136 16.8011 18.4566V23.4656H13.6566V13.3408H16.6752V14.7243H16.7179C17.1382 13.9286 18.1643 13.0891 19.695 13.0891C22.8811 13.0891 23.4699 15.1862 23.4699 17.9126V23.4656Z"
              fill="white"
            />
          </svg>
        </a>
      </li>
    </ul>

    <span class="uppercase text-sm justify-self-end"
      >© {{ currentYear }} Kouneli Media GmbH</span
    >
  </div>

  <!-- divider -->
  <div class="h-px bg-white my-6 opacity-50"></div>

  <!-- footer -->
  <!--  <ul class="grid grid-flow-col grid-rows-4 text-xs mx-6 gap-x-1 content-between">-->
  <ul class="text-xs mx-6 gap-x-1 footer-columns">
    <li>
      <a routerLink="/pages/imprint">Impressum</a>
    </li>
    <li>
      <a routerLink="/pages/terms">AGB</a>
    </li>
    <li>
      <a routerLink="/pages/privacy">Datenschutz</a>
    </li>

    <li>
      <a href="https://www.playboy.de/newsletter/subscribe" target="_blank"
        >Newsletter</a
      >
    </li>
    <li>
      <a routerLink="/pages/contact" target="_blank">Kontakt</a>
    </li>
    <li>
      <a href="https://www.playboy.de/presse" target="_blank">Presse</a>
    </li>
    <li>
      <button (click)="openPrivacyPopup()">Privatsphäre Verwalten</button>
    </li>
    <li>
      <a
        href="https://bcn.burda.de/marken/national/titel/playboyde/"
        target="_blank"
        >Mediendaten Digital</a
      >
    </li>
    <li>
      <a href="https://bcn.burda.de/datenschutz/" target="_blank"
        >Über unsere Werbung</a
      >
    </li>
    <li>
      <a
        href="https://www.playboy.de/werde-jetzt-mitglied-der-playboy-familie"
        target="_blank"
        >Casting</a
      >
    </li>
    <li>
      <a href="https://www.kouneli.de/karriere" target="_blank">Karriere</a>
    </li>
  </ul>
</div>
