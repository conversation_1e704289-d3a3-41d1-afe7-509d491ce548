:host {
  @apply z-50 fixed top-0 left-0 w-full sm:h-20 transition-transform duration-300 ease-in-out backdrop-filter backdrop-blur-xl bg-black bg-opacity-60;

  font-size: 14px;
  letter-spacing: 0.16em;
  @apply font-inter font-normal uppercase;
}

@screen lg {
  :host {
    font-size: 12px;
  }
}
@screen xl {
  :host {
    font-size: 14px;
  }
}

li,
ul,
header {
  @apply flex flex-row items-center;
}

li {
  @apply mr-4;
}
@screen lg {
  li {
    @apply mr-6;
  }
}

li a {
  @apply text-white no-underline;
  text-decoration: none;
}

header {
  max-width: 1920px;
  padding-top: 10px;
  padding-bottom: 10px;
  @apply flex justify-between px-3 md:py-3 md:px-6 flex-row-reverse sm:flex-row m-auto;
}

.mobile-menu {
  padding-top: 60px;
  pointer-events: all;
}

.logo-white {
  filter: invert(1) brightness(100%);
}
