import { Component, HostBinding, Input, OnInit } from '@angular/core';
import { AccountService } from 'projects/premium/src/app/services/account.service';

import { INavbarUrl, INavbarUrlExternal } from '../definitions';
import { RouterLink } from '@angular/router';
import { NavbarDesktopLinkComponent } from '@pb/ui/components/navbar/desktop-link/desktop-link.component';
import { AsyncPipe } from '@angular/common';

export interface INavbarAccountInfo {
  loggedIn: boolean;
  profileUrl: string;
  loginUrl: string;
}

@Component({
  selector: 'lib-navbar-upper',
  templateUrl: './upper.component.html',
  styleUrls: ['./upper.component.css'],
  imports: [RouterLink, NavbarDesktopLinkComponent, AsyncPipe],
})
export class NavbarUpperComponent implements OnInit {
  @HostBinding('style.height.px')
  @Input()
  upperHeaderBarHeight: number;
  @Input() headerBarHeight: number;
  @Input() scrollProgressAbs: number;

  @Input() urls: INavbarUrl[];
  @Input() urlsRight: INavbarUrl[];
  @Input() external: INavbarUrlExternal[];
  @Input() accountInfo: INavbarAccountInfo;
  $account = this.accService.Account;

  constructor(private readonly accService: AccountService) {}

  ngOnInit(): void {}
}
