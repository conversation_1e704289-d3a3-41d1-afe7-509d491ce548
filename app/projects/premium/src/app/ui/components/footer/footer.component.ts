import { Subscription } from 'rxjs';
import { Component, HostBinding, OnDestroy } from '@angular/core';
import {
  ActivatedRoute,
  Event,
  NavigationEnd,
  Router,
  RouterLink,
} from '@angular/router';
import { filter } from 'rxjs/operators';
import { NgClass } from '@angular/common';

@Component({
  selector: 'lib-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.css'],
  imports: [NgClass, RouterLink],
})
export class FooterComponent implements OnDestroy {
  currentYear: number = new Date().getFullYear();
  isCollapsed = true;

  @HostBinding('class.pt-16')
  hasSpacing = true;

  private sub: Subscription;
  private subSpace: Subscription;

  constructor(router: Router, route: ActivatedRoute) {
    this.sub = router.events
      .pipe(filter((r: Event) => r instanceof NavigationEnd))
      .subscribe(() => (this.isCollapsed = true));
    this.subSpace = route.data.subscribe(
      (r) => (this.hasSpacing = r.disableFooterSpacing),
    );
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
    this.subSpace?.unsubscribe();
  }

  getLinkType(
    data: { page: string; label: string } | { icon: string; href: string }[],
  ): 'group' | 'link' {
    if (Array.isArray(data)) {
      return 'group';
    }
    return 'link';
  }

  openPrivacyPopup(): void {
    // @ts-ignore
    if (typeof window.__tcfapi === 'function') {
      // @ts-ignore
      window.__tcfapi('showConsentManager', 2, function (result) {});
    }
  }

  toggleFooter() {
    this.isCollapsed = !this.isCollapsed;
  }
}
