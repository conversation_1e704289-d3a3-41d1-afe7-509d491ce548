<ng-template #link let-data="data">
  <a [routerLink]="['/cms', data.page]">{{ data.label }}</a>
</ng-template>
<ng-template #group let-data="data">
  @for (item of data; track item) {
    <a [href]="item.href" class="mr-4">
      <img [src]="'assets/footer/' + item.icon" />
    </a>
  }
</ng-template>

<footer
  [ngClass]="{ 'lg:pt-0 lg:pb-0 lg:max-h-10': isCollapsed }"
  class="bg-black w-full text-xs font-stag uppercase lg:py-12 overflow-hidden max-h-96 transition-all lg:fixed z-30 bottom-0"
>
  <img
    src="assets/arrow-down-white.svg"
    class="hidden lg:block absolute top-1 left-1/2 cursor-pointer p-3 transition-transform transform -translate-x-1/2"
    (click)="toggleFooter()"
    [ngClass]="{ 'rotate-180': isCollapsed }"
  />
  <div class="container flex flex-row flex-wrap lg:flex-no-wrap w-full">
    <div
      class="flex w-full lg:w-1/5 mb-4 sm:mb-2 lg:justify-center lg:items-center"
    >
      <div class="w-28 lg:w-32">
        <img
          class="transition-transform -translate-y-0 lg:-translate-y-14"
          [ngClass]="{ transform: isCollapsed }"
          src="assets/logo/playboy-logo-white-full.png"
        />
      </div>
    </div>
    <ul
      [ngClass]="{ 'lg:opacity-0 lg:pointer-events-none': isCollapsed }"
      class="flex flex-col mt-4 md:mt-2 w-full md:w-1/2 lg:w-1/5"
    >
      <li
        class="flex flex-row justify-start items-center w-full md:w-1/2 border-l-4 border-golden h-8 text-white pl-4 m-0"
      >
        <a target="_blank" href="https://www.playboy.de/newsletter/subscribe"
          >Newsletter</a
        >
      </li>
      <li
        class="flex flex-row justify-start items-center w-full border-l-4 border-golden h-8 text-white pl-4 m-0"
      >
        <a routerLink="/pages/imprint">IMPRESSUM</a>
      </li>
    </ul>

    <ul
      [ngClass]="{ 'lg:opacity-0 lg:pointer-events-none': isCollapsed }"
      class="flex flex-col w-full md:w-1/2 md:w-1/2 lg:w-1/5 mt-4 lg:mt-0 order-3 lg:order-2"
    >
      <li
        class="flex flex-row justify-start items-center w-full border-l-4 border-golden h-8 text-white pl-4 m-0"
      >
        <a routerLink="/pages/contact">KONTAKT</a>
      </li>
      <li
        [ngClass]="{ 'lg:opacity-0 lg:pointer-events-none': isCollapsed }"
        class="flex flex-row justify-start items-center w-full border-l-4 border-golden h-8 text-white pl-4 m-0"
      >
        <a
          target="_blank"
          href="https://shop.playboy.de/abo?utm_campaign=footer&utm_medium=website&utm_source=playboy"
          >SHOP</a
        >
      </li>
    </ul>

    <ul
      class="flex flex-col w-full md:w-1/2 md:w-1/2 lg:w-1/5 mt-4 lg:mt-0 order-3"
    >
      <li
        [ngClass]="{ 'lg:opacity-0 lg:pointer-events-none': isCollapsed }"
        class="flex flex-row justify-start items-center w-full border-l-4 border-golden h-8 text-white pl-4 m-0"
      >
        <a routerLink="/pages/privacy">Datenschutz</a>
      </li>
      <li
        [ngClass]="{ 'lg:opacity-0 lg:pointer-events-none': isCollapsed }"
        class="flex flex-row justify-start items-center w-full border-l-4 border-golden h-8 text-white pl-4 m-0 cursor-pointer"
      >
        <a id="privacyEdit" (click)="openPrivacyPopup()"
          >PRIVATSPHÄRE VERWALTEN</a
        >
      </li>
      <li
        [ngClass]="{ 'lg:opacity-0 lg:pointer-events-none': isCollapsed }"
        class="flex flex-row justify-start items-center w-full border-l-4 border-golden h-8 text-white pl-4 m-0"
      >
        <a routerLink="/pages/terms">AGB</a>
      </li>
      <li
        [ngClass]="{
          'border-golden border-l-4': !isCollapsed,
          'lg:transform -translate-y-27': isCollapsed,
        }"
        class="flex flex-row justify-start items-center w-full h-8 text-white pl-4 mt-4 sm:m-0 transition-transform"
      >
        <a
          class="mr-4"
          href="https://www.instagram.com/playboygermany/"
          target="_blank"
          ><img src="assets/footer/instagram.svg"
        /></a>
        <a
          class="mr-4"
          href="https://www.facebook.com/PlayboyGermany"
          target="_blank"
          ><img src="assets/footer/facebook.svg"
        /></a>
        <a
          class="mr-4"
          href="https://www.youtube.com/user/playboydeutschland"
          target="_blank"
          ><img src="assets/footer/youtube.svg"
        /></a>
        <a class="mr-4" href="https://twitter.com/playboy_d" target="_blank"
          ><img src="assets/footer/twitter.svg"
        /></a>
      </li>
    </ul>

    <p
      class="order-last flex self-end w-full lg:w-1/5 h-8 flex-row justify-start items-center text-white font-hairline pl-4"
    >
      <span
        class="opacity-50 transition-transform"
        [ngClass]="{
          'lg:transform -translate-y-27': isCollapsed,
        }"
      >
        © {{ currentYear }} Kouneli Media GmbH
      </span>
    </p>
  </div>
</footer>
