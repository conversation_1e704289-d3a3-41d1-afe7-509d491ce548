import {
  animate,
  keyframes,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { NgClass } from '@angular/common';
import {
  AfterViewInit,
  Component,
  ElementRef,
  HostBinding,
  HostListener,
  Input,
  OnDestroy,
  ViewChild,
} from '@angular/core';
import { CdnPipe } from '@pb/ui/pipes/cdn.pipe';
import { ElResize, UnObserve } from '@pb/ui/utils/resizeObserver';
import { Subscription } from 'rxjs';
import { debounceTime, startWith } from 'rxjs/operators';

@Component({
  selector: 'app-preload-image',
  template: `
    @if (!_image && scrolledIn) {
      <img
        class="w-full inline filter "
        [src]="
          _src
            | cdn
              : imageWidth || _dynamicImageWidth
              : imageHeight || _dynamicImageHeight
        "
        [attr.alt]="alt"
        [@fadeIn]="loaded"
        [style.aspectRatio]="imageRatio"
        [ngClass]="{
          'absolute left-0 h-full top-0 z-0': fill || contain,
          'transition-transform duration-500 transform group-hover:scale-110':
            groupHoverEffect,
        }"
        [class.object-cover]="fill"
        [class.object-contain]="contain"
        (load)="loaded = true"
        (loadstart)="loaded = false"
        [style.objectPosition]="
          focalPoint ? focalPoint.x + '% ' + focalPoint.y + '%' : undefined
        "
      />
    }
    @if (scrolledIn && _lowResSrc && !loaded) {
      <img
        class="w-full filter blur-xl relative z-10"
        [@fadeIn]="previewLoaded && !loaded"
        [style.aspectRatio]="imageRatio"
        (load)="previewLoaded = true"
        (loadstart)="previewLoaded = false"
        [ngClass]="{
          'absolute left-0 h-full top-0': fill || contain,
          relative: !(fill || contain),
        }"
        [class.object-cover]="fill"
        [class.object-contain]="contain"
        [style.objectPosition]="
          focalPoint ? focalPoint.x + '% ' + focalPoint.y + '%' : undefined
        "
        [src]="_lowResSrc | cdn"
        [attr.alt]="alt"
      />
    }
    @if (!previewLoaded && !loaded) {
      <div
        class=" p-8 self-center w-full h-full flex flex-col justify-center items-center"
      >
        <img src="assets/bunnyanimation.gif" class="w-24 m-auto" />
      </div>
    }
  `,
  styleUrls: ['./preload-image.component.css'],
  animations: [
    trigger('fadeIn', [
      state('false', style({ opacity: 0 })),
      state('true', style({ opacity: 1 })),
      transition('* => true', [
        animate(
          '50ms ease-out',
          keyframes([style({ opacity: 0 }), style({ opacity: 1 })]),
        ),
      ]),
      transition('true => *', [
        animate(
          '100ms ease-out',
          keyframes([style({ opacity: 1 }), style({ opacity: 0 })]),
        ),
      ]),
    ]),
  ],
  imports: [CdnPipe, NgClass],
})
export class PreloadImageComponent implements OnDestroy, AfterViewInit {
  @Input() scrolledIn = false;
  private scrollSub: Subscription;

  @HostListener('window:scroll')
  private scroll() {
    if (!this.element.nativeElement.getBoundingClientRect || !window) return;
    this.scrolledIn = true;
  }

  @HostBinding('style.aspectRatio')
  @Input()
  imageRatio?: number;

  _dynamicImageWidth?: number;
  @Input() imageWidth?: number;

  _dynamicImageHeight?: number;
  @Input() imageHeight?: number;

  _image: HTMLImageElement;
  @Input() set image(image: HTMLImageElement) {
    if (this._image) {
      this.element.nativeElement.removeChild(this._image);
    }
    this.element.nativeElement.appendChild(image);
    // No clue why i can't set absolute it through ::ng-deep in css without overwriting the parents css
    image.className = 'absolute unmanaged-image-element';
    this.loaded = this.loaded && this._image === image;
    this.scrolledIn = this._image === image && this.loaded;
    this.previewLoaded = this.loaded;
    this._image !== image && this.scroll();
    this._image = image;
  }

  _src: string;
  @Input() set src(src: string) {
    this.loaded = this.loaded && this._src === src;
    this.scrolledIn = this._src === src && this.loaded;
    this.previewLoaded = this.loaded;
    this._src !== src && this.scroll();
    this._src = src;
  }

  _lowResSrc?: string;
  @Input() set lowResSrc(lowResSrc: string | undefined) {
    this.loaded = this.loaded && this._lowResSrc === lowResSrc;
    this.previewLoaded = this.loaded;
    this._lowResSrc !== lowResSrc && this.scroll();
    this._lowResSrc = lowResSrc;
  }

  @Input() alt?: string;

  @Input() focalPoint?: {
    x: number;
    y: number;
  };

  @ViewChild('image') imageElement: ElementRef<HTMLImageElement>;

  @HostBinding('class.preload-group-hover-effect') @Input() groupHoverEffect =
    false;

  loaded = false;
  previewLoaded = false;

  @HostBinding('class.preload-fill') @Input() fill = false;
  @HostBinding('class.preload-contain') @Input() contain = false;

  private sizeSub: Subscription;
  constructor(private element: ElementRef<HTMLElement>) {
    this.sizeSub = ElResize(element.nativeElement)
      .pipe(startWith(), debounceTime(100))
      .subscribe(() => {
        const { width, height } =
          this.element.nativeElement.getBoundingClientRect();
        const ratio = 1; // Math.min(window.devicePixelRatio, 2)
        if (!this.imageWidth) {
          this.imageWidth = Math.ceil(width * ratio);
        }
        if (!this.imageHeight) {
          this.imageHeight = Math.ceil(height * ratio);
        }
      });
  }

  ngOnDestroy(): void {
    this.scrollSub?.unsubscribe();
    clearTimeout(this.timeout);
    this.sizeSub?.unsubscribe();
    UnObserve(this.element.nativeElement);
  }

  private timeout = undefined;
  ngAfterViewInit() {
    if (this.timeout === undefined) {
      return;
    }
    clearTimeout(this.timeout);
    this.timeout = setTimeout(() => this.scroll(), 1000);
    this.scroll();
  }
}
