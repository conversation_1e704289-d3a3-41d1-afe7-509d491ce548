:host:not(.absolute) {
  @apply relative;
}

:host {
  @apply overflow-hidden;
  img {
    @apply pointer-events-none object-cover;
  }
}

:host.preload-contain ::ng-deep .unmanaged-image-element,
:host.preload-fill ::ng-deep .unmanaged-image-element {
  @apply left-0 h-full top-0 z-0;
}

:host.preload-contain ::ng-deep .unmanaged-image-element {
  @apply object-contain;
}

:host.preload-fill ::ng-deep .unmanaged-image-element {
  @apply object-cover;
}

:host.preload-group-hover-effect ::ng-deep .unmanaged-image-element {
  @apply transition-transform duration-500 transform;
}
/* ::ng-deep.group:hover
  :host.preload-group-hover-effect
  ::ng-deep
  .unmanaged-image-element {
  @apply scale-110;
} */

:host ::ng-deep .unmanaged-image-element {
  @apply w-full inline filter;
}

:host-context(app-pagination-grid) {
  @apply absolute top-0 bottom-0 m-auto w-full h-full;
}

:host-context(app-pagination-grid) img {
  @apply min-w-full min-h-full absolute top-0 bottom-0 m-auto object-cover object-center;
}

:host-context(.gallery-list) {
  @apply absolute top-0 bottom-0 m-auto w-full h-full;
}

:host-context(.gallery-list) img {
  @apply min-w-full min-h-full absolute top-0 bottom-0 m-auto object-cover object-center;
}

:host-context(.gallery-hero) {
  @apply h-full;
}

:host-context(.gallery-hero) img {
  @apply object-cover object-center h-full w-full;
}

:host-context(swiper-slide app-gallery-card) {
  @apply h-full w-full;
}

:host-context(swiper-slide app-gallery-card) img {
  @apply object-cover object-center h-full w-full;
}
