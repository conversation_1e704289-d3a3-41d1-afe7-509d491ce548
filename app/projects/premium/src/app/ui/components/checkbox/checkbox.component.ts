import {
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnInit,
  Output,
} from '@angular/core';

@Component({
  selector: 'lib-checkbox',
  templateUrl: './checkbox.component.html',
  styleUrls: ['./checkbox.component.css'],
  standalone: true,
})
export class CheckboxComponent implements OnInit {
  @Input() checked = false;
  @Output() check = new EventEmitter<boolean>();

  @HostListener('click')
  onClick() {
    this.check.emit(!this.checked);
  }

  constructor() {}

  ngOnInit(): void {}
}
