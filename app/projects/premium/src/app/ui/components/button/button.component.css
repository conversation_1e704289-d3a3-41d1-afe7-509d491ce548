:host {
  @apply border cursor-pointer uppercase py-2 px-3 sm:py-3 sm:px-4 text-xs text-center transition-colors tracking-widest;
}

:host.disabled {
  @apply pointer-events-none;
}

:host.type--outline {
  @apply uppercase font-inter font-medium border-white text-white;
  letter-spacing: 2.5px;
}
:host.type--outline:hover {
  @apply bg-white;
  @apply bg-opacity-25 !important;
}

:host.type--outline.disabled {
  @apply opacity-50;
}

:host:not(.type--outline) {
  @apply shadow-md font-bold text-white border-transparent;
}

:host:not(.type--outline):hover {
  @apply border-golden;
}

:host:not(.disabled):not(.type--outline) {
  @apply bg-golden;
}

:host.disabled:not(.type--outline) {
  @apply bg-gray-300;
}

@screen md {
  :host {
    @apply text-sm;
  }
}
