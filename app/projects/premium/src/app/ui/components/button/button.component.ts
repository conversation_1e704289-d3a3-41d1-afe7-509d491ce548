import { Component, OnInit, Input, HostBinding } from '@angular/core';

@Component({
  selector: 'lib-button',
  template: `
    <ng-content></ng-content>
    @if (outline) {
      &nbsp;&nbsp;>&nbsp;&nbsp;
    }
  `,
  styleUrls: ['./button.component.css'],
  standalone: true,
})
export class ButtonComponent implements OnInit {
  @HostBinding('class.type--outline')
  @Input()
  outline = false;

  @HostBinding('class.disabled') @Input() disabled = false;

  constructor() {}

  ngOnInit(): void {}
}
