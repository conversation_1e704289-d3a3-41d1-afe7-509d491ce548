import { Subscription } from 'rxjs';
import {
  ChangeDetectionStrategy,
  Component,
  HostBinding,
  OnDestroy,
} from '@angular/core';

import { LoadingIndicatorService } from '../../services/loading-indicator.service';

@Component({
  selector: 'lib-loading-indicator',
  template: `
    <img src="assets/bunnyanimation.gif" alt="Loading..." class="w-20" />
  `,
  styleUrls: ['./loading-indicator.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
})
export class LoadingIndicatorComponent implements OnDestroy {
  private sub?: Subscription;

  constructor(loading: LoadingIndicatorService) {
    this.sub = loading.Display.subscribe((display) => (this.show = display));
  }
  @HostBinding('class.show') private show: boolean;

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
  }
}
