import { BehaviorSubject } from 'rxjs';
import { Theme } from '../constants/theme';
import { Inject, Injectable } from '@angular/core';
import { UI_THEME } from '../constants';
import { distinctUntilChanged } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class ThemeService {
  private behav: BehaviorSubject<Theme>;

  public get Theme() {
    return this.behav.pipe(distinctUntilChanged((a, b) => a === b));
  }

  constructor(@Inject(UI_THEME) theme = Theme.Light) {
    this.behav = new BehaviorSubject<Theme>(theme);
    this.Theme.subscribe((theme) => {
      try {
        window.document.body.classList[theme === Theme.Dark ? 'add' : 'remove'](
          'dark',
        );
      } catch {}
    });
  }
}
