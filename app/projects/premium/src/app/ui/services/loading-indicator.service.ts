import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, distinctUntilChanged } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class LoadingIndicatorService {
  // Each loading instance has an id assigned and is being tracked on whether its loading.
  private displayedLoaders = new BehaviorSubject<{ [taskId: number]: boolean }>(
    {},
  );
  private show = new BehaviorSubject(false);

  public get Display(): Observable<boolean> {
    return this.show.pipe(distinctUntilChanged((a, b) => a === b));
  }

  constructor() {
    let cacher = null;
    this.displayedLoaders
      .pipe(map((v) => Object.values(v).filter((e) => !!e).length > 0))
      .subscribe((show) => {
        // to smooth over tiny delays
        // clearTimeout(cacher);
        // if (!show) {
        //   cacher = setTimeout(() => this.show.next(false), 100);
        // } else {
        // }
        this.show.next(show);
      });
  }

  public getNewTaskId(bootstrapValue = false): number {
    let HighestId = parseInt(
      (Object.keys(this.displayedLoaders.getValue()) as string[])
        .sort()
        .reverse()[0],
      10,
    );
    if (isNaN(HighestId)) {
      HighestId = 0;
    }
    this.displayedLoaders.next({
      ...this.displayedLoaders.getValue(),
      [HighestId]: bootstrapValue,
    });
    return HighestId + 1;
  }

  public StartTask(taskId?: number): number {
    if (typeof taskId !== 'number') {
      taskId = this.getNewTaskId(true);
    } else {
      this.displayedLoaders.next({
        ...this.displayedLoaders.getValue(),
        [taskId]: true,
      });
    }

    // if loading is not ended in 5 seconds, end it as a fallback
    setTimeout(() => {
      if (this.displayedLoaders.getValue()[taskId]) {
        this.displayedLoaders.next({
          ...this.displayedLoaders.getValue(),
          [taskId]: false,
        });
      }
    }, 5000);

    return taskId;
  }

  public EndTask(taskId: number): void {
    this.displayedLoaders.next({
      ...this.displayedLoaders.getValue(),
      [taskId]: false,
    });
  }
}
