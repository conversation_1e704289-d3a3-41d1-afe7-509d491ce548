import { distinctJ<PERSON><PERSON> } from './server';
import { Observable, of, Subject, BehaviorSubject } from 'rxjs';
import { fromEvent } from 'rxjs';
import {
  debounceTime,
  map,
  startWith,
  distinctUntilChanged,
} from 'rxjs/operators';

export enum Screen {
  sm,
  md,
  lg,
  xl,
  xlm,
  xxl,
}

export const NameScreenMapping = {
  sm: Screen.sm,
  md: Screen.md,
  lg: Screen.lg,
  xl: Screen.xl,
  xlm: Screen.xlm,
  xxl: Screen.xxl,
};

export type ScreenName = keyof typeof NameScreenMapping;

const Mapping: { [screen in Screen]: number } = {
  [Screen.sm]: 640,
  [Screen.md]: 768,
  [Screen.lg]: 1024,
  [Screen.xl]: 1280,
  [Screen.xlm]: 1450,
  [Screen.xxl]: 2000,
};

function getSizeOfScreen(size: number): Screen | undefined {
  for (const s in Object.keys(Mapping) as any[] as Screen[]) {
    if (size < Mapping[s]) {
      return parseInt(s, 10) as Screen;
    }
  }
  return Object.keys(Mapping).length;
}

let _ScreenSubject = new BehaviorSubject<Screen | undefined>(GetScreen());

try {
  fromEvent(window, 'resize')
    .pipe(
      debounceTime(100),
      startWith(window.innerWidth),
      map((v) => window.innerWidth),
      distinctJSON(),
      map((v) => getSizeOfScreen(window.innerWidth)),
      distinctJSON(),
    )
    .subscribe((v) => _ScreenSubject.next(v));
} catch {}

export function GetScreen(): Screen {
  try {
    return getSizeOfScreen(window.innerWidth);
  } catch {
    return Screen.sm;
  }
}

export function IsMobile(): boolean {
  return GetScreen() <= Screen.md;
}

export const ScreenObservable: Observable<Screen | undefined> =
  _ScreenSubject.pipe(distinctUntilChanged((a, b) => a === b));

export const $GridColumns = ScreenObservable.pipe(
  map((v) => {
    if (v > Screen.xxl) {
      return 6;
    }
    if (v > Screen.lg) {
      return 4;
    }
    if (v > Screen.md) {
      return 3;
    }
    return 1;
  }),
  distinctUntilChanged((a, b) => a === b),
);

export const $ShownPerScreen = $GridColumns.pipe(
  map((columns) => columns * GRID_ROWS),
);

export const GRID_ROWS = 6;
