import { distinctUntilChanged, tap } from 'rxjs/operators';
import { BehaviorSubject } from 'rxjs';

export * from './server';

export function getVersion() {
  return '0.0.1';
}

function normalizeName(name: string) {
  return `playboy-shop-${getVersion()}-${name}`;
}

export class CacheBehaviour<
  T extends { [key: string]: any },
  Raw extends { [key: string]: any } = T,
> extends BehaviorSubject<T> {
  public static getData<T extends any>(
    name: string,
    fromJSON: (data: {}) => T,
  ): T | undefined {
    if (!localStorage.getItem(normalizeName(name))) {
      return undefined;
    }
    return fromJSON(JSON.parse(localStorage.getItem(normalizeName(name))));
  }

  public static writeData(name: string, data: {}) {
    localStorage.setItem(normalizeName(name), JSON.stringify(data));
  }

  constructor(
    private readonly cacheName: string,
    initial: T = null,
    private readonly toJSON: (data: T) => Raw = (data) => data as any as Raw,
    fromJSON: (data: Raw) => T = (data) => data as any as T,
    private readonly equalsFN?: (a: T, b: T) => boolean,
  ) {
    super(CacheBehaviour.getData(cacheName, fromJSON) || initial);
    this.DistinctObservable.subscribe((v) => this.save());
  }

  public get DistinctObservable() {
    return this.pipe(distinctUntilChanged((a, b) => this.equals(a, b)));
  }

  public equals(a: T, b: T) {
    if (!this.equalsFN) {
      return JSON.stringify(this.toJSON(a)) === JSON.stringify(this.toJSON(b));
    }
    return this.equalsFN(a, b);
  }

  public save() {
    CacheBehaviour.writeData(this.cacheName, this.toJSON(this.getValue()));
  }
}
