import { Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

const _s = new Subject<ResizeObserverEntry[]>();

//const resizeObserver = new ResizeObserver(el => _s.next(el));

export function UnObserve(element: HTMLElement) {
  //  resizeObserver.unobserve(element);
}

export function ElResize(
  element: HTMLElement,
): Observable<ResizeObserverEntry> {
  //  resizeObserver.observe(element);
  return _s.pipe(
    map((v) => v.find((e) => e.target === element)),
    filter((v) => !!v),
  );
}
