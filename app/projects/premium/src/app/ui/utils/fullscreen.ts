// export function openFullScreen(element?: any) {
//   if (!element) { throw new Error("Couldn't open fullscreen since no element was given!"); }

//   var cancelFullScreen = element.exitFullscreen || element.mozCancelFullScreen || element.webkitExitFullscreen || element.msExitFullscreen;

//   if (!doc.fullscreenElement && !doc.mozFullScreenElement && !doc.webkitFullscreenElement && !doc.msFullscreenElement) {
//     window.requestFullScreen.call(docEl);
//   }
//   else {
//     cancelFullScreen.call(doc);
//   }
// }

// export function closeFullScreen(element?: HTMLElement) {
//   if (!element) { throw new Error("Couldn't open fullscreen since no element was given!"); }

//   var requestFullScreen = docEl.requestFullscreen || docEl.mozRequestFullScreen || docEl.webkitRequestFullScreen || docEl.msRequestFullscreen;
//   var cancelFullScreen = doc.exitFullscreen || doc.mozCancelFullScreen || doc.webkitExitFullscreen || doc.msExitFullscreen;

//   if (!doc.fullscreenElement && !doc.mozFullScreenElement && !doc.webkitFullscreenElement && !doc.msFullscreenElement) {
//     requestFullScreen.call(docEl);
//   }
//   else {
//     cancelFullScreen.call(doc);
//   }
// }
