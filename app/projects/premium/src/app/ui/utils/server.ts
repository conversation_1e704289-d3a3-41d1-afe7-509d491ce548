import { MonoTypeOperatorFunction } from 'rxjs';
import { distinctUntilChanged } from 'rxjs/operators';

export type Query = [string, string | number | boolean][];

export function GetUrl(base: string, url: string, query?: Query): string {
  const u = [base, url].join('/');
  if (!query || query.length === 0) {
    return u;
  }
  return [
    u,
    query.map(([key, value]) => [key, value].join('=')).join('&'),
  ].join('?');
}

export function eq(a?: any, b?: any) {
  return JSON.stringify(a) === JSON.stringify(b);
}

export function distinctJSON<T extends any>(): MonoTypeOperatorFunction<T> {
  return distinctUntilChanged<T>(eq);
}
