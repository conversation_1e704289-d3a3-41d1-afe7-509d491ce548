import { timer, Observable } from 'rxjs';
import { scan, tap, switchMapTo, first } from 'rxjs/operators';

function checkAttempts(maxAttempts: number) {
  return (attempts: number) => {
    if (attempts > maxAttempts) {
      throw new Error('Error: max attempts');
    }
  };
}

export function pollUntil<T>(
  pollInterval: number,
  maxAttempts: number,
  responsePredicate: (res: any) => boolean,
) {
  return (source$: Observable<T>) =>
    timer(0, pollInterval).pipe(
      scan((attempts) => ++attempts, 0),
      tap(checkAttempts(maxAttempts)),
      switchMapTo(source$),
      first(responsePredicate),
    );
}
