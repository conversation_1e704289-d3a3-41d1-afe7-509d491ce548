import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';

export interface CouponCard {
  month: number;
  new_p: number;
  old_price: number;
  slug: string;
  coupon: string;
}
export interface CouponContent {
  field_slug: string;
  coupon: string;
  globalSlug: string;
  title: string;
  teaser: string;
  notice: string;
  heroImage: string;
  heroImageMobile: string;
  logo: string;
  buttonSubmit: string;
  button: string;
  columns: string;
  adTitle: string;
  adSubtitle: string;
  adImage: string;
  cards: CouponCard[];
}

@Injectable({
  providedIn: 'root',
})
export class CouponService {
  constructor(public http: HttpClient) {}

  getPageContents(coupon: string) {
    return this.http.get<[CouponContent]>(
      environment.serverUrl + '/api/v1/coupon/' + coupon,
    );
  }
}
