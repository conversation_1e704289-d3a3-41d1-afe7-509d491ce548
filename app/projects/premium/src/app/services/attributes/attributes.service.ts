import { Observable, of } from 'rxjs';
import { Apollo } from 'apollo-angular';

import { Injectable } from '@angular/core';

interface IAttribute {
  value: string;
  title: string;
}
interface IAPIResult {
  pbTaxonomy: { results: { entityLabel: string; entityId: string }[] };
}

@Injectable({ providedIn: 'root' })
export class AttributesService {
  // private colorsSubject = new CachedBehav<IAttribute[]>('colors');
  // private eyeColorsSubject = new CachedBehav<IAttribute[]>('eyeColors');
  // private preferencesSubject = new CachedBehav<IAttribute[]>('preferences');

  $colors: Observable<IAttribute[]> = of([
    { title: 'Blond', value: '2348' },
    { title: 'Braun', value: '10386' },
    { title: 'Dunkelblond', value: '3292' },
    { title: 'Rot', value: '10385' },
    { title: 'Rotbraun', value: '3288' },
    { title: '<PERSON><PERSON><PERSON>', value: '2347' },
  ]);
  $eyeColors: Observable<IAttribute[]> = of([
    { title: 'Blau', value: '13047' },
    { title: 'Braun', value: '13048' },
    { title: 'Grau', value: '13049' },
    { title: 'Grün', value: '13050' },
  ]);
  $preferences: Observable<IAttribute[]> = of([
    { title: 'Curvy', value: '12119' },
    { title: 'Dessous', value: '12121' },
    { title: 'Gepierct', value: '13060' },
    { title: 'High Heels', value: '13064' },
    { title: 'Homeshooting', value: '12123' },
    { title: 'Hot Summer', value: '12125' },
    { title: 'Hot Wheels', value: '12122' },
    { title: 'Oktoberfest', value: '13059' },
    { title: 'Outdoor', value: '12124' },
    { title: 'Retro', value: '13063' },
    { title: 'Rollenspiele', value: '13004' },
    { title: 'Serienstars', value: '12127' },
    { title: 'Sportlich', value: '12120' },
    { title: 'Tattoos', value: '12118' },
    { title: 'Wasserspiele', value: '12126' },
    { title: '50 Jahre Playboy', value: '13192' },
  ]);

  constructor(apollo: Apollo) {
    // if (!this.colorsSubject.getValue() || !this.colorsSubject.getValue() || !this.colorsSubject.getValue()) {
    //   combineLatest([
    //     apollo.query<IAPIResult>({ query: COLORS_QUERY }),
    //     apollo.query<IAPIResult>({ query: EYE_COLORS_QUERY }),
    //     apollo.query<IAPIResult>({ query: PREFERENCES_QUERY })
    //   ]).pipe(
    //     map(rawAttributeResults => rawAttributeResults.map(rawValues =>
    //       rawValues.data.pbTaxonomy.results.map((v): IAttribute => ({
    //         title: v.entityLabel,
    //         value: v.entityId,
    //       }))
    //     )),
    //     first()
    //   ).subscribe(([colors, eyeColors, preferences]) => {
    //     this.colorsSubject.next(colors);
    //     this.eyeColorsSubject.next(eyeColors);
    //     this.preferencesSubject.next(preferences);
    //   });
    // }
  }
}
