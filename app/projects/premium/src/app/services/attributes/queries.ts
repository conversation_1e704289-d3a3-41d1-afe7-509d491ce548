import gql from 'graphql-tag';

export const COLORS_QUERY = gql`
  query {
    pbTaxonomy(filter: { vid: "color" }) {
      results {
        entityLabel
        entityId
      }
    }
  }
`;

export const EYE_COLORS_QUERY = gql`
  query {
    pbTaxonomy(filter: { vid: "eyecolor" }) {
      results {
        entityLabel
        entityId
      }
    }
  }
`;

export const PREFERENCES_QUERY = gql`
  query {
    pbTaxonomy(filter: { vid: "preferences" }) {
      results {
        entityLabel
        entityId
      }
    }
  }
`;
