import gql from 'graphql-tag';
import { GenerateCountryParam } from '../../utils/country';
import { GetReleaseYearFilter } from '../../utils/releases';
import { ICategoryFilterOptions, ICategoryQueryVariableSort } from './models';

export interface IGirlInfoPreviewByDescriptionIdQueryParams {
  hairColor: string;
  eyeColor: string;
  preference: string;
  bustSize: Partial<{ min: number | string; max: number | string }>;
  release: Partial<{ min: number | string; max: number | string }>;
  publish: Partial<{ value: number | string }>;
  field_category: (string | number)[];
  sortField: 'FIELD_PUBLISH_DATE_VALUE' | 'TOTALCOUNT' | 'DAYCOUNT';
  sortDirection: 'DESC' | 'ASC';
  searchName: string;
  girlId: string;
  girlInfoId: string;
  year: number;
  month: number;
}

export function GenerateMainQueryParams(
  filter: Partial<ICategoryFilterOptions>,
  sorting: Partial<ICategoryQueryVariableSort>,
  field_category?: (string | number)[],
): Partial<IGirlInfoPreviewByDescriptionIdQueryParams> {
  let params: Partial<IGirlInfoPreviewByDescriptionIdQueryParams> = {
    field_category,
    ...sorting,
  };

  if (filter.Veröffentlichung) {
    params.release = GetReleaseYearFilter(filter.Veröffentlichung);
  }

  if (filter.Oberweite) {
    params.searchName = filter.Oberweite;
  }

  if (filter.Vorliebe) {
    params.preference = filter.Vorliebe;
  }

  if (filter.Haarfarbe) {
    params.hairColor = filter.Haarfarbe.join('|');
  }

  if (filter.Augenfarbe) {
    params.eyeColor = filter.Augenfarbe.join('|');
  }

  if (filter.Kategorie) {
    params.field_category = filter.Kategorie;
  }

  if (filter.Land) {
    params = { ...params, ...GenerateCountryParam(filter.Land as any) };
  }

  return params;
}

export const GIRL_INFO_PREVIEW_BY_DESCRIPTOR_ID_QUERY = gql`
  query GetGirlDetailData(
    $hairColor: String
    $eyeColor: String
    $preference: String
    $bustSize: BustsizeMultiViewFilterInput
    $release: DescriptorYearMultiViewFilterInput
    $field_category: [String]
    $sortField: GraphqlGalleriesGraphql1ViewSortBy
    $pageSize: Int!
    $page: Int!
    $countryIs: CountryIsMultiViewFilterInput
    $countryIsNot: CountryIsNotMultiViewFilterInput
    $sortDirection: ViewSortDirection
    $searchName: String
    $publish: PublishDateMultiViewFilterInput
    $girlId: String
    $girlInfoId: String
  ) {
    pbInfoWithLastGallery(
      filter: {
        haircolor: { value: $hairColor }
        eyecolor: { value: $eyeColor }
        field_preference_target_id: $preference
        bustsize: $bustSize
        field_category: $field_category
        descriptor_year: $release
        search_name: $searchName
        search_description: $searchName
        search_credit: $searchName
        country_is: $countryIs
        country_is_not: $countryIsNot
        tags: $searchName
        girl_id: { value: $girlId }
        publish_date: $publish
        girl_info_id: { value: $girlInfoId }
      }
      sortBy: $sortField
      pageSize: $pageSize
      sortDirection: $sortDirection
      page: $page
    ) {
      count
      results {
        entityLabel
        entityId
        fieldImage {
          entity {
            ... on MediaImage {
              fieldMediaImage {
                url
                width
                height
              }
              fieldFocalPointX
              fieldFocalPointY
            }
          }
        }
        fieldPublishDate {
          value
        }
        reverseGalleriesGirlInfo {
          entities {
            entityLabel
            entityId
            ... on GirlInfo {
              descriptorYear
              descriptorMonth
              release
              fieldPlusAccess
              fieldCategory {
                entity {
                  name
                }
              }
              fieldPublicImages {
                url
              }
              fieldPublicImagesLow: fieldPublicImages {
                derivative(style: XLARGE) {
                  url
                }
              }
              fieldImageCount
              fieldVideoCount
              fieldLatestGalleryRelease
              fieldMainFocalPointX
              fieldMainFocalPointY
              fieldSecondFocalPointX
              fieldSecondFocalPointY
              fieldThirdFocalPointX
              fieldThirdFocalPointY
              queryGirl {
                entities {
                  ... on Girl {
                    id
                    name
                    lastname
                    middlename
                    firstname
                    reverseGirlGirlInfo {
                      count
                      entities {
                        ... on GirlInfo {
                          queryGalleries(
                            filter: {
                              conditions: { field: "status", value: "1" }
                            }
                          ) {
                            entities {
                              ... on MediaGallery {
                                queryFieldMediaSlideshow {
                                  count
                                }
                                queryFieldVideos {
                                  count
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;
