import { Injectable } from '@angular/core';
import { distinctJSON } from '@pb/ui';
import { IFilterOptions } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { Apollo } from 'apollo-angular';
import { IPreview } from 'projects/premium/src/app/models/preview';
import {
  BehaviorSubject,
  combineLatest,
  Observable,
  of,
  Subscription,
  zip,
} from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  scan,
  switchMap,
} from 'rxjs/operators';

import { GetFocalPoint } from '../../shared/pipes/focal-point.pipe';
import { BUST_FILTER_OPTIONS } from '../../utils/bust';
import { COUNTRY_FILTER_OPTIONS } from '../../utils/country';
import { IsNewFromTimestamp } from '../../utils/newCheck';
import { RELEASES_FILTER_OPTIONS } from '../../utils/releases';
import { AccountService } from '../account.service';
import { AttributesService } from './../attributes/attributes.service';
import {
  ICategoryFilterOptions,
  ICategoryQueryVariableSort,
  IGirlInfoServiceData,
  ISimplePreviewModel,
  SortOptions,
} from './models';
import {
  GIRL_INFO_PREVIEW_BY_DESCRIPTOR_ID_QUERY,
  IGirlInfoPreviewByDescriptionIdQueryParams,
} from './queries';
import { parseNumberOrNull } from '../../utils/parseNumberOrNull';
import { getFocalPointByImageIndex } from '../../utils/getFocalPointByImageIndex';

export { ICategoryFilterOptions, IGirlInfoServiceData, SortOptions };

export type GirlInfoListQueryParams = {
  screens?: number;
  perScreen: number;
  screen?: number;
};

@Injectable({ providedIn: 'root' })
export class GirlInfoService {
  private girlsBehav = new BehaviorSubject<{ count: number; data: IPreview[] }>(
    { count: 0, data: [] },
  );
  private loadingBehav = new BehaviorSubject<boolean>(true);

  public $Data: Observable<IGirlInfoServiceData> = combineLatest([
    this.girlsBehav.pipe(distinctJSON()),
    this.loadingBehav.pipe(distinctUntilChanged((a, b) => a === b)),
  ]).pipe(
    map(([{ data, count }, loading]) => ({ data, count, loading })),
    debounceTime(100),
  );

  public readonly $filterOptions: Observable<IFilterOptions>;

  public mapSortingObservable(obs: Observable<SortOptions>) {
    return obs.pipe(
      map((sorting: SortOptions): ICategoryQueryVariableSort => {
        switch (sorting) {
          case 'Beliebteste':
            return { sortField: 'TOTALCOUNT', sortDirection: 'DESC' };
          case 'Beliebteste (heute)':
            return { sortField: 'DAYCOUNT', sortDirection: 'DESC' };
          case 'Älteste':
            return {
              sortField: 'FIELD_PUBLISH_DATE_VALUE',
              sortDirection: 'ASC',
            };
          default:
          case 'Neueste':
            return {
              sortField: 'FIELD_PUBLISH_DATE_VALUE',
              sortDirection: 'DESC',
            };
        }
      }),
    );
  }

  public readonly sortingOptions: SortOptions[] = [
    'Beliebteste',
    'Beliebteste (heute)',
    'Älteste',
    'Neueste',
  ];

  private dataSub: Subscription | undefined;
  private loadingSub: Subscription | undefined;

  fetchGirlInfoPreviews(
    screens: number,
    perScreen: number,
    params: Partial<IGirlInfoPreviewByDescriptionIdQueryParams>,
    offset = 0,
  ): Observable<IGirlInfoServiceData> {
    return this.fetchGirlInfoPreviewsDynamic(
      of([{ screens, perScreen, offset }, params]),
    );
  }

  fetchGirlInfoPreviewsDynamic(
    _params: Observable<
      [
        GirlInfoListQueryParams,
        Partial<IGirlInfoPreviewByDescriptionIdQueryParams>,
      ]
    >,
  ): Observable<IGirlInfoServiceData> {
    return combineLatest([this.accountService.LastLogin, _params]).pipe(
      distinctJSON(),
      debounceTime(100),
      switchMap(
        ([lastLogin, [{ screens = 1, perScreen, screen = 0 }, params]]) =>
          zip(
            ...new Array(screens)
              .fill(null)
              .map((_, page) => ({
                ...params,
                page: page + screen,
                pageSize: perScreen,
              }))
              .map((variables) =>
                this.apollo.query<{
                  pbInfoWithLastGallery: {
                    count: number;
                    results: ISimplePreviewModel[];
                  };
                }>({
                  query: GIRL_INFO_PREVIEW_BY_DESCRIPTOR_ID_QUERY,
                  variables,
                }),
              ),
          ).pipe(
            scan(
              (fullData: IGirlInfoServiceData, v): IGirlInfoServiceData => {
                const items = [
                  ...fullData.data,
                  ...v
                    .reduce(
                      (a, m) => [
                        ...a,
                        ...m.data?.pbInfoWithLastGallery.results,
                      ],
                      [] as ISimplePreviewModel[],
                    )
                    .filter((v) => !!v)
                    .map((gallery, mappingIndex, all): IPreview => {
                      // If there are multiple references to the same gallery, select the correct one by index
                      const index = Math.max(
                        0,
                        all
                          .map((v, i) => ({ ...v, i }))
                          .filter((v) => gallery.entityId === v.entityId)
                          .findIndex((v) => v.i === mappingIndex),
                      );

                      const girlInfo =
                        gallery.reverseGalleriesGirlInfo.entities[index];
                      const girl = girlInfo?.queryGirl.entities.filter(
                        (v) => !!v,
                      )[0];
                      const allGalleries = (
                        girl?.reverseGirlGirlInfo.entities || []
                      )
                        .filter((v) => !!v)
                        .map((v) => v.queryGalleries?.entities || [])
                        .reduce((a, v) => [...a, ...v], [])
                        .filter((v) => !!v);
                      let imageRatio: number | undefined;
                      // if (!gallery.fieldPublishDate) {
                      //   console.log(
                      //     'no publishing date found for',
                      //     girlInfo.entityLabel,
                      //     gallery,
                      //     girlInfo,
                      //   );
                      // }
                      const paywallImages =
                        girlInfo?.fieldPublicImagesLow.map(
                          (image, imageIndex) => ({
                            src: image.derivative.url,
                            focalPoint: getFocalPointByImageIndex(
                              girlInfo,
                              imageIndex,
                            ),
                          }),
                        ) ||
                        girlInfo?.fieldPublicImages.map((image, imageIndex) => ({
                          src: image.url,
                          focalPoint: getFocalPointByImageIndex(
                            girlInfo,
                            imageIndex,
                          ),
                        })) ||
                        [];
                      return {
                        id: gallery.entityId,
                        girlInfoId: girlInfo?.entityId,
                        girlId: girl?.id,
                        text: girlInfo?.entityLabel,
                        title: girlInfo?.fieldCategory?.[0]?.entity?.name,
                        year: girlInfo?.descriptorYear,
                        month: girlInfo?.descriptorMonth,
                        fieldPlusAccess: girlInfo?.fieldPlusAccess,
                        paywallImages,

                        // nexxID?: string;
                        imageRatio,
                        image: girlInfo?.fieldPublicImages?.at(0)?.url,
                        imageLowRes:
                          girlInfo?.fieldPublicImagesLow?.at(0)?.derivative.url,
                        isNew: IsNewFromTimestamp(girlInfo?.fieldLatestGalleryRelease as number),
                        focalPoint: girlInfo?.fieldMainFocalPointX
                          ? {
                              x: girlInfo?.fieldMainFocalPointX || 50,
                              y: girlInfo?.fieldMainFocalPointY || 50,
                            }
                          : GetFocalPoint(girlInfo as any),
                        meta: {
                          images: parseNumberOrNull(girlInfo?.fieldImageCount),
                          videos: parseNumberOrNull(girlInfo?.fieldVideoCount),
                          girlInfos: girl?.reverseGirlGirlInfo.count,
                        },
                        link: [
                          '/girl',
                          girl?.id || -1,
                          girlInfo?.entityId || -1,
                        ],
                      };
                    }),
                ];
                return {
                  loading: v.reduce((is, vi) => is || vi.loading, false),
                  count: v.reduce(
                    (all, current) =>
                      all + current.data.pbInfoWithLastGallery.count,
                    0,
                  ),
                  data: items,
                };
              },
              { loading: false, count: 0, data: [] } as IGirlInfoServiceData,
            ),
          ),
      ),
      distinctJSON(),
    );
  }

  getGirlInfoPreviews(
    _params: Observable<
      [
        GirlInfoListQueryParams,
        Partial<IGirlInfoPreviewByDescriptionIdQueryParams>,
      ]
    >,
  ): Observable<IGirlInfoServiceData> {
    this.loadingSub?.unsubscribe();
    this.dataSub?.unsubscribe();
    this.girlsBehav.next({ count: 0, data: [] });
    this.loadingBehav.next(true);
    _params
      .pipe(
        distinctUntilChanged(
          ([a, paramsA], [b, paramsB]) =>
            JSON.stringify(paramsA) === JSON.stringify(paramsB),
        ),
      )
      .subscribe(() => this.loadingBehav.next(true));

    this.loadingSub = this.fetchGirlInfoPreviewsDynamic(_params).subscribe(
      ({ loading, data, count }) => {
        this.girlsBehav.next({ data, count });
        this.loadingBehav.next(loading);
      },
    );

    return this.$Data;
  }

  constructor(
    private apollo: Apollo,
    private accountService: AccountService,
    attributesService: AttributesService,
  ) {
    this.$filterOptions = combineLatest([
      attributesService.$eyeColors,
      attributesService.$colors,
      attributesService.$preferences,
    ]).pipe(
      debounceTime(10),
      map(([eyecolor, colors, preferences]): IFilterOptions => {
        const data: IFilterOptions = {
          Oberweite: { type: 'single', options: BUST_FILTER_OPTIONS },
          Haarfarbe: { type: 'multi', options: colors },
          Augenfarbe: { type: 'multi', options: eyecolor },
          Vorliebe: { type: 'single', options: preferences },
          Veröffentlichung: {
            type: 'single',
            options: RELEASES_FILTER_OPTIONS,
          },
          Land: { type: 'single', options: COUNTRY_FILTER_OPTIONS },
        };
        return data;
      }),
    );
  }
}
