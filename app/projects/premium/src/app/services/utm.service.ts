import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { SsrCookieService as CookieService } from 'ngx-cookie-service-ssr';

@Injectable({
  providedIn: 'root'
})
export class UtmService {
  private readonly cookieService = inject(CookieService);
  private readonly platformId = inject(PLATFORM_ID);

  private readonly utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];

  /**
   * Handles UTM parameters and sets cookies for tracking
   * @param upscoreObjectId - The object_id from upscore configuration (e.g., 'aa_6')
   */
  handleUTM(upscoreObjectId?: string): void {
    if (!isPlatformBrowser(this.platformId)) {
      return; // Only run in browser
    }

    const urlParams = new URLSearchParams(window.location.search);
    const isLocalhost = window.location.hostname === 'localhost';

    // Get the current URL without query parameters
    const currentUrlWithoutParams = window.location.origin + window.location.pathname;

    const setCookie = (key: string, value: string | null) => {
      if (!value) {
        return;
      }

      try {
        const decoded = decodeURIComponent(value.trim());
        let cookieString = `${key}=${decoded}; path=/`;
        if (!isLocalhost) {
          cookieString += `; domain=.playboy.de`;
        }
        document.cookie = cookieString;
      } catch (e) {
        console.warn(`Could not decode cookie value for ${key}:`, value);
      }
    };

    // Set the source_id cookie
    setCookie('source_id', currentUrlWithoutParams);

    // Set UTM cookies
    this.utmParams.forEach(param => {
      const value = urlParams.get(param);
      if (value) {
        setCookie(param, value);
      }
    });

    // Set the upscore_object_id cookie if provided
    if (upscoreObjectId) {
      setCookie('upscore_object_id', upscoreObjectId);
    }
  }

  /**
   * Gets UTM parameter value from URL
   * @param param - UTM parameter name
   * @returns UTM parameter value or null
   */
  getUtmParam(param: string): string | null {
    if (!isPlatformBrowser(this.platformId)) {
      return null;
    }

    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
  }

  /**
   * Gets all UTM parameters from URL
   * @returns Object with UTM parameters
   */
  getAllUtmParams(): { [key: string]: string } {
    if (!isPlatformBrowser(this.platformId)) {
      return {};
    }

    const urlParams = new URLSearchParams(window.location.search);
    const utmData: { [key: string]: string } = {};

    this.utmParams.forEach(param => {
      const value = urlParams.get(param);
      if (value) {
        utmData[param] = value;
      }
    });

    return utmData;
  }
}
