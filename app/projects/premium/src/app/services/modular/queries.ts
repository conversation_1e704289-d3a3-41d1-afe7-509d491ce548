import gql from 'graphql-tag';

export const GRAPHQL_CATEGORY_BY_ID_GRAPHQL_1_VIEW = gql`
  query ($url: String) {
    graphqlCategoryByIdGraphql1View(contextualFilter: { tid: $url }) {
      results {
        entityLabel
        fieldLink {
          url {
            path
          }
        }
        fieldImage {
          targetId
          entity {
            ... on MediaPublicImage{
              fieldMediaImage1{
                url
              }
              fieldFocalPointX
              fieldFocalPointY
            }
            ... on MediaImage{
              fieldMediaImage{
                url
              }
              fieldFocalPointX
              fieldFocalPointY
            }
          }
        }
      }
    }
  }
`;

export const GRAPHQL_GIRL_INFOS_BY_CATEGORY_QUERY = gql`
  query ($url: String, $urlTwo: String) {
    graphqlGirlInfosByCategoryGraphql1View(contextualFilter: { descriptor_country_ref: $urlTwo, field_category_target_id: $url }) {
      results {
        ... on GirlInfo {
          id
          name
          fieldPublicImages {
            url
          }
          fieldPlusAccess
          fieldFeatured
          girl {
            targetId
          }
          fieldCategory {
            entity {
              name
            }
          }
          descriptorYear
          descriptorMonth
          fieldPublicImagesLow: fieldPublicImages {
            derivative(style: XLARGE) {
              url
            }
          }
          fieldMainFocalPointX
          fieldMainFocalPointY
          fieldSecondFocalPointX
          fieldSecondFocalPointY
          fieldThirdFocalPointX
          fieldThirdFocalPointY
          fieldLatestGalleryRelease
        }
      }
    }
  }
`;

export const GRAPHQL_POPULAR_GIRL_INFOS_BY_CATEGORY_QUERY = gql`
    query ($url: String, $urlTwo: String) {
    graphqlPopularGirlInfosByCategoryGraphql1View(contextualFilter: { descriptor_country_ref: $urlTwo, field_category_target_id: $url }) {
      results {
        ... on GirlInfo {
          id
          name
          fieldPublicImages {
            url
          }
          fieldPlusAccess
          fieldFeatured
          girl {
            targetId
          }
          fieldCategory {
            entity {
              name
            }
          }
          descriptorYear
          descriptorMonth
          fieldPublicImagesLow: fieldPublicImages {
            derivative(style: XLARGE) {
              url
            }
          }
          fieldMainFocalPointX
          fieldMainFocalPointY
          fieldSecondFocalPointX
          fieldSecondFocalPointY
          fieldThirdFocalPointX
          fieldThirdFocalPointY
          fieldLatestGalleryRelease
        }
      }
    }
  }
`;

export const MODULAR_PAGE_QUERY = gql`
  query ($url: String) {
    modularPageGraphql1View(contextualFilter: { field_url_value: $url }) {
      results {
        entityLabel
        fieldModules {
          entity {
            entityBundle
            ... on ParagraphFeatureModule {
              fieldTitle
              fieldCategoryBool
              fieldCategoryReference {
                targetId
                entity {
                  entityBundle
                }
              }
              fieldDescriptorCountry {
                targetId
              }
              fieldSorting
              fieldGridLayout
              fieldGirlInfo {
                entity {
                  ... on GirlInfo {
                    id
                    name
                    fieldPublicImages {
                      url
                    }
                    fieldPlusAccess
                    fieldFeatured
                    girl {
                      targetId
                    }
                    fieldCategory {
                      entity {
                        name
                      }
                    }
                    descriptorYear
                    descriptorMonth
                    fieldPublicImagesLow: fieldPublicImages {
                      derivative(style: XLARGE) {
                        url
                      }
                    }
                    fieldMainFocalPointX
                    fieldMainFocalPointY
                    fieldSecondFocalPointX
                    fieldSecondFocalPointY
                    fieldThirdFocalPointX
                    fieldThirdFocalPointY
                    fieldLatestGalleryRelease
                  }
                }
              }
              fieldButtonBool
              fieldButton {
                url {
                  path
                }
                title
              }
            }
            ... on ParagraphCategoryModule {
              fieldTitle
              fieldGridLayout2
              fieldCategories {
                targetId
                entity {
                  entityBundle
                }
              }
            }
            ... on ParagraphGirlDesTagesModule {
              fieldTitle
              fieldGridLayout3
              fieldButtonBool
              fieldButton {
                url {
                  path
                }
                title
              }
            }
            ... on ParagraphSearchModule {
              fieldTitle
            }
            ... on ParagraphFeedModule {
              fieldTitle
            }
            ... on ParagraphFavoritesModule {
              fieldTitle
              fieldGridLayout2
              fieldCategoryList
            }
            ... on ParagraphVideoModule {
              fieldTitle
              fieldGridLayout2
            }
            ... on ParagraphEPaperArchivModule {
              fieldTitle
              fieldLoadNewest
              fieldMagazine
              fieldButtonBool
              fieldButton {
                url {
                  path
                }
                title
              }
            }
          }
        }
      }
    }
  }
`;
