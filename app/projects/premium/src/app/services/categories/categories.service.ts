import { Injectable } from '@angular/core';
import {
  FilterValue,
  IFilterOptions,
} from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { Apollo } from 'apollo-angular';
import { combineLatest, Observable, of } from 'rxjs';
import { map, mergeMap } from 'rxjs/operators';
import { CategoryNameToSlug } from '../../shared/pipes/category-slug.pipe';
import { ICategory } from './definitions/models';
import { Category_DATA_QUERY } from './definitions/queries';

enum CustomCategories {
  Coverstars = '2245',
  Playmates = '2264',
}

export { ICategory };

@Injectable({ providedIn: 'root' })
export class CategoriesService {
  // private readonly data: (ICategory & { children: ICategory[] })[] = [
  //   { "entityLabel": "Coverstar", "entityId": "2245", "children": [] },
  //   {
  //     "entityLabel": "Playmate", "entityId": "2264", "children": [
  //       { "entityLabel": "Playmate des Monats", "entityId": "10199" },
  //       { "entityLabel": "Playmate des Jahres", "entityId": "10198" }
  //     ]
  //   },
  //   {
  //     "entityLabel": "Women of Playboy", "entityId": "11990", "children": [
  //       { "entityLabel": "Blende Sechs", "entityId": "2274" },
  //       { "entityLabel": "International", "entityId": "2282" },
  //       { "entityLabel": "Playboy Muse", "entityId": "2288" },
  //       { "entityLabel": "Girlfriends", "entityId": "2302" },
  //       { "entityLabel": "Adult Stars", "entityId": "3563" },
  //       { "entityLabel": "Specials", "entityId": "10197" },
  //       { "entityLabel": "Classic", "entityId": "10196" },
  //       { "entityLabel": "Mehr", "entityId": "10195" }]
  //   }
  // ];
  // private behav = new CachedBehav<(ICategory & { children: ICategory[] })[]>('menu');

  constructor(private readonly apollo: Apollo) {
    // if (!this.behav.getValue()) {
    //   this.fetchCategories().pipe(
    //     first()
    //   ).subscribe(cat => this.behav.next(cat));
    // }
  }

  public readonly Categories: Observable<
    (ICategory & { children: ICategory[] })[]
  > = of([
    //{ "entityLabel": "50 Jahre Playboy", "entityId": "13191", "children": [] },
    { entityLabel: 'Coverstar', entityId: '2245', children: [] },
    {
      entityLabel: 'Playmate',
      entityId: '2264',
      children: [
        { entityLabel: 'Playmate des Monats', entityId: '10199' },
        { entityLabel: 'Playmate des Jahres', entityId: '10198' },
      ],
    },
    {
      entityLabel: 'Women of Playboy',
      entityId: '11990',
      children: [
        { entityLabel: 'Blende Sechs', entityId: '2274' },
        { entityLabel: 'International', entityId: '2282' },
        { entityLabel: 'Playboy Muse', entityId: '2288' },
        { entityLabel: 'Playboy Creator', entityId: '13213' },
        { entityLabel: 'Girlfriends', entityId: '2302' },
        { entityLabel: 'Adult Stars', entityId: '3563' },
        { entityLabel: 'Specials', entityId: '10197' },
        { entityLabel: 'Classic', entityId: '10196' },
        { entityLabel: 'Mehr', entityId: '10195' },
      ],
    },
  ]);

  public readonly $filterOptions = this.Categories.pipe(
    map((complex) => complex.map((v) => [v, ...v.children]).flat()),
    map((flatCategories) =>
      flatCategories.map(
        (category): FilterValue => ({
          title: category.entityLabel,
          value: category.entityId,
        }),
      ),
    ),
    map(
      (options): IFilterOptions => ({
        Kategorie: { type: 'multi', options },
      }),
    ),
  );

  private fetchCategories(): Observable<
    (ICategory & { children: ICategory[] })[]
  > {
    return this.fetchSubMenu().pipe(
      mergeMap((categories) =>
        combineLatest(
          categories.map((category) =>
            this.fetchSubMenu(category.entityId).pipe(
              map((children) => ({ ...category, children })),
            ),
          ),
        ),
      ),
    );
  }

  private fetchSubMenu(parent_target_id?: any): Observable<ICategory[]> {
    return this.apollo
      .query<{ pbNav: { results: ICategory[] } }>({
        query: Category_DATA_QUERY,
        variables: { parent_target_id },
      })
      .pipe(map((v) => v.data?.pbNav.results));
  }

  find(q: (c: ICategory) => boolean): Observable<ICategory | undefined> {
    return this.Categories.pipe(
      map((v) => {
        let cat: ICategory = undefined;
        v.forEach((c) => {
          if (q(c)) {
            cat = c;
            return;
          }
          if (!cat) {
            c.children.forEach((sc) => {
              if (!cat && q(sc)) {
                cat = sc;
                return;
              }
            });
          }
        });
        return cat;
      }),
    );
  }

  getById(id: number): Observable<ICategory | undefined> {
    return this.find((c) => c.entityId === id);
  }

  getBySlug(slug: string): Observable<ICategory | undefined> {
    return this.find((c) => CategoryNameToSlug(c.entityLabel) === slug);
  }
}
