import { PromoService } from './promo.service';
import { Injectable } from '@angular/core';
import { AccountService } from './account.service';
import { Observable, combineLatest, BehaviorSubject } from 'rxjs';
import { combineAll, map, distinctUntilChanged } from 'rxjs/operators';
import { Router } from '@angular/router';

@Injectable({ providedIn: 'root' })
export class MaintainanceService {
  private readonly behav = new BehaviorSubject(false);
  public readonly $isMaintainance = this.behav.pipe(
    distinctUntilChanged((a, b) => a === b),
  );

  constructor(promo: PromoService, account: AccountService, router: Router) {
    combineLatest([promo.$isMaintainance, account.Admin])
      .pipe(
        map(([maintainance, admin]) => !admin && maintainance),
        distinctUntilChanged((a, b) => a === b),
      )
      .subscribe((is) => this.behav.next(is));

    this.$isMaintainance.subscribe((v) => {
      if (v) {
        return router.navigate(['/maintainance']);
      }
    });
  }
}
