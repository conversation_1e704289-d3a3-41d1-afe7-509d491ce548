import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

const redirect = environment.serverUrl + '/user/token/new';

export interface IPackage {
  months: number;
  monthlyPrice: string;
  fullPrice: string;
  newPrice?: string;
  link: string;
  recommendation?: true;
}

@Injectable({ providedIn: 'root' })
export class PackagesService {
  private readonly packagesBehaviour = new BehaviorSubject<IPackage[]>([
    {
      months: 12,
      monthlyPrice: '9,49 €',
      fullPrice: 'Insgesamt 113,88 €*',
      link: `https://shop.playboy.de/abo/cart/add?product=playboy-premium-12-monate/&plan_id=5&redirect=${redirect}`,
    },
    {
      months: 6,
      monthlyPrice: '12,99 €',
      fullPrice: 'Insgesamt 77,94 €*',
      link: `https://shop.playboy.de/abo/cart/add?product=playboy-premium-6-monate/&plan_id=6&redirect=${redirect}`,
    },
    {
      recommendation: true,
      months: 3,
      monthlyPrice: '16,49 €',
      fullPrice: 'Insgesamt 49,97 €*',
      link: `https://shop.playboy.de/abo/cart/add?product=playboy-premium-3-monate/&plan_id=7&redirect=${redirect}`,
    },
    {
      months: 1,
      monthlyPrice: '19,99 €',
      fullPrice: 'Insgesamt 19,99 €*',
      link: `https://shop.playboy.de/abo/cart/add?product=playboy-premium-1-monat/&plan_id=8&redirect=${redirect}`,
    },
  ]);

  public readonly $packages = this.packagesBehaviour.asObservable();
  public getPackagesForCoupon(coupon: string) {
    return this.$packages.pipe(
      map((data): IPackage[] => [
        {
          months: 12,
          monthlyPrice: '9,49 €',
          newPrice: '0,00 €',
          fullPrice: 'Insgesamt 0,00€*',
          link: `https://shop.playboy.de/abo/cart/add?product=playboy-premium-12-monate/&plan_id=5&redirect=${redirect}&couponcode=${coupon}`,
        },
      ]),
    );
  }
}
