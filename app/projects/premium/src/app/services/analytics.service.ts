import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'projects/premium/src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AnalyticsService {
  analyticsEndpoint =
    environment.serverUrl + '/core/modules/statistics/statistics.php';

  private createBody(
    key: 'id' | 'mid',
    id: string,
    type: 'girl' | 'girl_info' | 'media',
  ) {
    let body = new URLSearchParams();
    body.set('key', key);
    body.set('id', id);
    body.set('type', type);

    return body.toString();
  }

  sendEvent(eventType: 'girl' | 'girl_info' | 'media', id: string) {
    let key, type;
    switch (eventType) {
      case 'girl':
        key = 'id';
        type = 'girl';
        break;
      case 'girl_info':
        key = 'id';
        type = 'girl_info';
        break;
      case 'media':
        key = 'mid';
        type = 'media';
        break;
    }

    const body = this.createBody(key, id, type);

    const options = {
      headers: new HttpHeaders().set(
        'Content-Type',
        'application/x-www-form-urlencoded',
      ),
    };

    this.http
      .post(this.analyticsEndpoint, body, options)
      .subscribe((res) => res);
  }

  constructor(private readonly http: HttpClient) {}
}
