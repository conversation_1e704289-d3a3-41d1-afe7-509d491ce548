import { environment } from 'projects/premium/src/environments/environment';
import { distinctJSON } from '@pb/ui';
import { catchError, map } from 'rxjs/operators';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SsrCookieService as CookieService } from 'ngx-cookie-service-ssr';
interface FavoriteRequestBody {
  flag_id: 'gallery_flag' | 'image_flag' | 'girl_flag';
  entity_type: 'media' | 'girl';
  entity_id: string;
}

export interface IRequestProperties {
  endpoint: 'rest' | 'graphql' | 'preview' | 'base';
  accept: 'application/json' | 'text/html';
  cache: boolean;
}

export function BuildEndpoint(
  url: string,
  { endpoint = 'rest' }: Partial<IRequestProperties>,
) {
  return `${endpoint === 'rest' ? environment.rest : environment.serverUrl}/${url}`;
}

@Injectable({ providedIn: 'root' })
export class ApiService {
  private readonly reqBehav = new BehaviorSubject<{
    [url: string]: null | any;
  }>({});

  constructor(
    private readonly http: HttpClient,
    private cookieService: CookieService,
  ) {}

  public _get<T>(
    url: string,
    { accept = 'application/json', ...props }: Partial<IRequestProperties> = {},
  ): Observable<T> {
    return this.http
      .get<T>(BuildEndpoint(url, { ...props, accept }), {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          Accept: accept,
          'Content-Type': 'application/json',
        },
        withCredentials: true,
        // @ts-ignore
        responseType: accept === 'application/json' ? 'json' : 'text',
      })
      .pipe(map((v) => v as any));
  }

  private getCachedUrl<T>(url: string) {
    return this.reqBehav.pipe(
      map((v) => v[url] as T),
      distinctJSON(),
    );
  }

  private patch(url: string, data: any) {
    this.reqBehav.next({ ...this.reqBehav.getValue(), [url]: data });
  }

  public get<T>(
    url: string,
    props: Partial<IRequestProperties> = {},
  ): Observable<T> {
    return this._get<T>(url, props);
  }

  public post<T>(
    url: string,
    body: any,
    props: Partial<IRequestProperties> = {},
  ): Observable<T> {
    const headerObj = {
      'X-CSRF-Token': this.cookieService.get('csrf_token'),
    };

    const requestOptions = {
      headers: headerObj,
      withCredentials: true,
    };

    return this.http
      .post<T>(BuildEndpoint(url, props), body, requestOptions)
      .pipe(
        catchError((e) => {
          console.error(e);
          return of(undefined);
        }),
      );
    // .toPromise()
    // .catch((err) => console.error(err));
  }
}
