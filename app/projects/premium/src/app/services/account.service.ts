import { computed, inject, Injectable, PLATFORM_ID } from '@angular/core';
import { distinctJSON } from '@pb/ui';
import { INavbarAccountInfo } from '@pb/ui/components/navbar/upper/upper.component';
import { SsrCookieService as CookieService } from 'ngx-cookie-service-ssr';
import { firstValueFrom, from, Observable, of, shareReplay } from 'rxjs';
import {
  catchError,
  distinctUntilChanged,
  filter,
  map,
  startWith,
  switchMap,
} from 'rxjs/operators';

import { isPlatformBrowser } from '@angular/common';
import { environment } from '../../environments/environment';
import { ApiService } from './api.service';
import { toSignal } from '@angular/core/rxjs-interop';

export interface IAccount {
  name: string;
  roles: string;
  last_login: string;
  email: string;
  field_subscription_data: string;
  subscription_data: {
    (key: string): {
      (key: string): {
        cancelDate: string;
        cancelDateDate: Date;
        paymentId: string;
        name: string;
        paymentMethod: string;
        uuid: string;
        startDate: string;
      };
    };
  };
}

@Injectable({ providedIn: 'root' })
export class AccountService {
  subscription_data(subscription_data: any) {
    throw new Error('Method not implemented.');
  }

  private readonly api = inject(ApiService);
  private readonly cookieService = inject(CookieService);

  private readonly platFormId = inject(PLATFORM_ID);
  private readonly accountBehav: Observable<
    IAccount[] | undefined | 'loading'
  > = isPlatformBrowser(this.platFormId)
    ? from(this.get_csrf_token(isPlatformBrowser(this.platFormId))).pipe(
        switchMap(() =>
          this.api.get<IAccount[]>('user/current', { cache: false }),
        ),
        map((v) => (Array.isArray(v) ? v : undefined)),
        catchError(() => of(undefined)),
        startWith('loading' as const),
        shareReplay({ bufferSize: 1, refCount: false }),
      )
    : of(undefined);

  public get NavbarAccountInfo(): Observable<INavbarAccountInfo> {
    return this.LoggedIn.pipe(
      map((loggedIn) => ({
        loggedIn,
        profileUrl: 'https://login.playboy.de/service',
        loginUrl: environment.loginUrl,
      })),
    );
  }

  get Account(): Observable<IAccount | undefined> {
    return this.accountBehav?.pipe(
      filter((v) => v !== 'loading'),
      map((v: IAccount[]) =>
        !v
          ? false
          : v.find(
              (a) =>
                a.roles.toLowerCase().indexOf('subscriber') >= 0 ||
                a.roles.toLowerCase().indexOf('plus') >= 0,
            ) || v[0],
      ),
      distinctJSON(),
    ) as Observable<IAccount>;
  }

  get LastLogin(): Observable<number | undefined> {
    return this.Account.pipe(
      map((v) => Math.floor(new Date(v.last_login).getTime() / 1000)),
      distinctJSON(),
    );
  }

  get Subscribed(): Observable<boolean> {
    return this.Account.pipe(
      map(
        (a) =>
          a &&
          (a.roles.toLowerCase().indexOf('subscriber') >= 0 ||
            a.roles.toLowerCase().indexOf('plus') >= 0),
      ),
      distinctUntilChanged((a, b) => a === b),
    );
  }

  public readonly subscriptionType = toSignal(
    this.Account?.pipe(
      map((a) => {
        if (!a || !a.roles) {
          return null;
        }
        if (a.roles.toLowerCase().indexOf('subscriber') >= 0) {
          return 'all-access';
        } else if (a.roles.toLowerCase().indexOf('plus') >= 0) {
          return 'plus';
        }
        return null;
      }),
    ),
  );

  public readonly subscriptionTypeTitle = computed(() =>
    this.subscriptionType() === 'all-access'
      ? 'All Access'
      : this.subscriptionType() === 'plus'
        ? 'Plus'
        : '',
  );

  get Admin(): Observable<boolean> {
    return this.Account.pipe(
      map((a) => a && a.roles.toLowerCase().indexOf('admin') >= 0),
      distinctUntilChanged((a, b) => a === b),
    );
  }

  get LoggedIn(): Observable<boolean> {
    return this.Account.pipe(
      map((v) => !!v),
      distinctJSON(),
    );
  }

  private async get_csrf_token(isBrowser: boolean): Promise<void> {
    if (isBrowser) {
      return firstValueFrom(
        this.api.get<string>('session/token', {
          endpoint: 'base',
          accept: 'text/html',
        }),
      )
        .then((token) => {
          this.cookieService.set('csrf_token', token);
        })
        .catch((err) => console.error(err));
    }
    return Promise.resolve();
  }

  public CancelSubscription() {
    return this.api.post<string>(
      'webform_rest/submit',
      { webform_id: 'cancellation' },
      { endpoint: 'base' },
    );
  }

  public checkSession(): void {
    this.LoggedIn.subscribe(loggedIn => {
      if (loggedIn) {
        return;
      }

      const serverUrl = environment.serverUrl;
      const currentPath = window.location.pathname;
      const returnUrl =
        currentPath === '/'
          ? `${serverUrl}/frontend`
          : `${serverUrl}${currentPath}`;
      const subdomain =
        document.location.host === 'stage.playboy.de' ? 'login.stage' : 'login';
      const loginBaseUrl = `https://${subdomain}.playboy.de`;
      const sessionCheckUrl = `${loginBaseUrl}/api/session/check.json`;

      fetch(sessionCheckUrl, {
        credentials: 'include',
      })
        .then((res) => res.json())
        .then((data) => {
          if (data === 1) {
            window.location.href = `${loginBaseUrl}?return=${returnUrl}`;
          }
        })
        .catch(() => {});
    });
  }
}
