import { gql } from 'apollo-angular';
import { IPreview } from 'projects/premium/src/app/models/preview';
import {
  PublicImage,
  PublicImageDerivative,
} from '../../../../screens/model/screens/model/definitions/models';
import { getFocalPointByImageIndex } from '../../../../utils/getFocalPointByImageIndex';

export function MapFavGirlInfoResultToPreviews(data: {
  results: IFavGirlInfoResult[];
}): IPreview[] {
  return data.results.map((v): IPreview => {
    const image = v.queryGalleries.entities[0]?.fieldImage.entity;
    const paywallImages =
      v.fieldPublicImagesLow.map((img, index) => ({
        src: img.derivative.url,
        focalPoint: getFocalPointByImageIndex(v, index),
      })) ||
      v.fieldPublicImages.map((img, index) => ({
        src: img.url,
        focalPoint: getFocalPointByImageIndex(v, index),
      })) ||
      [];
    return {
      id: v.queryGalleries.entities[0]?.mid,
      girlId: v.girl.entity.entityId,
      girlInfoId: v.id,
      text: v.name,
      title: v.fieldCategory?.[0]?.entity?.entityLabel,
      year: v.descriptorYear,
      month: v.descriptorMonth,
      publicImage: v.fieldPublicImages?.[0]?.url,
      paywallImages,
      fieldPlusAccess: v.fieldPlusAccess,
      // subtitle: v.fieldCategory?.entity?.entityLabel,
      image: image?.fieldMediaImage.url,
      focalPoint: image
        ? {
            x: image.fieldFocalPointX,
            y: image.fieldFocalPointY,
          }
        : undefined,
      link: ['/girl', v.girl.entity.entityId, v.id],
    };
  });
}

export interface IFavGirlInfoResult {
  id: number;
  name: string;
  descriptorYear: number;
  descriptorMonth: number;
  fieldCategory: {
    entity: {
      entityLabel: string;
    };
  };
  girl: {
    entity: {
      entityId: string;
    };
  };
  fieldPublicImages: PublicImage[];
  fieldPublicImagesLow: PublicImageDerivative[];
  fieldMainFocalPointX?: number;
  fieldMainFocalPointY?: number;
  fieldSecondFocalPointX?: number;
  fieldSecondFocalPointY?: number;
  fieldThirdFocalPointX?: number;
  fieldThirdFocalPointY?: number;
  fieldPlusAccess?: boolean | string;
  queryGalleries: {
    entities: {
      entityLabel: string;
      mid: string;
      fieldImage: {
        entity: {
          fieldMediaImage: {
            url: string;
          };
          fieldFocalPointX: number;
          fieldFocalPointY: number;
        };
      };
    }[];
  };
}

export const GET_FAV_GIRL_INFOS = gql`
  query GetFavoriteGirlInfos($pageSize: Int!, $page: Int!) {
    pbFavoritesGirlInfos(pageSize: $pageSize, page: $page) {
      results {
        ... on GirlInfo {
          id
          name
          descriptorYear
          descriptorMonth
          fieldCategory {
            entity {
              entityLabel
            }
          }
          girl {
            entity {
              entityId
            }
          }
          fieldPublicImages {
            url
          }
          fieldPublicImagesLow: fieldPublicImages {
            derivative(style: XLARGE) {
              url
            }
          }
          fieldPlusAccess
          fieldMainFocalPointX
          fieldMainFocalPointY
          fieldSecondFocalPointX
          fieldSecondFocalPointY
          fieldThirdFocalPointX
          fieldThirdFocalPointY
          queryGalleries {
            entities {
              entityLabel
              ... on MediaGallery {
                mid
                fieldImage {
                  entity {
                    ... on MediaImage {
                      fieldMediaImage {
                        url
                      }
                      fieldFocalPointX
                      fieldFocalPointY
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;
export const GET_FAV_GIRL_INFOS_META = gql`
  {
    pbFavoritesGirlInfos {
      count
    }
  }
`;
