import { gql } from 'apollo-angular';
import { IPreview } from 'projects/premium/src/app/models/preview';

export function MapFavVideoResultToPreviews(data: {
  results: IFavVideoResult[];
}): IPreview[] {
  return data.results.map((v): IPreview => {
    const girlInfo =
      v.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
        .entities[0];
    return {
      id: v.mid,
      // girlInfoId: girlInfo?.entityId,
      nexxID: v.fieldNexxId,
      // girlId: v.entityId,
      text: girlInfo?.entityLabel,
      title: girlInfo?.entityLabel,
      image: v.fieldPreviewImage.entity.fieldMediaImage.entity.url,
      // subtitle: girlInfo?.fieldCategory?.entity?.entityLabel,
      fieldPlusAccess: false,
      link: ['/girl-info', girlInfo?.entityId, v.mid],
    };
  });
}

export interface IFavVideoResult {
  mid: number;
  fieldNexxId: string;
  fieldPreviewImage: {
    entity: {
      fieldMediaImage: {
        entity: {
          url: string;
        };
      };
    };
  };
  reverseFieldVideosMedia: {
    entities: {
      reverseGalleriesGirlInfo: {
        entities: {
          entityId: number;
          entityLabel: string;
        }[];
      };
    }[];
  };
}

export const GET_FAV_VIDEOS = gql`
  query GetFavoriteVideos($pageSize: Int!, $page: Int!) {
    pbFavoritesVideos(pageSize: $pageSize, page: $page) {
      results {
        mid
        ... on MediaNexxVideo {
          fieldNexxId
          fieldPreviewImage {
            entity {
              ... on MediaImage {
                fieldMediaImage {
                  entity {
                    url
                  }
                }
              }
            }
          }
          reverseFieldVideosMedia {
            entities {
              ... on MediaGallery {
                reverseGalleriesGirlInfo {
                  entities {
                    entityId
                    entityLabel
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;
export const GET_FAV_VIDEOS_META = gql`
  {
    pbFavoritesVideos {
      count
    }
  }
`;
