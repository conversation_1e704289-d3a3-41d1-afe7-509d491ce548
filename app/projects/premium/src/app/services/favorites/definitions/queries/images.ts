import { gql } from 'apollo-angular';
import { IPreview } from 'projects/premium/src/app/models/preview';

export function MapFavImageResultToPreviews(data: {
  results: IFavImageResult[];
}): IPreview[] {
  return data.results.map((v): IPreview => {
    const girlInfo =
      v.reverseFieldMediaSlideshowMedia.entities[0]?.reverseGalleriesGirlInfo
        .entities[0];
    return {
      id: v.mid,
      // girlInfoId: girlInfo?.entityId,
      // girlId: v.entityId,
      text: girlInfo?.entityLabel,
      title: girlInfo?.fieldCategory?.[0]?.entity?.entityLabel,
      image: v.fieldMediaImage.entity.url,
      // subtitle: girlInfo?.fieldCategory?.entity?.entityLabel,
      fieldPlusAccess: girlInfo?.fieldPlusAccess,
      link: ['/girl-info', girlInfo?.entityId, v.mid],
    };
  });
}

export interface IFavImageResult {
  mid: number;
  fieldMediaImage: {
    entity: {
      url: string;
    };
  };
  reverseFieldMediaSlideshowMedia: {
    entities: {
      reverseGalleriesGirlInfo: {
        entities: {
          entityId: number;
          entityLabel: string;
          fieldPlusAccess: boolean;
          fieldCategory: {
            entity: {
              entityLabel: string;
            };
          };
        }[];
      };
    }[];
  };
}

export const GET_FAV_IMAGES = gql`
  query GetFavoriteImagtes($pageSize: Int!, $page: Int!) {
    pbFavoritesImages(pageSize: $pageSize, page: $page) {
      results {
        mid
        ... on MediaImage {
          fieldMediaImage {
            entity {
              url
            }
          }
          reverseFieldMediaSlideshowMedia {
            entities {
              ... on MediaGallery {
                reverseGalleriesGirlInfo {
                  entities {
                    entityId
                    entityLabel
                    ... on GirlInfo {
                      fieldPlusAccess
                      fieldCategory {
                        entity {
                          entityLabel
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;
export const GET_FAV_IMAGES_META = gql`
  {
    pbFavoritesImages {
      count
    }
  }
`;
