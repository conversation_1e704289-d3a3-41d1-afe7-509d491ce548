import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, Signal, signal } from '@angular/core';

export interface Magazine {
  title?: string;
  url?: string;
  field_media_image?: string;
  field_archiv_image?: string;
}

@Injectable({
  providedIn: 'root',
})
export class MagazineService {
  private apiUrl = 'https://www.playboy.de/api/v1/issues'; // Replace with your API URL
  private _magazineIssues = signal<Magazine[]>([]);

  constructor(private http: HttpClient) {}

  getMagazineIssues(): void {
    const headers = new HttpHeaders({
      Accept: 'application/json',
    });

    this.http
      .get<Magazine[]>(this.apiUrl, { headers })
      .subscribe((response) => {
        if (response.length > 12) {
          response = response.slice(0, 12);
        }
        this._magazineIssues.set(response);
      });
  }

  get magazineIssues(): Signal<Magazine[]> {
    return this._magazineIssues;
  }
}
