import { isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { BehaviorSubject, timer } from 'rxjs';
import { distinctUntilChanged, filter, map, switchMap } from 'rxjs/operators';
import { ApiService } from './api.service';

@Injectable({ providedIn: 'root' })
export class CdnService {
  private tokenLoadedSubject = new BehaviorSubject(false);

  public $loaded = this.tokenLoadedSubject.pipe(
    distinctUntilChanged((a, b) => a === b),
  );
  public $url = this.tokenLoadedSubject.pipe(
    filter((v) => !!v),
    map(() => CdnService.url),
    distinctUntilChanged((a, b) => a === b),
  );

  public static url: string;

  constructor(api: ApiService, @Inject(PLATFORM_ID) platformId: Object) {
    if (isPlatformBrowser(platformId)) {
      // Every 9 minutes
      timer(0, 1000 * 60 * 9)
        .pipe(
          switchMap(() =>
            api.get<{
              data: string;
            }>('image/token/json', { endpoint: 'base' }),
          ),
        )
        .subscribe(
          ({ data }) => {
            CdnService.url = data;
            this.tokenLoadedSubject.next(true);
          },
          () => {
            this.tokenLoadedSubject.next(true);
          },
        );
    }
  }
}
