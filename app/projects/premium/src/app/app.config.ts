import { APP_ID, ApplicationConfig, inject, LOCALE_ID } from '@angular/core';
import { constants } from '@pb/ui';
import { SsrCookieService as CookieService } from 'ngx-cookie-service-ssr';
import { provideHttpClient, withFetch } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, withComponentInputBinding } from '@angular/router';
import { routes } from './app.routes';
import { provideApollo } from 'apollo-angular';
import { HttpLink } from 'apollo-angular/http';
import { environment } from '../environments/environment';
import { InMemoryCache } from '@apollo/client/core';
import {
  provideClientHydration,
  withEventReplay,
} from '@angular/platform-browser';

export const appConfig: ApplicationConfig = {
  providers: [
    { provide: APP_ID, useValue: 'serverApp' },
    { provide: LOCALE_ID, useValue: 'de' },
    { provide: constants.UI_THEME, useValue: constants.Theme.Dark },
    CookieService,
    provideHttpClient(withFetch()),
    provideAnimations(),
    provideRouter(routes, withComponentInputBinding()),
    provideApollo(() => {
      const httpLink = inject(HttpLink);
      return {
        link: httpLink.create({
          uri: environment.graph,
          withCredentials: true,
        }),
        cache: new InMemoryCache(),
      };
    }),
    provideClientHydration(withEventReplay()),
  ],
};
