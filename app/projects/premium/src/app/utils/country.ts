import { FilterValue } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { ICategoryQueryFilters } from '../services/girl-info/models';

export enum CountryOption {
  DE = 'Deutschland',
  NotDE = 'International',
}

const DE_ID = '10835';

export const COUNTRY_FILTER_OPTIONS: FilterValue[] = [
  { title: 'Deutschland', value: CountryOption.DE },
  { title: 'International', value: CountryOption.NotDE },
];

export function GenerateCountryParam(option: CountryOption): {
  [key: string]: { value: string };
} {
  return {
    [option === CountryOption.DE ? 'countryIs' : 'countryIsNot']: {
      value: DE_ID,
    },
  };
}
