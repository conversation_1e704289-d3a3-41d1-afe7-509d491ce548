import moment from 'moment';
import { MomentInput } from 'moment';

export const NEW_TIMESTAMP = moment().subtract(2, 'days');
export const NEW_TIMESTAMP_DATE = NEW_TIMESTAMP.toDate();
export const NEW_TIMESTAMP_DB = NEW_TIMESTAMP_DATE.toISOString()
  .slice(0, 19)
  .replace('T', ' ');

export function IsNew(input: MomentInput): boolean {
  return !!input && Math.abs(moment(input).diff(moment(), 'days')) <= 2;
}

export function IsNewFromTimestamp(releaseTimestamp: number): boolean {
  if (!releaseTimestamp) {
    return false;
  }

  const releaseTimestampDate = new Date(releaseTimestamp * 1000);
  const now = new Date();
  const diffInMs = Math.abs(now.getTime() - releaseTimestampDate.getTime());
  const diffInDays = diffInMs / (1000 * 60 * 60 * 24);

  return diffInDays <= 2;
}
