export const getFocalPointByImageIndex = (
  item: {
    fieldMainFocalPointX?: number;
    fieldMainFocalPointY?: number;
    fieldSecondFocalPointX?: number;
    fieldSecondFocalPointY?: number;
    fieldThirdFocalPointX?: number;
    fieldThirdFocalPointY?: number;
  },
  index: number,
) => {
  if (index === 0) {
    return {
      x: item.fieldMainFocalPointX,
      y: item.fieldMainFocalPointY,
    };
  }
  if (index === 1) {
    return {
      x: item.fieldSecondFocalPointX,
      y: item.fieldSecondFocalPointY,
    };
  }
  if (index === 2) {
    return {
      x: item.fieldThirdFocalPointX,
      y: item.fieldThirdFocalPointY,
    };
  }
  return undefined;
};
