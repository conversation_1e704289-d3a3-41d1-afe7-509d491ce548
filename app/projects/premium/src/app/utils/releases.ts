import { FilterValue } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';

const years = [
  [2020, 2025],
  [2010, 2019],
  [2000, 2009],
  [1990, 1999],
  [1980, 1989],
  [1970, 1979],
  [1960, 1969],
  [1953, 1959],
];

export const RELEASES_FILTER_OPTIONS: FilterValue[] = years.map((v) => ({
  title: v.join(' - '),
  value: v.join('-'),
}));

export function GetReleaseYearFilter(
  input?: string | undefined,
): { min: string; max: string } | undefined {
  const [min, max] = input.split('-');
  if (!min || !max) {
    return undefined;
  }
  return { min, max };
}
