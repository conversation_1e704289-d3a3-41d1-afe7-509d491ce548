import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged, first, map } from 'rxjs/operators';

export class CachedBehav<Data extends any> extends BehaviorSubject<Data> {
  constructor(key: string, fetchIfEmpty?: Observable<Data>) {
    super(JSON.parse(localStorage.getItem(`playboy-premium-${key}`)));
    if (!this.getValue() && fetchIfEmpty) {
      fetchIfEmpty.pipe(first()).subscribe((v) => this.next(v));
    }
    this.asObservable()
      .pipe(
        map((v) => JSON.stringify(v)),
        distinctUntilChanged((a, b) => a === b),
      )
      .subscribe((v) => localStorage.setItem(`playboy-premium-${key}`, v));
  }
}
