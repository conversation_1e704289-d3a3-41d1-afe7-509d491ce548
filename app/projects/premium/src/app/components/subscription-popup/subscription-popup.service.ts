import { inject, Injectable } from '@angular/core';
import {
  SubscriptionPopupComponent,
  SubscriptionPopupData,
} from './subscription-popup.component';
import { Dialog } from '@angular/cdk/dialog';

export const SubscriptionPopupAllAccessData = {
  title: 'All Access',
  text: 'Sie wollen mehr sehen?',
  list: [
    '250.000+ exklusive Fotos und Videos',
    'Internationale Coverstars',
    '70 Jahre Playboy-Geschichte',
    'inkl. E-Paper-Archiv mit 250+ Ausgaben',
  ],
  priceMonthly: '4,99 €',
  priceYearly: '3,99 €',
  productMonthly: 'O_PPD1ZUWN9ZETQBDB6L',
  productYearly: 'O_KE3WYQEKAQ9DQE91LB',
  images: [],
  light: false,
};

const SubscriptionPopupPlusData = {
  title: '+Plus',
  text: 'Sie wollen mehr sehen?',
  list: [
    'Alle Fotos und Videos der Coverstars und Playmates der letzten 3 Ausgaben',
    'Die letzten 3 Ausgaben als E-Paper',
    'Die vollständigen Galerien des Tages inkl. Archiv der aktuellen Woche',
    'Alle Stories auf Playboy.de',
  ],
  priceMonthly: '2,49 €',
  priceYearly: '1,99 €',
  productMonthly: 'O_46S08C5TLHAE7WCI6Q',
  productYearly: 'O_LGMT46PHI6PNTQNNEL',
  images: [],
  light: true,
};

@Injectable({
  providedIn: 'root',
})
export class SubscriptionPopupService {
  protected readonly dialog = inject(Dialog);

  /**
   * Opens  popup for all access or plus subscription
   */
  public open(
    plusAccess: boolean,
    partialData?: Partial<SubscriptionPopupData>,
  ): void {
    if (plusAccess) {
      this.openPlusSubscriptionPopup(partialData);
    } else {
      this.openAllAccessSubscriptionPopup(partialData);
    }
  }

  /**
   * open plus subscription popup
   */
  private openPlusSubscriptionPopup(
    partialData?: Partial<SubscriptionPopupData>,
  ): void {
    this.openPopup({
      ...SubscriptionPopupPlusData,
      ...partialData,
    });
  }

  /**
   * open plus subscription popup
   */
  private openAllAccessSubscriptionPopup(
    partialData?: Partial<SubscriptionPopupData>,
  ): void {
    this.openPopup({
      ...SubscriptionPopupAllAccessData,
      ...partialData,
    });
  }

  private openPopup(data: SubscriptionPopupData): void {
    const dialogRef = this.dialog.open(SubscriptionPopupComponent, {
      panelClass: 'subscription-popup',
      data,
      autoFocus: false,
    });
  }
}
