:host {
  display: contents;
  height: 100%;
  width: 100%;
}

:host .close-btn {
  outline: none;
}

:host .close-btn:focus-visible {
  outline: auto;
}

:host ul > li > svg {
  flex: 0 0 auto;
}

.popup-container {
  background: #1a1a1a;

  @media (min-width: 640px) {
    max-height: 95vh;
  }

  @media (min-width: 768px) {
    .popup-content {
      grid-template-rows: 1fr auto;
      /*@apply gap-3;*/
      grid-template-areas:
        "content images"
        "newAcc -";

      .existing-account {
        grid-area: newAcc;
        @apply my-3;
      }
    }
  }

  &.light {
    background: white;
    color: black;

    .toggle-button.active {
      border-color: black;
    }

    .logo-tile,
    .unlock-link {
      background: black;
      color: white;
    }

    .existing-account {
      color: black;
    }
  }
}

.image-grid {
  app-preload-image {
    border-radius: 4px;

    @media (min-width: 1024px) {
      border-radius: 8px;
    }
  }
}

.image-grid--desktop {
  app-preload-image:last-child {
    grid-column: 1 / -1;
  }
}

.toggle-button {
  outline: 1px solid #515151;
  border-color: transparent;
}

.toggle-button.active {
  border-color: white;
}

.unlock-button a {
  padding: 12px;
  width: 100%;
  display: block;
}

.existing-account {
  color: white;
  text-decoration: underline;
  cursor: pointer;
  width: 100%;
  font-size: 16px;
  letter-spacing: 0.05rem;

  font-family: "Inter", sans-serif;
  @supports (font-variation-settings: normal) {
    font-family: "Inter var", sans-serif;
  }
}
