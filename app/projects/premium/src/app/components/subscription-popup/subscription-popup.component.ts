import { Component, computed, inject, signal } from '@angular/core';
import { DIALOG_DATA, DialogRef } from '@angular/cdk/dialog';
import { PreloadImageComponent } from '@pb/ui';
import { SubscriptionPopupAllAccessData } from './subscription-popup.service';
import { AccountService } from '../../services/account.service';
import { AsyncPipe } from '@angular/common';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { PaywallImage } from '../../screens/model/screens/model/definitions/models';

export interface SubscriptionPopupData {
  title?: string;
  text?: string;
  list: Array<string>;
  images: Array<PaywallImage>;
  priceMonthly?: string;
  priceYearly?: string;
  productMonthly: string;
  productYearly: string;
  light?: boolean;
}

@Component({
  selector: 'app-subscription-popup',
  imports: [PreloadImageComponent, AsyncPipe],
  templateUrl: './subscription-popup.component.html',
  styleUrl: './subscription-popup.component.css',
  standalone: true,
})
export class SubscriptionPopupComponent {
  dialogRef = inject<DialogRef<void>>(DialogRef<void>);
  logoUrl = signal<string>('assets/logo/pb_all_ac_logo_gold.png');

  private readonly router = inject(Router);
  protected readonly returnURL = computed(() => {
    let specificCurrentPath = this.router?.url || '';
    if (specificCurrentPath === '/' || specificCurrentPath === '/home') {
      specificCurrentPath = '/frontend';
    }
    return environment.serverUrl + specificCurrentPath;
  });

  /**
   * Whether monthly or yearly price is selected
   */
  isMonthly = signal<boolean>(false);
  /**
   * Whether price is animating.
   */
  animatePrice = signal<boolean>(false);
  protected injectedData: SubscriptionPopupData = inject(DIALOG_DATA);
  protected accountService: AccountService = inject(AccountService);

  readonly frontendReturnUrl = this.returnURL();

  protected data: Required<SubscriptionPopupData> = {
    ...SubscriptionPopupAllAccessData,
    ...this.injectedData,
  };

  /**
   * Switch to monthly price.
   * @protected
   */
  protected switchToMonthly(): void {
    if (!this.isMonthly()) {
      this.triggerPriceAnimation();
      this.isMonthly.set(true);
    }
  }

  /**
   * Switch to yearly price.
   * @protected
   */
  protected switchToYearly(): void {
    if (this.isMonthly()) {
      this.triggerPriceAnimation();
      this.isMonthly.set(false);
    }
  }

  /**
   * Trigger price animation.
   * @private
   */
  private triggerPriceAnimation(): void {
    this.animatePrice.set(true);
    setTimeout(() => {
      this.animatePrice.set(false);
    }, 500);
  }
}
