import {
  AfterViewInit,
  Component,
  ElementRef,
  forwardRef,
  input,
  output,
  Signal,
  signal,
  viewChild,
  WritableSignal,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormGroup,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';

type InputProps = {
  formGroup: FormGroup;
  type: 'text' | 'password' | 'number';
  value: string;
  label: string;
  formControlName: string;
  placeholder: string;
  checkValidation: boolean;
};

@Component({
  selector: 'app-search-input',
  imports: [FormsModule, ReactiveFormsModule],
  templateUrl: './search-input.component.html',
  styleUrl: './search-input.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SearchInputComponent),
      multi: true,
    },
  ],
})
export class SearchInputComponent
  implements ControlValueAccessor, AfterViewInit
{
  public readonly placeholder = input<InputProps['placeholder']>();
  public type = input<InputProps['type']>('text');
  readonly externalMode = input<boolean>(false);
  readonly autofocus = input<boolean>(true);
  readonly triggerSearch = output<string>();
  touched = false;
  disabled = false;
  title = viewChild<ElementRef>('title');
  protected _value: WritableSignal<any> = signal(null);

  private _inputElement = viewChild<ElementRef<HTMLInputElement>>('val');

  get inputElement(): Signal<ElementRef<HTMLInputElement>> {
    return this._inputElement;
  }

  set inputElement(value: Signal<ElementRef<HTMLInputElement>>) {
    this._inputElement = value;
  }

  ngAfterViewInit(): void {
    if (this.autofocus() && this._inputElement()) {
      this._inputElement().nativeElement.focus();
    }
  }

  onChange = (value: any) => {};

  onTouched = () => {};

  writeValue(value: any) {
    if (value !== undefined) {
      this._value.set(value);
    }
  }

  valueChanged(val: any) {
    this.onChange(val);
  }

  registerOnChange(onChange: any) {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: any) {
    this.onTouched = onTouched;
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  setDisabledState(disabled: boolean) {
    this.disabled = disabled;
  }
}
