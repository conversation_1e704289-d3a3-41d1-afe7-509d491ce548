<div class="flex gap-2 flex-row">
  <input
    #val
    (focus)="markAsTouched()"
    (input)="valueChanged(val.value)"
    [disabled]="disabled"
    [placeholder]="placeholder()"
    [type]="type()"
    [value]="_value()"
    class="plain w-full h-auto border bg-transparent text-sm text-gray-400 border-gray-400 p-4 outline-none"
  />

  <button
    (click)="triggerSearch.emit(val.value)"
    [attr.type]="externalMode() ? 'button' : 'submit'"
    class="p-4 rounded bg-golden text-sm text-white tracking-w25"
  >
    Suchen
  </button>
</div>
