import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { map, Observable } from 'rxjs';

import { AccountService } from '../services/account.service';

@Injectable({
  providedIn: 'root',
})
export class LoggedInGuard {
  constructor(
    private acc: AccountService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> {
    // get logged status
    return this.acc.Subscribed.pipe(
      map((subscribed) => {
        // if not logged, check where to redirect
        if (!subscribed) {
          let url = ['/'];
          let queryParamsObject: Record<string, string> | null = null;

          // Extract path and query parameters separately
          const [pathname, queryString] = state.url.split('?');
          const params = pathname.split('/');

          // Parse query parameters if they exist
          if (queryString) {
            queryParamsObject = Object.fromEntries(
              new URLSearchParams(queryString),
            );
          }

          // if girl or girl info was requested, go to public girl
          if (route.routeConfig?.path == 'girl') {
            if (params.length === 4) {
              url = [`/p/girl/info/${params[3]}`];
            } else if (params.length === 3) {
              url = [`/p/girl/${params[2]}`];
            }
          } else if (route.routeConfig?.path == 'categories') {
            if (params.length === 3) {
              const category = decodeURIComponent(params[2]);
              url = [`/p/categories/${category}`];
            }
          } else if (route.routeConfig?.path == 'search') {
            url = [`/p/search`];
          } else if (route.routeConfig?.path == ':modular_url') {
            url = [`/p/${params[1]}`];
          }

          return this.router.createUrlTree(url, {
            queryParams: queryParamsObject,
          });
        }
        return subscribed;
      }),
    );
  }
}
