import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { combineLatest, Observable } from 'rxjs';

import { AccountService } from '../services/account.service';

@Injectable({
  providedIn: 'root',
})
export class SubscribedGuard {
  constructor(
    private acc: AccountService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> {
    return combineLatest([this.acc.Subscribed, this.acc.LoggedIn]).pipe(
      map(([subscribed, loggedIn]) =>
        loggedIn && !subscribed
          ? true // logged in but not subscribed user can also view
          : true,
      ),
    );
  }
}
