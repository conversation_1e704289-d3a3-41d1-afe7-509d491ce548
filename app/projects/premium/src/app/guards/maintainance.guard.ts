import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { MaintainanceService } from '../services/maintainance.service';

@Injectable({
  providedIn: 'root',
})
export class MaintainanceGuard {
  constructor(
    private maintain: MaintainanceService,
    private router: Router,
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> {
    return this.maintain.$isMaintainance.pipe(
      map((maintainance) =>
        maintainance ? this.router.createUrlTree(['/maintainance']) : true,
      ),
    );
  }
}
