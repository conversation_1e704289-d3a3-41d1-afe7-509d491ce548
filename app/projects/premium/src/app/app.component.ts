import { Component, Inject, OnDestroy, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivationStart, Router, RouterOutlet } from '@angular/router';
import { INavbarUrl, NavbarComponent } from '@pb/ui';
import { environment } from 'projects/premium/src/environments/environment';
import { combineLatest, Observable, of } from 'rxjs';
import {
  distinctUntilChanged,
  filter,
  map,
  switchMap,
  tap,
} from 'rxjs/operators';

import { AsyncPipe, isPlatformBrowser } from '@angular/common';
import { AccountService } from './services/account.service';
import { CategoriesService } from './services/categories/categories.service';
import { CdnService } from './services/cdn.service';
import { MaintainanceService } from './services/maintainance.service';
import { CategoryNameToSlug } from './shared/pipes/category-slug.pipe';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  imports: [NavbarComponent, RouterOutlet, AsyncPipe],
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'premium';
  isBrowser: boolean;

  $linksLeft: Observable<INavbarUrl[]>;

  linksRight: Observable<INavbarUrl[]> = combineLatest([
    this.promo.$isMaintainance,
    this.accountService.Subscribed,
    this.accountService.LoggedIn,
    this.accountService.Admin,
  ]).pipe(
    map(([isMaintainance, isSubscribed, isLoggedIn, isAdmin]): INavbarUrl[] => {
      if (isMaintainance) {
        return [];
      }
      if (!isLoggedIn) {
        return [
          {
            href: environment.loginUrl,
            title: 'Einloggen',
            color: 'golden',
          },
        ];
      }
      const links: INavbarUrl[] = [
        {
          href: 'https://www.playboy.de/archiv',
          title: 'E-paper',
          color: 'golden',
          news: true,
          children: [
            {
              routerLink: 'e-paper',
              title: 'Playboy Magazin',
              color: 'white',
              news: true,
            },
            {
              routerLink: 'e-paper-special',
              title: 'Special Edition',
              color: 'white',
              news: true,
            },
          ],
        },
      ];

      if (isSubscribed) {
        links.push({
          routerLink: 'favorites',
          title: 'Favoriten',
          color: 'golden',
        });
      }
      if (isAdmin) {
        links.push({
          href: 'https://api.premium.playboy.de/',
          title: 'Admin',
          color: 'golden',
        });
      }
      if (isLoggedIn) {
        links.push({
          href: environment.logoutUrl,
          title: 'Logout',
          color: 'golden',
        });
      }
      return links;
    }),
  );

  showNavBar = true;

  public $loading: Observable<boolean>;

  constructor(
    private cdnService: CdnService,
    router: Router,
    public readonly accountService: AccountService,
    private readonly promo: MaintainanceService,
    categories: CategoriesService,
    @Inject(PLATFORM_ID) platformId: Object,
  ) {
    this.isBrowser = isPlatformBrowser(platformId);

    if (this.isBrowser) {
      this.$loading = combineLatest([
        accountService.Subscribed,
        cdnService.$loaded,
      ]).pipe(
        map(([subscribed, loaded]) => subscribed && !loaded),
        distinctUntilChanged((a, b) => a === b),
      );
      router.events
        .pipe(
          filter((v) => v instanceof ActivationStart),
          map((v: ActivationStart) => v.snapshot.data),
          map((v) => v.navbar !== false),
          distinctUntilChanged((a, b) => a === b),
        )
        .subscribe((v) => (this.showNavBar = v));
      router.events
        .pipe(
          filter((v) => v instanceof ActivationStart),
          map((v: ActivationStart) => v.snapshot.data),
          tap(console.log),
          filter((v) => !v?.stayScrolled),
        )
        .subscribe(() => window.scrollTo({ top: 0, behavior: 'smooth' }));
      this.$linksLeft = combineLatest([
        this.promo.$isMaintainance,
        this.accountService.Subscribed,
      ]).pipe(
        switchMap(([inMaintainance, subscribed]) =>
          inMaintainance
            ? of([])
            : categories.Categories.pipe(
                map((v) => {
                  const links = [
                    ...v.map((v) => ({
                      routerLink: `${!subscribed ? 'p/' : ''}categories/${CategoryNameToSlug(v.entityLabel)}`,
                      title: v.entityLabel,
                      children: v.children.map((c) => ({
                        routerLink: `${!subscribed ? 'p/' : ''}categories/${CategoryNameToSlug(c.entityLabel)}`,
                        title: c.entityLabel,
                      })),
                    })),
                  ];

                  if (subscribed) {
                    links.push({
                      routerLink: 'videos',
                      title: 'Videos',
                      children: [],
                    });
                    links.push({
                      routerLink: 'premium-tv',
                      title: 'Playboy TV',
                      children: [],
                    });
                  }

                  return links;
                }),
              ),
        ),
      );
    }
  }

  private checkSessionInterval?: number;

  ngOnInit(): void {
    this.accountService.checkSession();

    this.checkSessionInterval = window.setInterval(
      () => {
        this.accountService.checkSession();
      },
      10 * 60 * 1000,
    );
  }

  ngOnDestroy(): void {
    if (this.checkSessionInterval) {
      clearInterval(this.checkSessionInterval);
    }
  }
}
