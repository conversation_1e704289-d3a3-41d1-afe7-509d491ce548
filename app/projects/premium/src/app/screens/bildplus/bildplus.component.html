<div class="container mb-52">
  <div class="border-2 border-golden hero relative mx-auto w-full">
    <!-- <img class="absolute top-4 left-4 h-6 md:h-15" src="assets/bildplus/playboy-premium-bildplus-logo.png"> -->
    <img
      class="absolute top-0 right-0 h-8 md:h-14"
      src="assets/bildplus/bildplus-logo.png"
    />
    <img
      class="absolute hero-image -top-4 -inset-x-4 max-w-max md:max-w-full object-contain md:object-top md:left-1/12"
      [src]="
        'assets/bildplus/hero' +
        (('md' | responsive | async) ? '' : '-mobile') +
        '.png'
      "
    />
  </div>
</div>

<h2 class="text-center px-4 w-5/6 mx-auto max-w-4xl">
  Aktivieren Sie jetzt Ihr PLAYBOYPREMIUM Jahresabo
</h2>

<p class="text-center max-w-3xl mx-auto px-4 font-garamond font-light mt-10">
  Registrieren Sie sich jetzt in nur wenigen Minuten. <br />
  Anschließend können Sie PlayboyPremium kostenlos nutzen.
</p>

<div class="flex justify-center my-15">
  <a [href]="loginUrl">
    <lib-button>Jetzt Kostenloses Konto Eröffnen</lib-button>
  </a>
</div>

<section class="flex flex-row justify-center">
  @if (packages[0]; as package) {
    <app-package
      [single]="true"
      [package]="package"
      (onSelection)="selectedPackageIndex = i"
      [selected]="selectedPackageIndex === i"
    ></app-package>
  }
  <!-- <app-package *ngFor="let package of packages; let i = index" [package]="package"
  (onSelection)="selectedPackageIndex = i" [selected]="selectedPackageIndex === i"></app-package> -->
</section>

<p class="my-15 font-garamond px-4 w-5/6 mx-auto">
  1) alle Preise inkl. gesetzlich gültiger MwSt.<br /><br />
  2) Der PlayboyPremium Gutschein berechtigt Sie zur Nutzung von PlayboyPremium
  für 12 Monate. Sie können den Gutschein im letzten Schritt des
  Bestellprozesses einlösen. Die Laufzeit von 12 Monaten für PlayboyPremium
  beginnt in jedem Fall erst mit Einlösung des Gutscheins. Sollte PlayboyPremium
  zum Ablauf der 12 Monate nicht rechtzeitig gekündigt werden, verlängert sich
  die Laufzeit hierfür um den entsprechenden Zeitraum und zum Preis von 113,88
  €. BILDplus und PlayboyPremium müssen jeweils getrennt gekündigt werden. Der
  Gutscheincode von PlayboyPremium ist nicht übertragbar und darf nicht separat
  verkauft werden. Der Gutscheincode ist bis zum 31.12.2022 einlösbar. Bei
  Nutzung des Gutscheins gelten die
  <a
    class="text-blue-500 underline hover:no-underline"
    [routerLink]="['/pages/terms']"
    >hier</a
  >
  abrufbaren AGB.
</p>

<section class="grid place-content-center text-center my-15">
  <p class="text-4xl font-bauer">Nur bei</p>
  <img src="assets/logo/golden-logo-with-text_new.svg" />
</section>

<section>
  <div
    class="container grid md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 font-stag text-2xl"
  >
    @for (feature of gridFeatures; track feature) {
      <div class="flex flex-col">
        <div
          class="w-full bg-cover h-80 grid place-content-center"
          style="background-image: url(&quot;assets/bg-pattern.jpg&quot;)"
        >
          <img [src]="feature.icon" />
        </div>
        <div class="bg-gray-800 flex-1 p-6">
          <hr class="border-golden mb-6" style="max-width: 60px" />
          <p [innerHTML]="feature.name"></p>
        </div>
      </div>
    }
  </div>
</section>
