import { map, filter, switchMap } from 'rxjs/operators';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { IPackage, PackagesService } from '../../services/packages.service';
import { PromoService } from '../../services/promo.service';
import { environment } from 'projects/premium/src/environments/environment';
import { ResponsivePipe } from '@pb/ui/pipes/responsive.pipe';
import { AsyncPipe } from '@angular/common';
import { ButtonComponent } from '@pb/ui/components/button/button.component';
import { PackageComponent } from '../../shared/components/package/package.component';

@Component({
  templateUrl: './bildplus.component.html',
  styleUrls: ['./bildplus.component.css'],
  imports: [
    ResponsivePipe,
    AsyncPipe,
    ButtonComponent,
    PackageComponent,
    RouterLink,
  ],
})
export class BildplusComponent implements OnDestroy {
  loginUrl = environment.loginUrl;

  public packages: IPackage[];
  public gridFeatures: {
    icon: string;
    name: string;
  }[];

  selectedPackageIndex = -1;

  private packagesSub: Subscription;
  private sub: Subscription;
  constructor(pS: PackagesService, promo: PromoService, route: ActivatedRoute) {
    this.packagesSub = route.paramMap
      .pipe(
        map((v) => v.get('coupon')),
        filter((v) => !!v),
        switchMap((coupon) => pS.getPackagesForCoupon(coupon)),
      )
      .subscribe((p) => (this.packages = p));

    this.sub = promo.Data.subscribe(
      ({ headerGalleries, ads, previewVideo, gridFeatures }) => {
        this.gridFeatures = gridFeatures;
      },
    );
  }

  ngOnDestroy(): void {
    this.packagesSub?.unsubscribe();

    this.sub?.unsubscribe();
  }
}
