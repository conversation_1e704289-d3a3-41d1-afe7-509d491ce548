import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { CategoriesService } from '../../../services/categories/categories.service';

@Injectable({
  providedIn: 'root',
})
export class CategoryExistsGuard {
  constructor(private catServie: CategoriesService) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> | boolean {
    if (!route.paramMap.has('category_name')) {
      return;
    }
    return this.catServie
      .getBySlug(route.paramMap.get('category_name') || '_')
      .pipe(map((v) => !!v));
  }
}
