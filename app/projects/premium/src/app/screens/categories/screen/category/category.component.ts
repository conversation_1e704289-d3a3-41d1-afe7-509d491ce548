import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import {
  BreadcrumbsComponent,
  distinctJSON,
  FilterDropdownComponent,
} from '@pb/ui';
import {
  IFilterOptions,
  IFilterSelection,
} from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { $ShownPerScreen } from '@pb/ui/utils/screen';
import { IPreview } from 'projects/premium/src/app/models/preview';
import {
  CategoriesService,
  ICategory,
} from 'projects/premium/src/app/services/categories/categories.service';
import {
  GirlInfoService,
  IGirlInfoServiceData,
  SortOptions,
} from 'projects/premium/src/app/services/girl-info/girl-info.service';
import { GenerateMainQueryParams } from 'projects/premium/src/app/services/girl-info/queries';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  skip,
  switchMap,
} from 'rxjs/operators';

import {
  ICategoryFilterOptions,
  ICategoryQueryVariableSort,
} from '../../../../services/girl-info/models';
import { AsyncPipe, Location } from '@angular/common';
import { CategorySlugPipe } from '../../../../shared/pipes/category-slug.pipe';
import { GalleryCardComponent } from '../../../../shared/components/gallery-card/gallery-card.component';
import { PaginationGridComponent } from '../../../../shared';

@Component({
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.css'],
  imports: [
    AsyncPipe,
    BreadcrumbsComponent,
    CategorySlugPipe,
    FilterDropdownComponent,
    RouterLink,
    GalleryCardComponent,
    PaginationGridComponent,
  ],
})
export class CategoryComponent implements OnInit, OnDestroy {
  $categoryName: Observable<string>;
  $category: Observable<ICategory>;
  sortingOptions: SortOptions[] = [];
  $filterOptions: Observable<IFilterOptions>;
  $modelData: Observable<IGirlInfoServiceData>;
  $models: Observable<IPreview[]>;
  $pageAmount: Observable<number>;
  $loading: Observable<boolean>;
  $headerGalleries: Observable<IPreview[]>;
  $page: Observable<number>;
  $nextPage: Observable<number>;
  private selectedSorting = new BehaviorSubject<SortOptions | undefined>(
    undefined,
  );
  public $selectedSorting = this.selectedSorting.pipe(distinctJSON());
  private selectedFilter = new BehaviorSubject<Partial<ICategoryFilterOptions>>(
    {},
  );
  public $selectedFilter = this.selectedFilter.pipe(distinctJSON());
  private $activeSorting: Observable<ICategoryQueryVariableSort>;
  private categorySub: Subscription;
  private titleSub: Subscription;
  private paramsSub: Subscription;

  constructor(
    route: ActivatedRoute,
    public readonly router: Router,
    categoriesService: CategoriesService,
    titleService: Title,
    private location: Location,
    private meta: Meta,
    girlService: GirlInfoService,
  ) {
    this.$activeSorting = girlService.mapSortingObservable(
      this.$selectedSorting,
    );
    this.sortingOptions = girlService.sortingOptions;
    this.$page = route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );

    this.$categoryName = route.paramMap.pipe(
      map((v) => v.get('category_name')),
      filter((v) => !!v),
      distinctJSON(),
    );

    this.$category = this.$categoryName.pipe(
      switchMap((v) => categoriesService.getBySlug(v)),
      distinctJSON(),
    );

    this.titleSub = this.$category
      .pipe(
        map((v) =>
          [v.entityLabel[0].toUpperCase(), ...v.entityLabel.slice(1)].join(''),
        ),
        distinctUntilChanged((a, b) => a === b),
      )
      .subscribe((v) => titleService.setTitle(`${v} | Playboy All Access`));

    this.$filterOptions = girlService.$filterOptions;

    const $params = combineLatest([
      this.$selectedFilter,
      this.$activeSorting,
      this.$category.pipe(
        map((v) => [v.entityId, ...(v.children || []).map((v) => v.entityId)]),
        distinctJSON(),
      ),
    ]).pipe(
      distinctJSON(),
      map((d) => GenerateMainQueryParams(...d)),
      distinctJSON(),
    );

    this.paramsSub = $params.pipe(skip(1)).subscribe(() => (this.Page = 0));

    this.$modelData = girlService.getGirlInfoPreviews(
      combineLatest([$ShownPerScreen, this.$page, $params]).pipe(
        map(([perScreen, screen, params]) => [{ perScreen, screen }, params]),
      ),
    );

    this.$pageAmount = combineLatest([$ShownPerScreen, this.$modelData]).pipe(
      filter(([_, v]) => !!v),
      map(([perScreen, { count }]) => Math.ceil(count / perScreen)),
      distinctUntilChanged((a, b) => a === b),
    );

    this.$loading = this.$modelData.pipe(map((v) => v.loading));

    this.$models = this.$modelData.pipe(
      map((v) => {
        return v.data.map(({ image, imageLowRes, ...a }) => ({
          ...a,
          image: imageLowRes,
        }));
      }),
      distinctJSON(),
    );

    this.$nextPage = combineLatest([
      this.$models.pipe(
        map((v) => v.length),
        distinctUntilChanged((a, b) => a === b),
        debounceTime(10),
      ),
      $ShownPerScreen,
    ]).pipe(
      map(([videoCount, perScreen]) => Math.ceil(videoCount / perScreen) + 1),
      distinctUntilChanged((a, b) => a === b),
    );

    this.$headerGalleries = this.$modelData.pipe(
      map((v) => v.data.slice(0, 4)),
      distinctJSON(),
    );
  }

  public set Page(page: number) {
    this.router.navigate([], {
      queryParams: { page },
      queryParamsHandling: 'merge',
    });
  }

  ngOnInit() {
    this.categorySub = this.$categoryName.subscribe(() =>
      window.scrollTo({ top: 0, behavior: 'smooth' }),
    );
    this.meta.removeTag(`name='og:site_name'`);
    this.meta.removeTag(`name='og:type'`);
    this.meta.removeTag(`name='og:title'`);
    this.meta.removeTag(`name='og:description'`);
    this.meta.removeTag(`name='og:locale'`);
    this.meta.removeTag(`name='og:url'`);
    this.meta.removeTag(`name='og:image'`);
    this.meta.removeTag(`name='article:published_time'`);
    this.meta.removeTag(`name='profile:first_name'`);
    this.meta.removeTag(`name='profile:last_name'`);
    this.meta.removeTag(`name='profile:gender'`);
    this.meta.removeTag(`name='description'`);
    this.meta.removeTag(`name='author'`);
    this.meta.removeTag(`name='meta'`);
    this.$categoryName.subscribe((categoryName) => {
      if (categoryName) {
        this.meta.addTag({ name: 'og:title', content: categoryName });
      }
    });
    this.meta.addTag({ name: 'og:site_name', content: 'Playboy All Access' });
    this.meta.addTag({ name: 'og:type', content: 'website' });
    this.meta.addTag({
      name: 'og:description',
      content:
        'Mit All Access erhalten Sie Zugriff auf über 250.000 Fotos und Videos sowie auf unser Playboy E-Paper-Archiv mit mehr als 250 Ausgaben. Jetzt anmelden und Vorteile genießen.',
    });
    this.meta.addTag({
      name: 'description',
      content:
        'Mit All Access erhalten Sie Zugriff auf über 250.000 Fotos und Videos sowie auf unser Playboy E-Paper-Archiv mit mehr als 250 Ausgaben. Jetzt anmelden und Vorteile genießen.',
    });
    this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
    const currentUrl = this.location.path();
    this.meta.addTag({
      name: 'og:url',
      content: `https://premium.playboy.de${currentUrl}`,
    });
  }

  ngOnDestroy() {
    this.categorySub?.unsubscribe();
    this.titleSub?.unsubscribe();
    this.paramsSub?.unsubscribe();
  }

  selectSorting(data: SortOptions): void {
    this.selectedSorting.next(data);
    this.Page = 0;
  }

  selectFilter(data: IFilterSelection): void {
    this.selectedFilter.next(data);
    this.Page = 0;
  }
}
