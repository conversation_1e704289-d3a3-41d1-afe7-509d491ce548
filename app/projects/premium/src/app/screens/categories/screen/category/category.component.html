@if ($category | async; as category) {
  <lib-breadcrumbs
    [breadcrumbs]="[
      {
        link: '/categories/' + (category | categorySlug),
        label: category.entityLabel,
      },
    ]"
  ></lib-breadcrumbs>
}

<h3
  class="w-full text-4xxl sm:text-5xl text-center p-4 my-10 md:mt-8 md:mb-15"
  id="title-proposal"
>
  {{ ($category | async)?.entityLabel }}
</h3>

<lib-filter-dropdown
  (selectedFilterChange)="selectFilter($event)"
  (selectedSortChange)="selectSorting($event)"
  [filterOptions]="$filterOptions | async"
  [selectedFilter]="$selectedFilter | async"
  [selectedSorting]="$selectedSorting | async"
  [sortingOptions]="sortingOptions"
>
</lib-filter-dropdown>

<ng-template
  #article
  let-focalPoint="focalPoint"
  let-id="id"
  let-image="image"
  let-link="link"
  let-model="item"
>
  <a [routerLink]="link" class="w-full flex cursor-pointer h-full max-h-full">
    <app-gallery-card
      [focalPoint]="focalPoint"
      [image]="image"
      [paywallImages]="model?.paywallImages || []"
      [isAAContent]="model?.fieldPlusAccess !== true"
      [previewData]="model"
      class="w-full"
    ></app-gallery-card>
  </a>
</ng-template>

@if ($models | async; as models) {
  <app-pagination-grid
    [paginationAnimationElementSelector]="'#title-proposal'"
    [items]="models"
    [page]="$page | async"
    [pages]="$pageAmount | async"
    [loading]="$loading | async"
    [template]="article"
  ></app-pagination-grid>
}
