import { FavoritesComponent } from './favorites.component';
import { Routes } from '@angular/router';
import { TypeComponent } from './type/type.component';

const routes: Routes = [
  {
    path: ':type',
    children: [
      {
        path: ':page',
        component: TypeComponent,
        data: { stayScrolled: true },
      },
      { path: '', redirectTo: '0', pathMatch: 'full' },
    ],
  },
  { path: '', component: FavoritesComponent },
];

export default routes;
