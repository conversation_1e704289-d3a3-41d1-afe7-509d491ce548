import { Component, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import {
  BreadcrumbsComponent,
  IBreadcrumb,
  RoutePaginationComponent,
} from '@pb/ui';
import { combineLatest, Observable, of, Subscription } from 'rxjs';
import { distinctUntilChanged, map, switchMap } from 'rxjs/operators';

import { IPreview } from '../../../models/preview';
import {
  FavoritesService,
  FavoriteType,
} from '../../../services/favorites/favorites.service';
import { AsyncPipe } from '@angular/common';
import { GalleryCardComponent } from '../../../shared';

@Component({
  templateUrl: './type.component.html',
  styleUrls: ['./type.component.css'],
  imports: [
    AsyncPipe,
    BreadcrumbsComponent,
    RouterLink,
    RoutePaginationComponent,
    GalleryCardComponent,
  ],
})
export class TypeComponent implements OnD<PERSON>roy {
  $headerItems: Observable<IPreview[]>;
  $items: Observable<IPreview[]>;
  $breadcrumb: Observable<IBreadcrumb>;
  $pageSize = of(16);
  $page: Observable<number>;
  $pageAmount: Observable<number>;
  $type: Observable<FavoriteType>;

  private sub: Subscription;

  constructor(activatedRoute: ActivatedRoute, favs: FavoritesService) {
    this.$page = activatedRoute.paramMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) || !v ? 0 : v)),
      distinctUntilChanged((a, b) => a === b),
    );

    this.$type = activatedRoute.paramMap.pipe(
      map((v) => v.get('type') as FavoriteType),
      distinctUntilChanged((a, b) => a === b),
    );

    this.$pageAmount = combineLatest([
      favs.getCountDynamic(this.$type),
      this.$pageSize,
    ]).pipe(
      map(([count, pageSize]) => Math.ceil(count / pageSize)),
      distinctUntilChanged((a, b) => a === b),
    );

    this.$items = combineLatest([this.$type, this.$page, this.$pageSize]).pipe(
      switchMap(([type, page, pageSize]) =>
        favs.getPreviews(type, { page, pageSize }),
      ),
    );
    this.$headerItems = this.$type.pipe(
      switchMap((type) => favs.getPreviews(type, { pageSize: 4 })),
    );
    this.$breadcrumb = activatedRoute.paramMap.pipe(
      map((v) => {
        switch (v.get('type') as FavoriteType) {
          case 'image':
            return { link: '', label: 'Bilder' };
          case 'girl-infos':
            return { link: '', label: 'Gallerien' };
          case 'girl':
            return { link: '', label: 'Women' };
          case 'video':
            return { link: '', label: 'Video' };
        }
      }),
    );
    // this.sub = this.;
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
  }
}
