<lib-breadcrumbs
  class="w-5/6 mx-auto mt-8 md:mt-0"
  [breadcrumbs]="[
    { link: '/favorites', label: 'Favoriten' },
    $breadcrumb | async,
  ]"
>
</lib-breadcrumbs>

<div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 gallery-list">
  @for (article of $items | async; track article) {
    <a class="flex" [routerLink]="article.link">
      <app-gallery-card
        class="w-full"
        [favoriteType]="'girl'"
        [paywallImages]="article?.paywallImages || []"
        [isAAContent]="article?.fieldPlusAccess !== true"
        [previewData]="article"
        [image]="article.publicImage"
      ></app-gallery-card>
    </a>
  }
</div>

@if (($pageAmount | async) > 1) {
  <app-route-pagination
    [baseRoute]="'/favorites/' + ($type | async)"
    [screenAmount]="$pageAmount | async"
    [screen]="$page | async"
  ></app-route-pagination>
}
