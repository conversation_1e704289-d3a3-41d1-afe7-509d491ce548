import { Component, OnInit } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { IPreview } from '../../models/preview';
import { AccountService } from '../../services/account.service';
import { FavoritesService } from '../../services/favorites/favorites.service';
import { BreadcrumbsComponent } from '@pb/ui';
import { AsyncPipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { GalleryCardComponent } from '../../shared';

@Component({
  selector: 'app-favorites',
  templateUrl: './favorites.component.html',
  styleUrls: ['./favorites.component.css'],
  imports: [
    AsyncPipe,
    BreadcrumbsComponent,
    RouterLink,
    GalleryCardComponent,
  ],
})
export class FavoritesComponent implements OnInit {
  $account = this.accService.Account;

  $girls: Observable<IPreview[]>;
  $girlInfos: Observable<IPreview[]>;
  $images: Observable<IPreview[]>;
  $videos: Observable<IPreview[]>;

  constructor(
    private readonly accService: AccountService,
    favoritesService: FavoritesService,
    titleService: Title,
    private meta: Meta,
  ) {
    titleService.setTitle('Favoriten | Playboy All Access');

    this.$girls = favoritesService.getPreviews('girl').pipe(
      map((v) => (v.length === 0 ? undefined : v)),
    );
    this.$girlInfos = favoritesService
      .getPreviews('girl-infos')
      .pipe(map((v) => (v.length === 0 ? undefined : v)));
    this.$images = favoritesService
      .getPreviews('image')
      .pipe(map((v) => (v.length === 0 ? undefined : v)));
    this.$videos = favoritesService
      .getPreviews('video')
      .pipe(map((v) => (v.length === 0 ? undefined : v)));
  }

  ngOnInit(): void {
    window.scroll({ top: window.innerHeight * 0.7, behavior: 'smooth' });
    this.meta.removeTag(`name='og:site_name'`);
    this.meta.removeTag(`name='og:type'`);
    this.meta.removeTag(`name='og:title'`);
    this.meta.removeTag(`name='og:description'`);
    this.meta.removeTag(`name='og:locale'`);
    this.meta.removeTag(`name='og:url'`);
    this.meta.removeTag(`name='og:image'`);
    this.meta.removeTag(`name='article:published_time'`);
    this.meta.removeTag(`name='profile:first_name'`);
    this.meta.removeTag(`name='profile:last_name'`);
    this.meta.removeTag(`name='profile:gender'`);
    this.meta.removeTag(`name='description'`);
    this.meta.removeTag(`name='author'`);
    this.meta.removeTag(`name='meta'`);

    this.meta.addTag({ name: 'og:site_name', content: 'Playboy All Access' });
    this.meta.addTag({ name: 'og:type', content: 'website' });
    this.meta.addTag({ name: 'og:title', content: 'Favoriten' });
    this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
    this.meta.addTag({
      name: 'og:url',
      content: 'https://premium.playboy.de/favorites',
    });
  }
}
