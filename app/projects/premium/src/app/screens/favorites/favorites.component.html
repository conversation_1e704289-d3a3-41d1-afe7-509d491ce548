<lib-breadcrumbs
  class="w-5/6 mx-auto mt-8 md:mt-0"
  [breadcrumbs]="[{ link: '/favorites', label: 'Favoriten' }]"
>
</lib-breadcrumbs>

<h2 class="my-20 w-full text-center"><PERSON><PERSON><PERSON></h2>

<section class="py-14">
  @if ($girls | async; as girls) {
    <article class="mb-15 w-full flex flex-col">
      <h3 class="text-center">Women</h3>
      @if (girls.length > 0) {
        <div
          class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 gallery-list"
        >
          @for (article of girls; track article; let index = $index) {
            @if (index <= 3) {
              <a class="flex" [routerLink]="article.link">
                <app-gallery-card
                  class="w-full"
                  [paywallImages]="article?.paywallImages || []"
                  [favoriteType]="'girl'"
                  [previewData]="article"
                  [image]="article.publicImage"
                ></app-gallery-card>
              </a>
            }
          }
        </div>
        <div
          class="flex mb-6 md:mb-8 lg:mb-12 mt-4 md:mt-6 lg:mt-8 px-3 md:px-0 mx-6 justify-center"
        >
          <a
            routerLink="/favorites/girl"
            class="uppercase font-inter text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
          >
            Alle anzeigen
          </a>
        </div>
      }
    </article>
  }

  @if ($girlInfos | async; as girlInfos) {
    <article class="mb-15 w-full flex flex-col">
      <h3 class="text-center">Galerien</h3>
      @if (girlInfos.length > 0) {
        <div
          class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 gallery-list"
        >
          @for (article of girlInfos; track article; let index = $index) {
            @if (index <= 3) {
              <a class="flex" [routerLink]="article.link">
                <app-gallery-card
                  class="w-full"
                  [paywallImages]="article?.paywallImages || []"
                  [isAAContent]="article?.fieldPlusAccess !== true"
                  [previewData]="article"
                  [image]="article.publicImage"
                ></app-gallery-card>
              </a>
            }
          }
        </div>
        <div
          class="flex mb-6 md:mb-8 lg:mb-12 mt-4 md:mt-6 lg:mt-8 px-3 md:px-0 mx-6 justify-center"
        >
          <a
            routerLink="/favorites/girl-infos"
            class="uppercase font-inter text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
          >
            Alle anzeigen
          </a>
        </div>
      }
    </article>
  }

  @if ($videos | async; as videos) {
    <article class="mb-15 w-full flex flex-col">
      <h3 class="text-center">Videos</h3>
      @if (videos.length > 0) {
        <div
          class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 gallery-list"
        >
          @for (article of videos; track article; let index = $index) {
            @if (index <= 3) {
              <a class="flex" [routerLink]="article.link">
                <app-gallery-card
                  class="flex w-full rounded-lg overflow-hidden"
                  [paywallImages]="article?.paywallImages || []"
                  [isAAContent]="article?.fieldPlusAccess !== true"
                  [previewData]="article"
                  [withoutInfos]="true"
                >
                </app-gallery-card>
              </a>
            }
          }
        </div>
        <div
          class="flex mb-6 md:mb-8 lg:mb-12 mt-4 md:mt-6 lg:mt-8 px-3 md:px-0 mx-6 justify-center"
        >
          <a
            routerLink="/favorites/video"
            class="uppercase font-inter text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
          >
            Alle anzeigen
          </a>
        </div>
      }
    </article>
  }


    @if ($images | async; as images) {
    <article class="mb-15 w-full flex flex-col">
      <h3 class="text-center">Bilder</h3>
      @if (images.length > 0) {
        <div
          class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-6 gallery-list"
        >
          @for (article of images; track article; let index = $index) {
            @if (index <= 3) {
              <a class="flex" [routerLink]="article.link">
                <app-gallery-card
                  class="flex w-full rounded-lg overflow-hidden"
                  [paywallImages]="article?.paywallImages || []"
                  [isAAContent]="article?.fieldPlusAccess !== true"
                  [previewData]="article"
                  [withoutInfos]="true"
                >
                </app-gallery-card>
              </a>
            }
          }
        </div>
        <div
          class="flex mb-6 md:mb-8 lg:mb-12 mt-4 md:mt-6 lg:mt-8 px-3 md:px-0 mx-6 justify-center"
        >
          <a
            routerLink="/favorites/image"
            class="uppercase font-inter text-sm tracking-widest p-5 inline-flex border border-white rounded-lg cursor-pointer justify-center w-max"
          >
            Alle anzeigen
          </a>
        </div>
      }
    </article>
  }
</section>
