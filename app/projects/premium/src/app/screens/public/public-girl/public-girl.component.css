:host {
  .single {
    @apply md:grid-cols-2;
  }
  .triple {
    @apply md:grid-cols-3;
  }
}

.new-btn {
  border: 1px solid #fff;
  display: flex;
  justify-content: center;
  padding: 10px;
  border-radius: 8px;
  line-height: normal;
  width: max-content;
  padding-right: 20px;
  cursor: pointer;
}

.grid img {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
  border-radius: 8px;
}

.btn.btn-new {
  @apply font-inter;
  --tw-border-opacity: 1;
  border-color: rgba(255, 255, 255, var(--tw-border-opacity));
  font-weight: 500;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity));
  letter-spacing: 2.5px;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.shooting-preview {
  border-radius: 8px;
  overflow: hidden;
}

.description {
  font-family: "Georgia";
}

.hero-image {
  height: 75vh;
}

@media only screen and (max-width: 767px) {
  .hero-image {
    height: 100% !important;
  }
}
