import { Subscription } from 'rxjs';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { environment } from 'projects/premium/src/environments/environment';
import { AsyncPipe, DatePipe, KeyValuePipe, NgClass } from '@angular/common';
import { AccountService } from '../../services/account.service';
import { RouterLink } from '@angular/router';
import {
  BreadcrumbsComponent,
  ButtonComponent,
  HeaderCarouselComponent,
  HeaderCarouselItemDirective,
} from '@pb/ui';

@Component({
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
  providers: [DatePipe],
  imports: [
    AsyncPipe,
    KeyValuePipe,
    NgClass,
    DatePipe,
    RouterLink,
    ButtonComponent,
    HeaderCarouselComponent,
    BreadcrumbsComponent,
    HeaderCarouselItemDirective,
  ],
})
export class ProfileComponent implements OnDestroy {
  isPopupVisible: boolean = false;
  readonly logoutURL = environment.logoutUrl;
  private sub: Subscription;
  unsubscribeStatus: undefined | 'loading' | 'success' | Error;
  myDate = new Date();
  public uuidToCancel: string;
  public environment = environment.serverUrl;

  constructor(
    public readonly accService: AccountService,
    private datePipe: DatePipe,
    titleService: Title,
  ) {
    titleService.setTitle(`Profil | Playboy All Access`);
    this.datePipe.transform(this.myDate, 'yyyy-MM-dd');
  }

  ngOnDestroy(): void {}
}
