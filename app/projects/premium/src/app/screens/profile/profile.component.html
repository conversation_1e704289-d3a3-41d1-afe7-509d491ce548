@if (accService.Account | async; as acc) {
  <div class="sm:h-screen-3/4 h-slider flex flex-col">
    <lib-header-carousel class="flex flex-1">
      <ng-template
        libHeaderCarouselItem
        image="assets/examples/landscape.jpg"
        title="Ihr Profil"
      >
        <PERSON><PERSON><PERSON><PERSON>, {{ acc.name }}
      </ng-template>
    </lib-header-carousel>
  </div>
  <lib-breadcrumbs
    class="w-5/6 mx-auto mt-8 md:mt-0"
    [breadcrumbs]="[{ link: '/profile', label: 'Profil' }]"
  >
  </lib-breadcrumbs>
  <section class="container pl-8 md:pl-16 lg:pl-20">
    <div class="mb-15">
      <!-- <h4 class="text-gray-400">Ihr Profil</h4> -->
      <h3 class="mt-6 md:mb-15 mb-8 text-4xl">Ihre Daten</h3>
      <p><b>Name:</b> {{ acc.name }}</p>
      <p><b>E-mail Adresse:</b> {{ acc.email }}</p>
      <p><b>Let<PERSON><PERSON> Login:</b> {{ acc.last_login }}</p>
    </div>
    <a [href]="logoutURL">
      <lib-button>Logout</lib-button>
    </a>
    <hr class="border-golden mt-14" />
  </section>
}
<section class="container pl-8 md:pl-16 lg:pl-20 mb-12 md:mb-24">
  <h3 class="mt-6 md:mb-15 mb-8 text-4xl">Ihre Abos</h3>
  <!-- Abo Table -->
  <div class="profileTable h-auto w-full max-w-7xl hidden lg:block">
    <!-- Header -->
    <div class="row bg-gray-700 flex-wrap p-3 flex">
      <div class="cell whitespace-nowrap">Aboplan</div>
      <div class="cell whitespace-nowrap">Status</div>
      <div class="cell big whitespace-nowrap"></div>
      <div class="cell date whitespace-nowrap">Startdatum</div>
      <div class="cell date whitespace-nowrap">Enddatum</div>
      <div class="cell big whitespace-nowrap">Nächste Rechnung</div>
      <div class="cell small whitespace-nowrap">Kosten</div>
      <div class="cell whitespace-nowrap">Bezahlmethode</div>
    </div>
    <!-- Body -->
    @if (accService.Account | async; as acc) {
      @for (item of acc.subscription_data | keyvalue; track item) {
        @for (subscription of item.value | keyvalue; track subscription) {
          <div
            attr.data-sectionvalue="{{ subscription.value.uuid }}"
            class="subTable row flex flex-wrap p-3 h-32 items-center"
            [ngClass]="{
              old:
                subscription.value.cancelDate &&
                myDate.getTime() >= subscription.value.cancelDateDate.getTime(),
            }"
          >
            <div class="cell pr-5">
              {{ subscription.value.name }}
            </div>
            @if (!subscription.value.cancelDate) {
              <div class="cell whitespace-nowrap">
                <div class="flex items-baseline">
                  <div class="w-2 h-2 mr-3 rounded-full bg-green-500"></div>
                  <div class="text-green-500 uppercase tracking-widest text-xs">
                    Aktiv
                  </div>
                </div>
              </div>
            }
            @if (
              subscription.value.cancelDate &&
              myDate.getTime() <= subscription.value.cancelDateDate.getTime()
            ) {
              <div class="cell whitespace-nowrap">
                <div class="flex items-baseline">
                  <div class="w-2 h-2 mr-3 rounded-full bg-red-500"></div>
                  <div class="text-red-500 uppercase tracking-widest text-xs">
                    Gekündigt
                  </div>
                </div>
              </div>
            }
            @if (
              subscription.value.cancelDate &&
              myDate.getTime() >= subscription.value.cancelDateDate.getTime()
            ) {
              <div class="cell whitespace-nowrap">
                <div class="flex items-baseline">
                  <div class="w-2 h-2 mr-3 rounded-full bg-gray-500"></div>
                  <div class="text-gray-500 uppercase tracking-widest text-xs">
                    Abgelaufen
                  </div>
                </div>
              </div>
            }
            @if (!subscription.value.cancelDate) {
              <div class="cell big whitespace-nowrap">
                <button
                  (click)="
                    isPopupVisible = true;
                    uuidToCancel = subscription.value.uuid
                  "
                  class="btnPopup px-6 py-2 border border-white uppercase tracking-widest text-xs m-auto block cursor-pointer"
                >
                  Kündigen
                </button>
              </div>
            }
            @if (subscription.value.cancelDate) {
              <div class="cell big"></div>
            }
            @if (subscription.value.startDate) {
              <div class="cell date whitespace-nowrap">
                {{ subscription.value.startDate | date }}
              </div>
            }
            @if (!subscription.value.startDate) {
              <div class="cell date"></div>
            }
            @if (subscription.value.cancelDate) {
              <div class="cell date whitespace-nowrap">
                {{ subscription.value.cancelDate | date }}
              </div>
            }
            @if (!subscription.value.cancelDate) {
              <div class="cell date"></div>
            }
            @if (!subscription.value.cancelDate) {
              <div class="cell big whitespace-nowrap">
                {{ subscription.value.invoice_end_dateDate | date }}
              </div>
            }
            @if (subscription.value.cancelDate) {
              <div class="cell big whitespace-nowrap"></div>
            }
            @if (subscription.value.amount) {
              <div class="cell small whitespace-nowrap">
                {{ subscription.value.amount + " €" }}
              </div>
            }
            @if (!subscription.value.amount) {
              <div class="cell small whitespace-nowrap"></div>
            }
            @if (subscription.value.paymentMethod !== "Stripe") {
              <div class="cell whitespace-nowrap">
                <div class="flex items-center">
                  <img class="mr-3" src="assets/paypal.svg" />
                  PayPal
                </div>
              </div>
            }
            @if (subscription.value.paymentMethod === "Stripe") {
              <div class="cell whitespace-nowrap">
                <div class="flex items-center">
                  <img class="mr-3" src="assets/credit-card.svg" />
                  Kreditkarte
                </div>
              </div>
            }
          </div>
        }
      }
    }
  </div>

  <!-- Mobile Table -->
  @if (accService.Account | async; as acc) {
    @for (item of acc.subscription_data | keyvalue; track item) {
      @for (subscription of item.value | keyvalue; track subscription) {
        <div
          class="row mobile flex-wrap justify-between mb-8 pb-8 p-3 flex lg:hidden block"
          [ngClass]="{
            old:
              subscription.value.cancelDate &&
              myDate.getTime() >= subscription.value.cancelDateDate.getTime(),
          }"
        >
          <div class="w-6/12 my-2 pr-5 title text-left">
            {{ subscription.value.name }}
          </div>
          @if (!subscription.value.cancelDate) {
            <div
              class="cell my-2 flex items-center whitespace-nowrap flex items-center"
            >
              <div class="flex items-baseline text-right">
                <div class="w-2 h-2 mr-3 rounded-full bg-green-500"></div>
                <div
                  class="text-green-500 uppercase tracking-widest text-xs title"
                >
                  Aktiv
                </div>
              </div>
            </div>
          }
          @if (
            subscription.value.cancelDate &&
            myDate.getTime() <= subscription.value.cancelDateDate.getTime()
          ) {
            <div class="cell whitespace-nowrap flex items-center">
              <div class="flex items-baseline text-right">
                <div class="w-2 h-2 mr-3 rounded-full bg-red-500"></div>
                <div
                  class="text-red-500 uppercase tracking-widest text-xs title"
                >
                  Gekündigt
                </div>
              </div>
            </div>
          }
          @if (
            subscription.value.cancelDate &&
            myDate.getTime() >= subscription.value.cancelDateDate.getTime()
          ) {
            <div class="cell whitespace-nowra flex items-center">
              <div class="flex items-baseline text-right">
                <div class="w-2 h-2 mr-3 rounded-full bg-gray-500"></div>
                <div
                  class="text-gray-500 uppercase tracking-widest text-xs title"
                >
                  Abgelaufen
                </div>
              </div>
            </div>
          }
          @if (!subscription.value.cancelDate) {
            <div class="cell whitespace-nowrap my-2 w-full text-left">
              <button
                (click)="
                  isPopupVisible = true; uuidToCancel = subscription.value.uuid
                "
                class="px-6 py-3 w-full border border-white uppercase tracking-widest text-xs cursor-pointer"
              >
                Kündigen
              </button>
            </div>
          }
          @if (!subscription.value.cancelDate) {
            <div class="cell"></div>
          }
          <div class="w-6/12 my-2 title text-left">Startdatum</div>
          @if (subscription.value.startDate) {
            <div
              class="cell date whitespace-nowrap flex items-center text-right"
            >
              {{ subscription.value.startDate | date }}
            </div>
          }
          @if (!subscription.value.startDate) {
            <div class="cell w-6/12"></div>
          }
          <div class="w-6/12 my-2 title text-left">Enddatum</div>
          @if (subscription.value.cancelDate) {
            <div class="cell whitespace-nowrap flex items-center text-right">
              {{ subscription.value.cancelDate | date }}
            </div>
          }
          @if (!subscription.value.cancelDate) {
            <div class="cell w-6/12"></div>
          }
          <div class="w-6/12 my-2 title text-left">Nächste Rechnung</div>
          @if (!subscription.value.cancelDate) {
            <div class="w-6/12 my-2 title text-right">
              {{ subscription.value.invoice_end_dateDate | date }}
            </div>
          }
          @if (subscription.value.cancelDate) {
            <div class="w-6/12 my-2 title text-right"></div>
          }
          <div class="w-6/12 my-2 title text-left">Kosten</div>
          @if (subscription.value.amount) {
            <div class="w-6/12 my-2 title text-right">
              {{ subscription.value.amount + " €" }}
            </div>
          }
          @if (!subscription.value.amount) {
            <div class="w-6/12 my-2 title"></div>
          }
          <div class="w-6/12 my-2 title text-left">Bezahlmethode</div>
          @if (subscription.value.paymentMethod !== "Stripe") {
            <div class="cell whitespace-nowrap flex items-center">
              <div class="flex items-center title">
                <img class="mr-3" src="assets/paypal.svg" />
                PayPal
              </div>
            </div>
          }
          @if (subscription.value.paymentMethod === "Stripe") {
            <div class="cell whitespace-nowrap flex items-center">
              <div class="flex items-center title">
                <img class="mr-3" src="assets/credit-card.svg" />
                Kreditkarte
              </div>
            </div>
          }
        </div>
      }
    }
  }

  <ng-template #notSubscribed>
    <a [routerLink]="['/subscription']">
      <lib-button> Neues Abo abschließen </lib-button>
    </a>
  </ng-template>

  <!-- <ng-container *ngIf="(accService.Subscribed | async); else notSubscribed">
  <lib-button *ngIf="unsubscribeStatus !== 'success'" [disabled]="!!unsubscribeStatus" (click)="CancelSubsription()">
    <ng-container [ngSwitch]="unsubscribeStatus" *ngIf="!!unsubscribeStatus">
      <ng-container *ngSwitchCase="'loading'">
        Anfrage wird gesendet…
      </ng-container>
      <ng-container *ngSwitchCase="'success'">
        Anfrage wurde gesendet!
      </ng-container>
      <ng-container *ngSwitchDefault>
        {{'Error: ' + unsubscribeStatus}}
      </ng-container>
    </ng-container>
    <ng-container *ngIf="!unsubscribeStatus">
      Abo Kündigen
    </ng-container>
  </lib-button>
  <p *ngIf="unsubscribeStatus === 'success'" class="text-golden">
    Kündigungsanfrage wurde gesendet!
    Vielen dank.
  </p>
</ng-container> -->
</section>

<!-- Popup -->
@if (isPopupVisible) {
  <div class="fixed w-full h-screen z-40 -inset-0 bg-black bg-opacity-80"></div>
}
@if (isPopupVisible) {
  <div
    class="popup absolute max-w-5xl left-1/2 top-1/2 transform -translate-y-2/4 -translate-x-2/4 bg-white md:w-3/4 w-11/12 z-50"
    style="position: fixed"
  >
    <div class="header pl-10 py-3 pr-5 flex justify-between bg-black">
      <img class="w-36" src="/assets/logo/<EMAIL>" />
      <a (click)="isPopupVisible = false" class="w-6 h-6 mt-2 cursor-pointer"
        ><img class="w-full" src="/assets/close.svg"
      /></a>
    </div>
    <div class="md:p-10 p-8">
      <h2 class="text-black">
        Sie sind im Begriff, Ihr Abo zu kündigen.<br />
        Sind Sie sicher?
      </h2>
      <p class="text-black mt-10 text-lg font-garamond font-light">
        Wenn Sie Ihr Abo kündigen, tritt diese Kündigung zum Ende der aktuellen
        Abolaufzeit in Kraft. Sie werden bis zum Ende der bezahlten Abolaufzeit
        Zugriff auf sämtliche PlayboyPremium Inhalte haben. Bereits gezahlte
        Gebühren werden nicht zurückerstattet.<b class="font-bold">
          Die Kündigung Ihres PlayboyPremium Abos kann nicht widerrufen
          werden.</b
        >
        Sie erhalten eine Kündigungsbestätigung an die von Ihnen angegebene
        E-Mail-Adresse.
      </p>
      <div class="flex justify-start flex-wrap mt-10">
        <a
          class="text-center btnCancel px-6 py-2 border border-black text-black uppercase tracking-widest text-sm cursor-pointer md:mr-4 md:order-1 order-2 md:mt-0 mt-3 md:w-auto w-full"
          [href]="environment + '/user/token/cancel?sub=' + uuidToCancel"
          >Ja, kündigen</a
        >
        <button
          (click)="isPopupVisible = false"
          class="px-6 py-2 border bg-golden border-golden uppercase tracking-widest text-sm cursor-pointer md:order-2 order-1 md:mb-0 mb-3 md:w-auto w-full"
        >
          Nein, nicht kündigen
        </button>
      </div>
    </div>
  </div>
}
