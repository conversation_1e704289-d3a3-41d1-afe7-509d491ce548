:host {
  @apply flex flex-col;
}

.profileTable .cell {
  width: 12.5%;
}
.profileTable .cell.small {
  width: 6.5%;
}
.profileTable .cell.medium {
  width: 8.5%;
}
.profileTable .cell.big {
  width: 16.5%;
}
.profileTable .cell.date {
  width: 11.5%;
}

.profileTable .row,
.row.mobile {
  border-bottom: 1px solid grey;
}

.profileTable .row:last-of-type {
  border-bottom: none;
}

.row.old div.cell,
.row.mobile.old .title {
  color: grey;
}

.row.old div.cell:nth-last-of-type(-n + 3),
.row.old div.cell button {
  display: none;
}

@media only screen and (max-height: 800px) and (max-width: 400px) {
  .popup {
    height: 100vh;
    overflow: auto;
  }
}
