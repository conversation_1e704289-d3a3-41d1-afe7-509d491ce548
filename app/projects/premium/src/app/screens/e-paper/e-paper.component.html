<ng-container>
  <div class="h-slider md:h-screen-3/4 flex flex-col">
    <div
      class="bg-cover h-full overflow-hidden relative w-full"
      style="
        background-image: url(&quot;assets/examples/background-epaper-min.jpeg&quot;);
      "
    >
      <img
        [src]="
          epaperObj?.data.pbIssueCurrent.results[0].fieldCover.entity
            ?.fieldMediaImage.url
        "
        class="absolute h-full left-1/5 lg:left-1/4 xl:left-1/2 rotate-12 transform translate-y-28 object-contain"
      />
    </div>
  </div>

  <lib-breadcrumbs
    class="w-5/6 mx-auto mt-8 md:mt-0"
    [breadcrumbs]="[{ link: '/e-paper', label: 'E-paper' }]"
  >
  </lib-breadcrumbs>

  <section class="py-14 px-8 md:px-32">
    <div class="mb-15">
      <h4 class="text-gray-400">
        {{
          epaperObj?.data.pbIssueCurrent.results[0].fieldMonth.value
            | date: "MMMM YYYY"
        }}
      </h4>
      <h3 class="mt-6 mb-15">
        {{ epaperObj?.data.pbIssueCurrent.results[0].entityLabel }}
      </h3>
      <div style="margin-bottom: 50px">
        <div
          [innerHTML]="epaperObj?.data.pbIssueCurrent.results[0].body.value"
        ></div>
      </div>

      <div
        [nexxID]="
          epaperObj?.data.pbIssueCurrent.results[0].fieldFeaturedVideo.entity
            ?.fieldNexxId
        "
        class="w-full flex mb-8"
        style="aspect-ratio: 16/9; height: auto !important"
      ></div>

      <a
        [href]="
          epaperObj?.data.pbIssueCurrent.results[0].fieldEPaper.entity.url
        "
        target="_blank"
      >
        <lib-button>E-PAPER HERUNTERLADEN</lib-button>
      </a>
    </div>
  </section>
</ng-container>
