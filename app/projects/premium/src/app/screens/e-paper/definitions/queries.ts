import gql from 'graphql-tag';

export const EPAPER_CONTENT_QUERY = gql`
  {
    pbIssueCurrent {
      results {
        uuid
        entityLabel
        fieldMonth {
          value
        }
        fieldEPaper {
          entity {
            url
          }
        }
        body {
          value
        }
        fieldFeaturedVideo {
          entity {
            ... on MediaNexxVideo {
              fieldNexxId
            }
          }
        }
        fieldCover {
          entity {
            ... on MediaImage {
              uuid
              fieldMediaImage {
                url
              }
            }
          }
        }
      }
    }
  }
`;
