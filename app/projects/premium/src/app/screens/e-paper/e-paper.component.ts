import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Apollo } from 'apollo-angular';
import { AccountService } from '../../services/account.service';
import { EPAPER_CONTENT_QUERY } from './definitions/queries';
import { BreadcrumbsComponent, ButtonComponent, NexxComponent } from '@pb/ui';
import { DatePipe } from '@angular/common';

export interface IEpaper {
  data: Data;
  loading: boolean;
  networkStatus: number;
}
export interface Data {
  pbIssueCurrent: pbIssueCurrent;
}
export interface pbIssueCurrent {
  results?: EntitiesEntity[] | null;
}
export interface EntitiesEntity {
  body: BodyOrFieldMonth;
  fieldEPaper: FieldEPaper;
  fieldCover: fieldCover;
  fieldFeaturedVideo?: null;
  fieldMonth: BodyOrFieldMonth;
  uuid: string;
  entityLabel: string;
}
export interface BodyOrFieldMonth {
  value: string;
}
export interface FieldEPaper {
  entity: EntityOrFieldMediaImage;
}
export interface EntityOrFieldMediaImage {
  url: string;
}
export interface fieldCover {
  entity: Entity;
}
export interface Entity {
  fieldMediaImage: EntityOrFieldMediaImage;
}

@Component({
  selector: 'app-e-paper',
  templateUrl: './e-paper.component.html',
  styleUrls: ['./e-paper.component.css'],
  imports: [BreadcrumbsComponent, DatePipe, NexxComponent, ButtonComponent],
})
export class EPaperComponent implements OnInit {
  headerGallery: any[];
  $account = this.accService.Account;
  public epaperObj: IEpaper;

  constructor(
    private readonly accService: AccountService,
    private apollo: Apollo,
    titleService: Title,
  ) {
    titleService.setTitle('E-Paper | Playboy All Access');

    this.headerGallery = [
      {
        name: 'IHR PROFIL',
        seen: false,
        model_id: '',
        id: 0,
        cover: 'assets/examples/example-4.png',
        date: -**************,
        category_id: 1,
        articles: [],
      },
    ];

    apollo
      .query({ query: EPAPER_CONTENT_QUERY })
      .toPromise()
      .then((v: IEpaper) => {
        this.epaperObj = v;
      });
  }

  ngOnInit(): void {}
}
