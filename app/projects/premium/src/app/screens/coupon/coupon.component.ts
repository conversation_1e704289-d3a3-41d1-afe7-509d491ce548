import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { environment } from 'projects/premium/src/environments/environment';
import { Subscription } from 'rxjs';
import { filter, map, switchMap } from 'rxjs/operators';
import {
  CouponCard,
  CouponContent,
  CouponService,
} from '../../services/coupon.service';
import { IPackage, PackagesService } from '../../services/packages.service';
import { PromoService } from '../../services/promo.service';
import { NgClass } from '@angular/common';
import { ButtonComponent } from '@pb/ui';

@Component({
  selector: 'app-coupon',
  templateUrl: './coupon.component.html',
  styleUrls: ['./coupon.component.css'],
  imports: [ButtonComponent, NgClass],
})
export class CouponComponent implements OnInit {
  private pageContentSubscription: Subscription;
  private coupon: string;
  private slug: string;
  public pageContent: CouponContent;
  public couponCard: CouponCard;

  public serverUrl = environment.serverUrl;

  public packages: IPackage[];
  public gridFeatures: {
    icon: string;
    name: string;
  }[];

  selectedPackageIndex = -1;

  private packagesSub: Subscription;
  private sub: Subscription;
  private button = document.querySelector('button');
  constructor(
    route: ActivatedRoute,
    public couponService: CouponService,
    pS: PackagesService,
    promo: PromoService,
  ) {
    this.slug = route.snapshot.params.slug;
    this.coupon = route.snapshot.params.coupon;

    this.packagesSub = route.paramMap
      .pipe(
        map((v) => v.get('coupon')),
        filter((v) => !!v),
        switchMap((coupon) => pS.getPackagesForCoupon(coupon)),
      )
      .subscribe((p) => (this.packages = p));

    this.sub = promo.Data.subscribe(
      ({ headerGalleries, ads, previewVideo, gridFeatures }) => {
        this.gridFeatures = gridFeatures;
      },
    );
  }

  ngOnInit(): void {
    this.pageContentSubscription = this.couponService
      .getPageContents(this.slug)
      .subscribe((data) => {
        console.log(data);

        if (data.length <= 0) {
          // throw 404
        }
        this.pageContent = data[0];
      });
  }
  getPlanId(plan: string) {
    if (plan == '1' || plan == '1old') {
      return 8;
    } else if (plan == '3' || plan == '3old') {
      return 7;
    } else if (plan == '6' || plan == '6old') {
      return 6;
    } else if (plan == '12' || plan == '12old') {
      return 5;
    }
    return 0;
  }
  getMonthlyPrice(price: string, months: string) {
    if (price != null && months != null) {
      const cleanMonths = parseInt(months.replace(/(old|bildPlus)/g, ''));

      const priceInt = parseFloat(price.replace(',', '.'));
      const monthInt = isNaN(cleanMonths) ? 1 : cleanMonths;

      const result = Math.ceil((priceInt / monthInt) * 100) / 100;

      return this.getEuroValue(result);
    }
    return price;
  }
  getEuroValue(number: number) {
    const formatter = new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR',

      // These options are needed to round to whole numbers if that's what you want.
      //minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
      //maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
    });

    return formatter.format(number);
  }
  getMonthId(monthId: string) {
    if (monthId == '1old') {
      return '-1499';
    } else if (monthId == '3old') {
      return '-3600';
    } else if (monthId == '6old') {
      return '-5400';
    } else if (monthId == '12old') {
      return '-8400';
    }
    return '';
  }
  getMonth(month: string): string {
    const numericMonth = month.match(/\d+/)?.[0];
    if (!numericMonth) {
      return '0';
    }
    if (numericMonth === '1') {
      return '1';
    } else if (numericMonth === '3') {
      return '3';
    } else if (numericMonth === '6') {
      return '6';
    } else if (numericMonth === '12') {
      return '12';
    }
    return '0';
  }

  ngOnDestroy(): void {
    this.pageContentSubscription?.unsubscribe();
    this.packagesSub?.unsubscribe();

    this.sub?.unsubscribe();
  }
}
