.golden {
  color: #c5a350;
}
.line {
  background-color: #c5a350;
  width: 100%;
  height: 1px;
  margin: 22px auto 45px auto;
  max-width: 220px;
}
.card {
  transition: all 0.3s ease-out;
  margin: 16px;
  cursor: pointer;
}
.card:hover {
  transition: all 0.3s ease-out;
  background: linear-gradient(
    180deg,
    rgba(197, 163, 80, 0.31) 0%,
    rgba(197, 163, 80, 0) 100%
  );
}
.hero-wrapper {
  margin-top: 110px;
  margin-bottom: 10rem;
}

.btn a {
  display: block;
  letter-spacing: 0.2rem;
}
.col-1 .card {
  width: calc(640px - 16px);
}
.col-2 .card {
  width: calc(640px - 16px);
}
.col-3 .card {
  width: calc(416px - 16px);
}
.col-4 .card {
  width: calc(304px - 16px);
}
.hero-image.mobile {
  display: none;
}
.adBlock-wrapper.mobile {
  display: none;
}
.adBlock-wrapper.mobile .adBlock {
  padding: 30px;
}

@media only screen and (max-width: 767px) {
  .hero-wrapper {
    margin-bottom: 4rem;
    margin-top: 60px;
  }
  .btn a {
    font-size: 14px;
    padding: 10px;
  }
  .card {
    margin: 16px 0;
  }
  .col-1 .card {
    width: 100%;
  }
  .col-2 .card {
    width: 100%;
  }
  .col-3 .card {
    width: 416px;
  }
  .col-4 .card {
    width: 304px;
  }
  .adImage {
    top: -100%;
    left: 50%;
    transform: translateX(-50%);
  }
  .hero-image.mobile {
    display: block;
  }
  .hero-image.desktop {
    display: none;
  }
  .adBlock-wrapper.desktop {
    display: none;
  }
  .adBlock-wrapper.mobile {
    display: block;
  }
}
