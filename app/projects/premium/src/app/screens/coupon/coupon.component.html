<div class="mb-52 hero-wrapper">
  <div class="hero relative mx-auto w-full">
    @if (pageContent?.logo) {
      <div>
        <img
          class="absolute md:top-8 md:right-8 top-4 right-4 h-8 md:h-14"
          [src]="'' + serverUrl + '/' + pageContent?.logo"
        />
      </div>
    }
    @if (!pageContent?.heroImageMobile) {
      <div>
        <img
          class="hero-image m-auto -top-4 -inset-x-4 md:max-w-full object-contain md:object-top md:left-1/12"
          [src]="serverUrl + pageContent?.heroImage"
        />
      </div>
    }
    @if (pageContent?.heroImageMobile) {
      <div>
        <img
          class="hero-image desktop m-auto -top-4 -inset-x-4 md:max-w-full object-contain md:object-top md:left-1/12"
          [src]="serverUrl + pageContent?.heroImage"
        />
        <img
          class="hero-image mobile m-auto -top-4 -inset-x-4 md:max-w-full object-contain md:object-top md:left-1/12"
          [src]="serverUrl + pageContent?.heroImageMobile"
        />
      </div>
    }
  </div>
</div>

<div class="md:px-14 px-8 mx-auto">
  <h2
    class="text-center w-5/6 mx-auto max-w-7xl md:text-7xl text-2xl title"
    [innerText]="pageContent?.title"
  ></h2>

  <p
    class="text-center md:text-3xl text-lg max-w-7xl mx-auto font-garamond font-light md:mt-10 mt-5"
    [innerHTML]="pageContent?.teaser"
  ></p>
  @if (pageContent?.button) {
    <div>
      <div class="flex justify-center my-15">
        <lib-button class="btn" [innerHTML]="pageContent?.button"></lib-button>
      </div>
    </div>
  }
  @if (!pageContent?.button) {
    <div>
      <div class="flex justify-center my-15"></div>
    </div>
  }
  <!-- card -->
  <div
    class="md:mb-16 flex flex-wrap justify-center"
    [ngClass]="'col-' + pageContent?.columns"
  >
    @for (card of pageContent?.cards; track card) {
      <a
        target="_blank"
        [href]="
          card?.month === '12bildPlus'
            ? 'https://shop.playboy.de/abo/cart/add?product=playboy-premium-12-monate-bildplus/' +
              '&redirect=' +
              serverUrl +
              '/user/login/&couponcode=' +
              (card.coupon !== null && card.coupon !== undefined
                ? card.coupon
                : this.coupon !== null && this.coupon !== undefined
                  ? this.coupon
                  : pageContent?.coupon)
            : 'https://shop.playboy.de/abo/cart/add?product=playboy-premium-' +
              getMonth(card?.month) +
              '-' +
              (getMonth(card?.month) > '1' ? 'monate' : 'monat') +
              getMonthId(card?.month) +
              '/&plan_id=' +
              getPlanId(card?.month) +
              '&redirect=' +
              serverUrl +
              '/user/login/&couponcode=' +
              (card.coupon !== null && card.coupon !== undefined
                ? card.coupon
                : this.coupon !== null && this.coupon !== undefined
                  ? this.coupon
                  : pageContent?.coupon)
        "
      >
        <div class="card p-8 pb-12 bg-gray-800 m-auto">
          <div class="flex items-baseline justify-center">
            <p
              class="month golden text-center text-8xl golden font-bauer font-bold"
              [innerText]="getMonth(card?.month)"
            ></p>
            @if (card?.month == "1" || card?.month == "1old") {
              <div>
                <p
                  class="golden text-center text-3xl golden font-bauer font-bold"
                >
                  Monat
                </p>
              </div>
            }
            @if (card?.month !== "1" && card?.month !== "1old") {
              <div>
                <p
                  class="golden text-center text-3xl golden font-bauer font-bold"
                >
                  Monate
                </p>
              </div>
            }
          </div>
          <p class="line my-5"></p>
          <div class="flex items-baseline justify-center mt-5">
            @if (card?.old_price) {
              <p
                class="text-center font-stag text-2xl text-gray-400 font-medium whitespace-nowrap"
              >
                Nur&nbsp;
              </p>
            }
            @if (card?.old_price) {
              <p
                class="text-center font-stag text-2xl text-gray-400 line-through font-medium whitespace-nowrap"
                [innerText]="getMonthlyPrice(card?.old_price, card?.month)"
              ></p>
            }
            @if (card?.new_p) {
              <p
                class="ml-2 text-center font-stag text-2xl font-medium whitespace-nowrap"
                [innerText]="
                  getMonthlyPrice(card?.new_p, card?.month) + ' mtl.'
                "
              ></p>
            }
          </div>
          @if (card?.month == "1" || card?.month == "1old") {
            <div>
              <p
                class="text-2xl text-center pr-1 px-4 font-garamond font-light mt-5"
                [innerText]="
                  'Insgesamt' +
                  ' ' +
                  card?.new_p +
                  ' €* für' +
                  ' ' +
                  getMonth(card?.month) +
                  ' ' +
                  'Monat'
                "
              ></p>
            </div>
          }
          @if (card?.month !== "1" && card?.month !== "1old") {
            <div>
              <p
                class="text-2xl text-center pr-1 px-4 font-garamond font-light mt-5"
                [innerText]="
                  'Insgesamt' +
                  ' ' +
                  card?.new_p +
                  ' €* für' +
                  ' ' +
                  getMonth(card?.month) +
                  ' ' +
                  'Monate'
                "
              ></p>
            </div>
          }
        </div>
      </a>
    }
  </div>

  <p
    class="max-w-7xl md:text-lg text-sm mx-auto w-5/6 font-garamond font-light mt-10"
    [innerHTML]="pageContent?.notice"
  ></p>

  @if (pageContent?.adTitle) {
    <div
      class="adBlock-wrapper desktop adBlock flex flex-wrap justify-between m-auto bg-gray-800 border-golden border p-8 max-w-4xl md:h-48 items-center md:mt-32 mt-64 mb-20 relative"
    >
      <div>
        <p
          class="golden text-left text-4xl font-bauer font-light mb-2"
          [innerText]="pageContent?.adTitle"
        ></p>
        <p
          class="text-left text-2xl font-garamond font-light"
          [innerText]="pageContent?.adSubtitle"
        ></p>
      </div>
      <img
        class="adImage object-contain absolute bottom-0 right-0 w-52"
        [src]="serverUrl + pageContent?.adImage"
      />
    </div>
  }

  @if (pageContent?.adTitle) {
    <div class="relative adBlock-wrapper mobile">
      <div
        class="adBlock flex flex-wrap justify-between m-auto bg-gray-800 border-golden border max-w-4xl md:h-48 items-center md:mt-32 mt-64 mb-20 z-50 relative"
      >
        <p
          class="golden text-center text-2xl font-bauer font-light m-auto mb-2"
          [innerText]="pageContent?.adTitle"
        ></p>
        <p
          class="md:text-left text-center md:text-2xl text-2xl font-garamond font-light"
          [innerText]="pageContent?.adSubtitle"
        ></p>
      </div>
      <img
        class="adImage object-contain absolute bottom-0 right-0 w-52 h-60 z-40"
        [src]="serverUrl + pageContent?.adImage"
      />
    </div>
  }

  <section class="grid place-content-center text-center my-15">
    <p class="text-4xl font-bauer">Nur bei</p>
    <img src="assets/logo/golden-logo-with-text_new.svg" />
  </section>

  <section>
    <div
      class="container grid md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 font-stag text-2xl"
    >
      @for (feature of gridFeatures; track feature) {
        <div class="flex flex-col">
          <div
            class="w-full bg-cover h-80 grid place-content-center"
            style="background-image: url(&quot;assets/bg-pattern.jpg&quot;)"
          >
            <img [src]="feature.icon" />
          </div>
          <div class="bg-gray-800 flex-1 p-6">
            <hr class="border-golden mb-6" style="max-width: 60px" />
            <p [innerHTML]="feature.name"></p>
          </div>
        </div>
      }
    </div>
  </section>
</div>
