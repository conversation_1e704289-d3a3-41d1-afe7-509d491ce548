import {
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { SsrCookieService as CookieService } from 'ngx-cookie-service-ssr';
import { Observable} from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { ModularService } from '../../services/modular/modular.service';
import { AccountService, IAccount } from '../../services/account.service';

import {
  ArticlePreviewComponent,
  CardCarouselComponent,
  CardCarouselSlideDirective,
  CategoryTile,
  GalleryCardComponent,
} from '../../shared';
import {
  AsyncPipe,
  NgTemplateOutlet,
} from '@angular/common';
import { ResponsivePipe } from '@pb/ui';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Meta, Title } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SearchInputComponent } from '../../components/search-input/search-input.component';
import { PublicFeedModuleComponent } from '../../shared/components/modular/public-feed-module/public-feed-module.component';
import { IsNew } from '../../utils/newCheck';

declare var upScore: any;
@Component({
  selector: 'modular',
  templateUrl: './modular.component.html',
  styleUrls: ['./modular.component.css'],
  imports: [
    NgTemplateOutlet,
    AsyncPipe,
    CardCarouselComponent,
    ResponsivePipe,
    CardCarouselSlideDirective,
    RouterLink,
    GalleryCardComponent,
    ArticlePreviewComponent,
    CategoryTile,
    ReactiveFormsModule,
    SearchInputComponent,
    FormsModule,
    PublicFeedModuleComponent,
  ],
})
export class ModularComponent implements OnInit {
  @Input() modularUrl?: string;

  $user: Observable<IAccount>;
  $modules: Observable<any>;
  $modularUrl: string;

  private convertUrlToTitle(url: string): string {
    return url
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  isExternalUrl(url: string): boolean {
    if (!url) return false;
    return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//');
  }

  constructor(
    private route: ActivatedRoute,
    public readonly accountService: AccountService,
    private cookieService: CookieService,
    private router: Router,
    private modularService: ModularService,
    private titleService: Title,
    private meta: Meta,
  ) {
    // If the user is logged in and subscribed, redirect to the full modular page
    this.accountService.Subscribed.subscribe((subscribed) => {
      if (subscribed) {
        return this.router.navigate(['/', this.$modularUrl]);
      }
    });
  }

  ngOnInit(): void {
    this.$user = this.accountService.Account;

    this.$modularUrl = this.modularUrl || this.route.snapshot.paramMap.get('modular_url');

    if (this.$modularUrl !== 'home') {
      this.meta.removeTag(`name='og:site_name'`);
      this.meta.removeTag(`name='og:type'`);
      this.meta.removeTag(`name='og:title'`);
      this.meta.removeTag(`name='og:description'`);
      this.meta.removeTag(`name='og:locale'`);
      this.meta.removeTag(`name='og:url'`);
      this.meta.removeTag(`name='og:image'`);
      this.meta.removeTag(`name='article:published_time'`);
      this.meta.removeTag(`name='profile:first_name'`);
      this.meta.removeTag(`name='profile:last_name'`);
      this.meta.removeTag(`name='profile:gender'`);
      this.meta.removeTag(`name='description'`);
      this.meta.removeTag(`name='author'`);
      this.meta.removeTag(`name='meta'`);
      const pageTitle = this.convertUrlToTitle(this.$modularUrl);
      const fullTitle = `${pageTitle} | Playboy Premium`;
      this.titleService.setTitle(fullTitle);
      this.meta.addTag({ name: 'og:site_name', content: 'Playboy Premium' });
      this.meta.addTag({ name: 'og:type', content: 'website' });
      this.meta.addTag({ name: 'og:title', content: pageTitle });
      this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
      this.meta.addTag({
        name: 'og:url',
        content: `https://premium.playboy.de/${this.$modularUrl}`,
      });
    }

    this.$modules = this.modularService
      .getPublicModules({ url: this.$modularUrl })
      .pipe(
        map((response) => {
          return response.modules;
        }),
      );
  }

  goToSearch(term?: string): void {
    this.router?.navigate(['/search'], { queryParams: { query: term || '' } });
  }

  isFirstModuleWithTitle(currentModule: any, modules: any[]): boolean {
    if (!currentModule?.title) {
      return false;
    }

    const firstModuleWithTitle = modules.find((module) => module?.title);
    return firstModuleWithTitle === currentModule;
  }

  protected readonly IsNew = IsNew;
}
