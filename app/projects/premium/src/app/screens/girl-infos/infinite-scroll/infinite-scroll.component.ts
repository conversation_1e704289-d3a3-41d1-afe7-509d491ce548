import { Component, Input, OnInit } from '@angular/core';
import { Screen, ScreenObservable } from '@pb/ui/utils/screen';
import { IPreview } from 'projects/premium/src/app/models/preview';
import {
  GirlInfoService,
  IGirlInfoServiceData,
} from 'projects/premium/src/app/services/girl-info/girl-info.service';
import { BehaviorSubject, combineLatest, Observable, Subject } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';
import { AsyncPipe } from '@angular/common';
import {
  ColumnLayoutComponent,
  ColumnLayoutItemDirective,
  GalleryCardComponent,
  ScrollInDirective,
} from '../../../shared';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-infinite-scroll',
  templateUrl: './infinite-scroll.component.html',
  styleUrls: ['./infinite-scroll.component.css'],
  imports: [
    AsyncPipe,
    ColumnLayoutComponent,
    ColumnLayoutItemDirective,
    GalleryCardComponent,
    RouterLink,
    ScrollInDirective,
  ],
})
export class InfiniteScrollComponent implements OnInit {
  $girlInfos: Observable<IGirlInfoServiceData>;

  @Input() inifinte = true;

  private readonly pagesBehav = new BehaviorSubject(1);
  $columns = ScreenObservable.pipe(
    map((v) => {
      if (v > Screen.xxl) {
        return 6;
      }
      if (v > Screen.lg) {
        return 4;
      }
      if (v > Screen.md) {
        return 3;
      }
      return 1;
    }),
    distinctUntilChanged((a, b) => a === b),
  );
  $pageSize = this.$columns.pipe(map((v) => v * 6));

  constructor(giS: GirlInfoService) {
    this.$girlInfos = giS.getGirlInfoPreviews(
      combineLatest([this.$pageSize, this.pagesBehav]).pipe(
        map(([perScreen, screens]) => [{ screens, perScreen }, {}]),
      ),
    );
  }

  ngOnInit(): void {}

  nextPage() {
    if (!this.inifinte) {
      return;
    }
    this.pagesBehav.next(this.pagesBehav.getValue() + 1);
  }
}
