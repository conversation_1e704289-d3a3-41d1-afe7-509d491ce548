import gql from 'graphql-tag';

// filter: {haircolor: {value: "2348"}, bustsize: {value: "90"}, field_category: {value: "2245"}}
export const GET_GIRL_INFOS = gql`
  query GirlInfoQuery($size: Int, $page: Int) {
    pbGirlInfo(
      sortBy: RELEASE
      pageSize: $size
      sortDirection: DESC
      page: $page
    ) {
      count
      results {
        entityLabel
        entityId
        ... on GirlInfo {
          id
          queryMainImages {
            entities {
              ... on MediaImage {
                fieldMediaImage {
                  url
                  derivative(style: XLARGE) {
                    url
                    width
                    height
                  }
                }
              }
            }
          }
          fieldCategory {
            entity {
              name
            }
          }
          queryGirl {
            entities {
              entityId
            }
          }
        }
      }
    }
  }
`;
