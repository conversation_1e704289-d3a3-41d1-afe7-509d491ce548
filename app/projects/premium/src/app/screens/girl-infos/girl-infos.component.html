<div class="sm:h-screen-3/4 h-slider flex flex-col">
  <lib-header-carousel class="flex flex-1">
    @for (modelInfo of $headerGirlInfos | async; track modelInfo.id) {
      <ng-template libHeaderCarouselItem [previewData]="modelInfo">
      </ng-template>
    }
  </lib-header-carousel>
</div>
<lib-breadcrumbs
  class="w-5/6 mx-auto mt-8 md:mt-0"
  [breadcrumbs]="[{ link: '/girl-infos', label: 'Gallerien' }]"
>
</lib-breadcrumbs>

<h3 class="my-20 w-full text-center">Entdecken Sie weiter Gallerien</h3>

<app-infinite-scroll></app-infinite-scroll>
