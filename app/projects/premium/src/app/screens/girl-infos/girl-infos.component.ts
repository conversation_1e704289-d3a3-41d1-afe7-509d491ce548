import {
  BreadcrumbsComponent,
  distinctJSON,
  HeaderCarouselComponent,
  HeaderCarouselItemDirective,
} from '@pb/ui';
import { GirlInfoService } from 'projects/premium/src/app/services/girl-info/girl-info.service';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { IGirlInfoWithGirl } from './infinite-scroll/definitions/models';
import { GET_GIRL_INFOS } from './infinite-scroll/definitions/queries';
import { IPreview } from '../../models/preview';
import { InfiniteScrollComponent } from './infinite-scroll/infinite-scroll.component';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-girl-infos',
  templateUrl: './girl-infos.component.html',
  styleUrls: ['./girl-infos.component.css'],
  imports: [
    HeaderCarouselComponent,
    HeaderCarouselItemDirective,
    BreadcrumbsComponent,
    InfiniteScrollComponent,
    AsyncPipe,
  ],
})
export class GirlInfosComponent implements OnDestroy {
  private behav = new BehaviorSubject<IPreview[]>([]);

  $girlInfos = this.behav.asObservable();
  $headerGirlInfos = this.$girlInfos.pipe(
    map((v) => v.slice(0, 4)),
    distinctJSON(),
  );

  private sub: Subscription;

  constructor(gi: GirlInfoService) {
    this.sub = gi
      .fetchGirlInfoPreviews(1, 4, {})
      .subscribe((v) => this.behav.next(v.data));
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
  }
}
