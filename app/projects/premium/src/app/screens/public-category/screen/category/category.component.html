@if ($category | async; as category) {
  <lib-breadcrumbs
    [breadcrumbs]="[
      {
        link: '/p/categories/' + (category | categorySlug),
        label: category.entityLabel,
      },
    ]"
  ></lib-breadcrumbs>
}

<h3
  class="w-full text-4xxl sm:text-5xl text-center p-4 my-10 md:mt-8 md:mb-15"
  id="title-proposal"
>
  {{ ($category | async)?.entityLabel }}
</h3>

<!--<lib-filter-dropdown-->
<!--  (selectedFilterChange)="selectFilter($event)"-->
<!--  (selectedSortChange)="selectSorting($event)"-->
<!--  [filterOptions]="$filterOptions | async"-->
<!--  [selectedFilter]="$selectedFilter | async"-->
<!--  [selectedSorting]="$selectedSorting | async"-->
<!--  [sortingOptions]="sortingOptions"-->
<!--&gt;-->
<!--</lib-filter-dropdown>-->

@if (!!models && models.length > 0) {
  <ng-template
    #article
    let-id="id"
    let-link="link"
    let-model="item"
    let-image="image"
  >
    <a class="w-full flex h-full max-h-full cursor-pointer" [routerLink]="link">
      <app-gallery-card
        class="w-full"
        [previewData]="model"
        [image]="image"
      ></app-gallery-card>
    </a>
  </ng-template>

  <app-pagination-grid
    [paginationAnimationElementSelector]="'#title-proposal'"
    [items]="models"
    [page]="$page | async"
    [pages]="pages"
    [loading]="false"
    [template]="article"
  ></app-pagination-grid>
}
