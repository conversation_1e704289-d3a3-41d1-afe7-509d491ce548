import { Component, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { BreadcrumbsComponent, distinctJSON } from '@pb/ui';
import { IFilterOptions } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { IPreview } from 'projects/premium/src/app/models/preview';
import {
  CategoriesService,
  ICategory,
} from 'projects/premium/src/app/services/categories/categories.service';
import { combineLatest, Observable, Subscription } from 'rxjs';
import { filter, map, switchMap } from 'rxjs/operators';

import { AsyncPipe, Location } from '@angular/common';
import { Meta } from '@angular/platform-browser';
import { AccountService } from 'projects/premium/src/app/services/account.service';
import { PublicService } from 'projects/premium/src/app/services/public.service';
import { PaginationGridComponent } from 'projects/premium/src/app/shared';
import { GalleryCardComponent } from '../../../../shared/components/gallery-card/gallery-card.component';
import { CategorySlugPipe } from '../../../../shared/pipes/category-slug.pipe';

@Component({
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.css'],
  imports: [
    AsyncPipe,
    RouterLink,
    BreadcrumbsComponent,
    CategorySlugPipe,
    GalleryCardComponent,
    PaginationGridComponent,
  ],
})
export class CategoryComponent implements OnDestroy {
  $categoryName: Observable<string>;
  $category: Observable<ICategory>;
  $filterOptions: Observable<IFilterOptions>;
  $pageAmount: Observable<number>;
  models: IPreview[];
  headers: IPreview[];
  pages: number;
  page = 0;
  $page: Observable<number>;
  // private girlService = inject(GirlInfoService);
  // sortingOptions: SortOptions[] = this.girlService.sortingOptions;
  private gallerySub: Subscription;
  private galleryCountSub: Subscription;
  private loggedSub: Subscription;
  private paramsSub: Subscription;
  // private selectedSorting = new BehaviorSubject<SortOptions | undefined>(
  //   undefined,
  // );
  // public $selectedSorting = this.selectedSorting.pipe(distinctJSON());
  // private selectedFilter = new BehaviorSubject<Partial<ICategoryFilterOptions>>(
  //   {},
  // );
  // public $selectedFilter = this.selectedFilter.pipe(distinctJSON());

  constructor(
    private publicData: PublicService,
    route: ActivatedRoute,
    private meta: Meta,
    private location: Location,
    public readonly router: Router,
    private accountService: AccountService,
    categoriesService: CategoriesService,
  ) {
    this.$page = route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );

    this.$page.subscribe((p) => {
      if (this.page !== p) {
        this.page = p;
      }
    });

    this.$categoryName = route.paramMap.pipe(
      map((v) => v.get('category_name')),
      filter((v) => !!v),
      distinctJSON(),
      map((v) => {
        this.loggedSub = this.accountService.Subscribed.subscribe(
          (subscribed) => {
            if (subscribed) {
              this.loggedSub.unsubscribe();
              return this.router.navigate(['/categories/' + v]);
            }
          },
        );
        return v;
      }),
    );

    this.$category = this.$categoryName.pipe(
      switchMap((v) => categoriesService.getBySlug(v)),
      distinctJSON(),
    );

    const $params = combineLatest([
      this.$category,
      this.$page,
      categoriesService.Categories,
    ])
      .pipe(distinctJSON())
      .subscribe((v) => {
        if (!!v[0] && !!v[0].entityId) {
          if (v[1] == 0) {
            this.headers = [];
          }
          let categories = [v[0].entityId];
          // also get parent category
          if (!!v[2]) {
            v[2].map((parent) => {
              if (parent.entityId == v[0].entityId) {
                parent.children.map((subcategory) => {
                  categories.push(subcategory.entityId);
                });
              }
            });
          }
          this.gallerySub = this.publicData
            .getGirlInfos(v[1], categories)
            .subscribe((data) => {
              if (v[1] == 0 || !this.headers) {
                this.headers = data.slice(0, 4);
              }
              this.models = data.map((model) => {
                return {
                  ...model,
                  image: model.imageLowRes,
                };
              });
            });
          this.galleryCountSub = this.publicData
            .getGirlInfosCount(categories)
            .subscribe((count) => {
              this.pages = count;
            });
        }
      });
  }

  public set Page(page: number) {
    this.router.navigate([], {
      queryParams: { page },
      queryParamsHandling: 'merge',
    });
  }

  ngOnInit(): void {
    this.$categoryName.subscribe((categoryName) => {
      if (categoryName) {
        this.meta.addTag({ name: 'og:title', content: categoryName });
      }
    });
    this.meta.removeTag(`name='og:site_name'`);
    this.meta.removeTag(`name='og:type'`);
    this.meta.removeTag(`name='og:title'`);
    this.meta.removeTag(`name='og:description'`);
    this.meta.removeTag(`name='og:locale'`);
    this.meta.removeTag(`name='og:url'`);
    this.meta.removeTag(`name='og:image'`);
    this.meta.removeTag(`name='article:published_time'`);
    this.meta.removeTag(`name='profile:first_name'`);
    this.meta.removeTag(`name='profile:last_name'`);
    this.meta.removeTag(`name='profile:gender'`);
    this.meta.removeTag(`name='description'`);
    this.meta.removeTag(`name='author'`);
    this.meta.removeTag(`name='meta'`);

    this.meta.addTag({ name: 'og:site_name', content: 'Playboy All Access' });
    this.meta.addTag({ name: 'og:type', content: 'website' });
    this.meta.addTag({
      name: 'og:description',
      content:
        'Mit All Access erhalten Sie Zugriff auf über 250.000 Fotos und Videos sowie auf unser Playboy E-Paper-Archiv mit mehr als 250 Ausgaben. Jetzt anmelden und Vorteile genießen.',
    });
    this.meta.addTag({
      name: 'description',
      content:
        'Mit All Access erhalten Sie Zugriff auf über 250.000 Fotos und Videos sowie auf unser Playboy E-Paper-Archiv mit mehr als 250 Ausgaben. Jetzt anmelden und Vorteile genießen.',
    });
    this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
    const currentUrl = this.location.path();
    this.meta.addTag({
      name: 'og:url',
      content: `https://premium.playboy.de${currentUrl}`,
    });
  }

  ngOnDestroy(): void {
    this.loggedSub?.unsubscribe();
    this.gallerySub?.unsubscribe();
    this.galleryCountSub?.unsubscribe();
    this.paramsSub?.unsubscribe();
  }

  // protected selectFilter(data: IFilterSelection): void {
  //   this.selectedFilter.next(data);
  //   this.Page = 0;
  // }
  //
  // protected selectSorting(data: SortOptions): void {
  //   this.selectedSorting.next(data);
  //   this.Page = 0;
  // }
}
