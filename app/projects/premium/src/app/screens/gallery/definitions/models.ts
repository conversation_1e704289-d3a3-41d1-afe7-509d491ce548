export interface IGirlInfoSlideshowData {
  girlInfoById: GirlInfoByID;
}

interface GirlInfoByID {
  description?: string;
  queryGalleries: QueryGalleries;
  queryGirl: {
    entities: [
      {
        id: number;
        name: string;
      },
    ];
  };
  reverseFieldGirlInfosNode?: {
    entities?: ({ entityId: number } | null)[];
  };
}

interface QueryGalleries {
  entities: ISlideshowGallery[];
}

interface ISlideshowGallery {
  name: string;
  fieldMediaSlideshow: IGirlInfoSlideshowFieldMediaSlideshow[];
  fieldVideos: INexxVideoItem[];
}

export interface INexxVideoItem {
  entity: {
    fieldNexxId: string;
    fieldDescription: {
      processed: string;
    };
    fieldPreviewImage: IGirlInfoSlideshowFieldMediaSlideshow;
    uuid: string;
    mid: number;
    fieldVideo: {
      title: string;
      itemId: number;
    };
    fieldCredits: {
      entity: {
        entityLabel: string;
      };
    }[];
  };
}

export interface IGirlInfoSlideshowFieldMediaSlideshow {
  entity: FieldMediaSlideshowEntity;
}

interface FieldMediaSlideshowEntity {
  fieldMediaImage: FieldMediaImage;
  fieldCredit: string;
  fieldDescription: {
    processed: string;
  };
  mid: string;
}

interface FieldMediaImage {
  url: string;
  alt: string;
  derivative: {
    url: string;
  };
}
