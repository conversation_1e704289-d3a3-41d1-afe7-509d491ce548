import gql from 'graphql-tag';

export const GIRL_INFO_SLIDESHOW_DATA_QUERY = gql`
  query GetGirlInfoSlideshowData($girlInfoId: String!) {
    girlInfoById(id: $girlInfoId) {
      queryGirl {
        entities {
          ... on Girl {
            id
            name
          }
        }
      }
      reverseFieldGirlInfosNode {
        entities {
          entityId
        }
      }
      queryGalleries(filter: { conditions: { field: "status", value: "1" } }) {
        entities {
          ... on MediaGallery {
            name
            fieldCredit
            fieldMediaSlideshow {
              entity {
                mid
                ... on MediaImage {
                  fieldCredit
                  fieldMediaImage {
                    url
                    alt
                    derivative(style: MEDIUM) {
                      url
                      width
                      height
                    }
                  }
                  fieldDescription {
                    processed
                  }
                }
              }
            }
            fieldVideos {
              entity {
                mid
                ... on MediaNexxVideo {
                  fieldNexxId
                  fieldVideoHash
                  fieldCredits {
                    entity {
                      entityLabel
                    }
                  }
                  fieldDescription {
                    processed
                  }
                  fieldPreviewImage {
                    entity {
                      ... on MediaImage {
                        fieldMediaImage {
                          url
                          alt
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;
