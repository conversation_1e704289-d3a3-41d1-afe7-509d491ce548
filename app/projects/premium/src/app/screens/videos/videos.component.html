<lib-breadcrumbs
  class="w-5/6 mx-auto mt-8 md:mt-0"
  [breadcrumbs]="[{ link: '/videos', label: 'Videos' }]"
>
</lib-breadcrumbs>

<h2 class="my-20 w-full text-center">Premium Videos</h2>

<lib-filter-dropdown
  [sortingOptions]="sortingOptions"
  [filterOptions]="$filterOptions | async"
  [selectedSorting]="$selectedSorting | async"
  [selectedFilter]="$selectedFilter | async"
  (selectedSortChange)="selectSorting($event)"
  (selectedFilterChange)="selectFilter($event)"
>
</lib-filter-dropdown>

@if ($videos | async; as videos) {
  <div
    class="mt-6 flex flex-row grid grid-cols-1 xxl:grid-cols-6 lg:grid-cols-4 md:grid-cols-3 grid-rows-auto gap-x-6 gap-y-14 mx-6"
  >
    @for (data of videos; track data) {
      <a
        [routerLink]="getVideoRouterLink(data)"
        class="w-full flex flex-col cursor-pointer p-3"
      >
        <app-gallery-card
          class="w-full"
          [image]="data.fieldPreviewImage.entity?.fieldMediaImage.url"
          [nexxID]="data.fieldNexxId"
          [title]="
            data.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
              .entities[0].fieldCategory?.[0].entity?.name
          "
          [name]="
            data.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
              .entities[0].entityLabel
          "
          [isAAContent]="getIsVideoAAContent(data)"
          favoriteType="video"
          [favoriteId]="data.mid"
          [imageRatio]="16 / 9"
        >
        </app-gallery-card>
      </a>
    }
  </div>
}

<!-- TODO: This is probably not the best way to communicate the nextPage param -->
@if ($nextPage | async; as nextPage) {
  <p
    class="my-32 justify-center items-center w-full flex"
    (appScrollIn)="showPage(nextPage)"
  ></p>
}
