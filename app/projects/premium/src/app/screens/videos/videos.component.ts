import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { Title } from '@angular/platform-browser';
import {
  BreadcrumbsComponent,
  distinctJSON,
  FilterDropdownComponent,
} from '@pb/ui';
import { IFilterOptions } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { Screen, ScreenObservable } from '@pb/ui/utils/screen';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  switchMap,
} from 'rxjs/operators';
import { AccountService } from '../../services/account.service';

import { AttributesService } from '../../services/attributes/attributes.service';
import {
  ICategoryFilterOptions,
  ICategoryQueryVariableSort,
} from '../../services/girl-info/models';
import { GenerateMainQueryParams } from '../../services/girl-info/queries';
import { GirlInfoService } from './../../services/girl-info/girl-info.service';
import { IVideo } from './definitions/models';
import { VIDEOS_QUERY } from './definitions/queries';
import { AsyncPipe } from '@angular/common';
import { GalleryCardComponent, ScrollInDirective } from '../../shared';
import { RouterLink } from '@angular/router';

type SortOptions =
  | 'Beliebteste'
  | 'Beliebteste (heute)'
  | 'Älteste'
  | 'Neueste';

@Component({
  selector: 'app-videos',
  templateUrl: './videos.component.html',
  styleUrls: ['./videos.component.css'],
  imports: [
    AsyncPipe,
    BreadcrumbsComponent,
    FilterDropdownComponent,
    RouterLink,
    GalleryCardComponent,
    ScrollInDirective,
  ],
})
export class VideosComponent implements OnDestroy {
  private selectedSorting = new BehaviorSubject<SortOptions | undefined>(
    undefined,
  );
  private selectedFilter = new BehaviorSubject<Partial<ICategoryFilterOptions>>(
    {},
  );
  private videosBehav = new BehaviorSubject<IVideo[]>([]);

  public $selectedSorting = this.selectedSorting.pipe(distinctJSON());

  $columns = ScreenObservable.pipe(
    map((v) => {
      if (v > Screen.xxl) {
        return 8;
      }
      if (v > Screen.lg) {
        return 4;
      }
      if (v > Screen.md) {
        return 3;
      }
      return 1;
    }),
    distinctUntilChanged((a, b) => a === b),
  );

  private $activeSorting: Observable<ICategoryQueryVariableSort>;
  public $selectedFilter = this.selectedFilter.pipe(distinctJSON());

  sortingOptions: SortOptions[] = [];
  $filterOptions: Observable<IFilterOptions>;

  $videos = this.videosBehav.asObservable();
  $headerVideos = this.$videos.pipe(
    map((v) =>
      v
        .filter(
          (data) =>
            !!data.reverseFieldVideosMedia?.entities[0]
              ?.reverseGalleriesGirlInfo.entities[0]?.entityLabel,
        )
        .slice(0, 4),
    ),
  );

  paramsSub: Subscription;
  videosSub: Subscription;

  $shownPerScreen = this.$columns.pipe(
    map((columns) => columns * 4),
    distinctUntilChanged((a, b) => a === b),
    debounceTime(10),
  );
  private shownScreensBehav = new BehaviorSubject<number>(1);

  $nextPage = combineLatest([
    this.$videos.pipe(
      map((v) => v.length),
      distinctUntilChanged((a, b) => a === b),
      debounceTime(10),
    ),
    this.$shownPerScreen,
  ]).pipe(
    map(([videoCount, perScreen]) => Math.ceil(videoCount / perScreen) + 1),
    distinctUntilChanged((a, b) => a === b),
  );

  public showPage(page: number) {
    this.shownScreensBehav.next(page);
  }

  constructor(
    apollo: Apollo,
    titleService: Title,
    attributesService: AttributesService,
    girlService: GirlInfoService,
    private accountService: AccountService,
  ) {
    titleService.setTitle('Videos | Playboy All Access');

    this.$activeSorting = girlService
      .mapSortingObservable(this.$selectedSorting)
      .pipe(
        map((v) => {
          switch (v?.sortField) {
            case 'FIELD_PUBLISH_DATE_VALUE':
              return {
                ...v,
                sortField: 'FIELD_RELEASE_DATE_VALUE',
              } as any;
          }
        }),
      );

    this.$filterOptions = girlService.$filterOptions;

    this.sortingOptions = girlService.sortingOptions;

    const $params = combineLatest([
      this.$selectedFilter,
      this.$activeSorting,
    ]).pipe(
      map((d) => GenerateMainQueryParams(...d)),
      distinctJSON(),
    );

    // Go to page 0 if params are changed
    this.paramsSub = $params.subscribe(() => this.shownScreensBehav.next(1));

    this.videosSub = combineLatest([
      this.$shownPerScreen,
      this.shownScreensBehav.pipe(
        distinctUntilChanged((a, b) => a === b),
        debounceTime(10),
      ),
      $params,
    ])
      .pipe(
        distinctJSON(),
        switchMap(([perScreen, screens, params]) =>
          combineLatest(
            new Array(screens).fill(null).map((_, page) =>
              apollo.query<{
                pbInfoWithLastVideoGallery: {
                  results: { fieldVideos: { entity: IVideo }[] }[];
                };
              }>({
                query: VIDEOS_QUERY,
                variables: { ...params, page, pageSize: perScreen },
              }),
            ),
          ),
        ),
        map((v) =>
          v.reduce(
            (full, current): IVideo[] => [
              ...full,
              ...current.data.pbInfoWithLastVideoGallery.results.reduce(
                (allResults, result) => [
                  ...allResults,
                  ...result.fieldVideos.reduce(
                    (videos, video) => [...videos, video.entity],
                    [],
                  ),
                ],
                [],
              ),
            ],
            [] as IVideo[],
          ),
        ),
        distinctJSON(),
      )
      .subscribe((videos) => this.videosBehav.next(videos));
  }

  ngOnDestroy(): void {
    this.paramsSub?.unsubscribe();
    this.videosSub?.unsubscribe();
  }

  selectSorting(data: SortOptions): void {
    this.selectedSorting.next(data);
    this.shownScreensBehav.next(1);
  }

  selectFilter(data: { [key: string]: string[] }): void {
    this.selectedFilter.next(data);
    this.shownScreensBehav.next(1);
  }

  public trackVideo(index: number, video: IVideo) {
    return video.fieldNexxId;
  }

  getVideoRouterLink(video: IVideo): any[] {
    const isAAContent =
      video?.reverseFieldVideosMedia?.entities?.at(0)?.reverseGalleriesGirlInfo
        ?.entities[0]?.fieldPlusAccess !== true;
    if (
      isAAContent &&
      this.accountService.subscriptionType() !== 'all-access'
    ) {
      return [
        '/girl',
        video.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
          .entities[0].girl?.targetId,
        video.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
          .entities[0].entityId,
      ];
    }

    return [
      '/girl-info',
      video.reverseFieldVideosMedia.entities[0]?.reverseGalleriesGirlInfo
        .entities[0].entityId,
      video.mid,
    ];
  }

  getIsVideoAAContent(video: IVideo): boolean {
    const isVideoAA =
      video?.reverseFieldVideosMedia?.entities?.at(0)?.reverseGalleriesGirlInfo
        ?.entities[0]?.fieldPlusAccess !== true;
    const isGirlOfTheDay =
      video?.reverseFieldGirlInfosNode?.entities &&
      video.reverseFieldGirlInfosNode.entities.length > 0;
    const isVideoBlockedForPlusUser =
      this.accountService.subscriptionType() === 'plus' && isGirlOfTheDay;
    return isVideoAA || isVideoBlockedForPlusUser;
  }
}
