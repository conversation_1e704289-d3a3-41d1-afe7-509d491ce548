import { IFieldMediaSlideshowEntity } from '../../model/screens/model/definitions/models';

export interface IVideo {
  fieldNexxId: string;
  entityLabel: string;
  mid: string;
  fieldPreviewImage: {
    entity: IFieldMediaSlideshowEntity;
  };
  reverseFieldVideosMedia?: {
    entities: [
      {
        uuid: string;
        reverseGalleriesGirlInfo: {
          entities: [
            {
              entityId: string;
              entityLabel: string;
              fieldCategory?: {
                entity: {
                  name: string;
                };
              };
              fieldPlusAccess?: boolean
              girl: {
                targetId?: number;
              }
            },
          ];
        };
      },
    ];
  };
  reverseFieldGirlInfosNode?: {
    entities?: ({ entityId: number } | null)[];
  };
}
