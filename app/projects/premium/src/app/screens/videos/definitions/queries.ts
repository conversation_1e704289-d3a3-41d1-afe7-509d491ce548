import gql from 'graphql-tag';

export const VIDEOS_QUERY = gql`
  query GetVideoData(
    $hairColor: String
    $eyeColor: String
    $preference: String
    $bustSize: BustsizeMultiViewFilterInput
    $release: FieldReleaseDateValueMultiViewFilterInput
    $field_category: [String]
    $sortField: GraphqlGalleriesVideosGraphql1ViewSortBy
    $pageSize: Int!
    $page: Int!
    $sortDirection: ViewSortDirection
    $searchName: String
  ) {
    pbInfoWithLastVideoGallery(
      filter: {
        haircolor: { value: $hairColor }
        eyecolor: { value: $eyeColor }
        field_preference_target_id: $preference
        bustsize: $bustSize
        field_category: $field_category
        field_release_date_value: $release
        search_name: $searchName
        search_description: $searchName
        search_credit: $searchName
        tags: $searchName
      }
      sortBy: $sortField
      pageSize: $pageSize
      sortDirection: $sortDirection
      page: $page
    ) {
      results {
        fieldVideos {
          entity {
            mid
            entityLabel
            ... on MediaNexxVideo {
              fieldNexxId
              fieldPreviewImage {
                entity {
                  ... on MediaImage {
                    fieldMediaImage {
                      url
                    }
                  }
                }
              }
              reverseFieldVideosMedia {
                entities {
                  ... on MediaGallery {
                    uuid
                    reverseGalleriesGirlInfo {
                      entities {
                        entityId
                        entityLabel
                        ... on GirlInfo {
                          fieldPlusAccess
                          fieldCategory {
                            entity {
                              name
                            }
                          }
                          girl {
                            targetId
                          }
                          reverseFieldGirlInfosNode {
                            entities {
                              entityId
                            }
                          }
                          fieldLatestGalleryRelease
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`;
