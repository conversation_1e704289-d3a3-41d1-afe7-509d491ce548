import { isPlatformBrowser } from '@angular/common';
import {
  AfterViewInit,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { Meta } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { SubscriptionPopupService } from '../../../components/subscription-popup/subscription-popup.service';
import { IPreview } from '../../../models/preview';
import { PublicService } from '../../../services/public.service';
import { UtmService } from '../../../services/utm.service';
import { ModularComponent } from '../../public-modular/modular.component';

declare var upScore: any;

@Component({
  selector: 'app-landing-preview',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.css'],
  imports: [ModularComponent],
})
export class LandingPreviewComponent
  implements OnInit, AfterViewInit, On<PERSON><PERSON>roy
{
  upScoreArticleContainer = 'preview-landing';
  featuredItems: IPreview[];

  private sub: Subscription;
  private readonly subscriptionPopupService: SubscriptionPopupService = inject(
    SubscriptionPopupService,
  );
  private readonly platFormId = inject(PLATFORM_ID);

  constructor(
    private publicData: PublicService,
    private meta: Meta,
    private utmService: UtmService,
  ) {}

  private scrollHandler: (() => void) | null = null;

  ngAfterViewInit(): void {
    if (isPlatformBrowser(this.platFormId)) {
      let hasScrolled = false;
      this.scrollHandler = () => {
        if (!hasScrolled) {
          hasScrolled = true;
          setTimeout(() => {
            this.subscriptionPopupService.open(false, {
              images: this.featuredItems.map((i) => ({
                src: i.image,
                focalPoint: i.focalPoint,
              })),
            });
          }, 5000);
          window.removeEventListener('scroll', this.scrollHandler);
          this.scrollHandler = null;
        }
      };

      window.addEventListener('scroll', this.scrollHandler);
    }
  }

  ngOnInit(): void {
    this.featuredItems = [];
    this.sub = this.publicData.getHomeFeatured().subscribe((data) => {
      this.featuredItems = data.slice(0, 4); // limit to first 4 items
    });

    if (typeof upScore !== 'undefined') {
      const upScoreObjectId = 'aa_6';
      const upScoreData = {
        article_container: '.' + this.upScoreArticleContainer,
        track_positions: false,
        section: 'homepage',
        taxonomy: '',
        object_id: upScoreObjectId,
        pubdate: '2021-10-10T20:15:00+2:00',
        author: '',
        object_type: 'homepage',
        custom_source: '',
        custom_app: 0,
        custom_video: 0,
        custom_audio: 0,
        custom_subdomain: 'premium',
        content_type: 1,
        content_blocked: 1,
        conversion: 0,
        user_status: '',
      };
      console.log('upScoreData', upScoreData);
      upScore('page_view', upScoreData);
      this.utmService.handleUTM(upScoreObjectId);
    }

    this.meta.removeTag(`name='og:site_name'`);
    this.meta.removeTag(`name='og:type'`);
    this.meta.removeTag(`name='og:title'`);
    this.meta.removeTag(`name='og:description'`);
    this.meta.removeTag(`name='og:locale'`);
    this.meta.removeTag(`name='og:url'`);
    this.meta.removeTag(`name='og:image'`);
    this.meta.removeTag(`name='article:published_time'`);
    this.meta.removeTag(`name='profile:first_name'`);
    this.meta.removeTag(`name='profile:last_name'`);
    this.meta.removeTag(`name='profile:gender'`);
    this.meta.removeTag(`name='description'`);
    this.meta.removeTag(`name='author'`);
    this.meta.removeTag(`name='meta'`);
    this.meta.addTag({ name: 'og:site_name', content: 'Playboy All Access' });
    this.meta.addTag({ name: 'og:type', content: 'website' });
    this.meta.addTag({ name: 'og:title', content: 'Home' });
    this.meta.addTag({
      name: 'og:description',
      content:
        'Mit All Access erhalten Sie Zugriff auf über 250.000 Fotos und Videos sowie auf unser Playboy E-Paper-Archiv mit mehr als 250 Ausgaben. Jetzt anmelden und Vorteile genießen.',
    });
    this.meta.addTag({
      name: 'description',
      content:
        'Mit All Access erhalten Sie Zugriff auf über 250.000 Fotos und Videos sowie auf unser Playboy E-Paper-Archiv mit mehr als 250 Ausgaben. Jetzt anmelden und Vorteile genießen.',
    });
    this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
    this.meta.addTag({
      name: 'og:url',
      content: 'https://premium.playboy.de/',
    });
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();

    if (isPlatformBrowser(this.platFormId) && this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler);
      this.scrollHandler = null;
    }
  }
}
