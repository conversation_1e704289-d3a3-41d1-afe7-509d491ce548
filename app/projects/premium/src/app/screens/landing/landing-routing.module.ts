import { createUrlTreeFromSnapshot, Routes } from '@angular/router';
import { LandingPreviewComponent } from './preview/landing.component';
import { LandingSubscribedComponent } from './subscribed/landing.component';

import { inject } from '@angular/core';
import { AccountService } from '../../services/account.service';
import { firstValueFrom } from 'rxjs';

const routes: Routes = [
  {
    path: '',
    component: LandingPreviewComponent,
    canActivate: [
      async (route) =>
        (await firstValueFrom(inject(AccountService).Subscribed))
          ? createUrlTreeFromSnapshot(route, ['home'])
          : true,
    ],
    title: 'Homepage | Playboy Premium',
  },
  {
    path: 'home',
    component: LandingSubscribedComponent,
    canActivate: [
      async (route) =>
        (await firstValueFrom(inject(AccountService).Subscribed))
          ? true
          : createUrlTreeFromSnapshot(route, ['..']),
    ],
    title: 'Homepage | Playboy Premium',
  },
];

export default routes;
