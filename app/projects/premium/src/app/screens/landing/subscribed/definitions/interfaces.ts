import {
  PublicImage,
  PublicImageDerivative,
} from '../../../model/screens/model/definitions/models';

export interface EntitiesEntity {
  fieldImage: {
    entity: {
      fieldFocalPointX: number;
      fieldFocalPointY: number;
      fieldMediaImage: {
        url: string;
        width: number;
        height: number;
        derivative: {
          url: string;
        };
      };
    };
  };
  fieldMediaImage: FieldMediaImage;
  mid: number;
  name: string;
  reverseGalleriesGirlInfo: ReverseGalleriesGirlInfo;
  uuid: string;
  fieldGirlInfos?: { entity: EntitiesEntity1 }[];
}
export interface FieldMediaImage {
  url: string;
}
export interface ReverseGalleriesGirlInfo {
  entities?: EntitiesEntity1[] | null;
}
export interface EntitiesEntity1 {
  id: number;
  name: string;
  descriptorMonth: string;
  descriptorYear: number;
  entityLabel: string;
  fieldPlusAccess?: boolean | string;
  fieldCategory?: { entity?: { entityLabel?: string, name?: string } };
  queryGirl?: { entities: { entityId: string }[] };
  fieldPublicImages?: PublicImage[];
  fieldPublicImagesLow?: PublicImageDerivative[];
  fieldMainFocalPointX?: number;
  fieldMainFocalPointY?: number;
}
export interface ISCData {
  pbFeatured: SCGirlInfoQuery;
}
export interface IGDTData {
  pbGalerieDesTages: SCGirlInfoQuery;
}
export interface IPBData {
  pbInfoWithLastGallery: SCGirlInfoQuery;
}
export interface SCGirlInfoQuery {
  results?: [EntitiesEntity];
}
export interface SCEntitiesEntity {
  fieldFeatured: boolean;
  id: number;
  name: string;
  descriptor: {
    entity: {
      name: string;
    };
  };
  focalPoint: { x: number; y: number };
  queryGalleries: {
    entities?: [EntitiesEntity];
  };
}

export interface QueryMainImages {
  entities?: [FieldMediaImage];
}

export interface FieldMediaImage {
  url: string;
  derivative?: {
    url: string;
  };
}

export interface IMediaQueryNexxVideo {
  __typename: 'MediaNexxVideo';
  mid: number;
  fieldNexxId: number;
  entityLabel: string;
  fieldPreviewImage: {
    entity: {
      fieldMediaImage: FieldMediaImage;
    };
  };
}

export interface IMediaQueryNexxVideo {
  __typename: 'MediaNexxVideo';
  mid: number;
  fieldNexxId: number;
  entityLabel: string;
  fieldPreviewImage: {
    entity: {
      fieldMediaImage: FieldMediaImage;
    };
  };
}

export interface IMediaQueryImage {
  __typename: 'MediaImage';
  mid: number;
  entityLabel: string;
  fieldMediaImage: FieldMediaImage;
  reverseMainImagesGirl: {
    entities:
      | []
      | [
          {
            entityLabel: string;
            entityId: string;
          },
        ];
  };
}

export interface IMediaQueryGallery {
  __typename: 'MediaGallery';
  mid: number;
  entityLabel: string;
  fieldMediaImage: FieldMediaImage;
}

export type MediaQueryResult =
  | IMediaQueryNexxVideo
  | IMediaQueryImage
  | IMediaQueryGallery;
