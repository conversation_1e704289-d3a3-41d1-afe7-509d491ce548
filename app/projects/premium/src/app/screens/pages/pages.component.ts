import { distinctJ<PERSON><PERSON> } from '@pb/ui';
import { Apollo } from 'apollo-angular';
import { map, mergeMap, switchMap, tap, filter } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IStaticPage } from './definitions/models';
import { GET_STATIC_PAGE } from './definitions/queries';
import { ApiService } from '../../services/api.service';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-pages',
  templateUrl: './pages.component.html',
  styleUrls: ['./unreset.scss', './pages.component.css'],
  encapsulation: ViewEncapsulation.Emulated,
  imports: [AsyncPipe],
})
export class PagesComponent implements OnInit {
  $pageUrl: Observable<string>;
  $page: Observable<IStaticPage | undefined>;

  constructor(route: ActivatedRoute, api: ApiService, router: Router) {
    this.$pageUrl = route.paramMap.pipe(map((v) => '/' + v.get('page_url')));
    this.$page = this.$pageUrl.pipe(
      switchMap((url) =>
        api.get<{ title: string; field_body: string }[]>(`static${url}`),
      ),
      // map(v => v.data.pbStaticPages.results[0]),
      map((v) => v[0]),
      tap((v) => {
        if (!v) {
          router.navigate(['/404']);
        }
      }),
      distinctJSON(),
    );
  }

  ngOnInit(): void {}
}
