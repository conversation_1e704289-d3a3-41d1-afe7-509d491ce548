<h2 class="my-6 md:my-20 w-full text-center p-4">Suche</h2>

@if ($loading | async) {
  <div
    class="bg-black bg-opacity-25 h-screen left-0 fixed top-0 w-screen z-40 flex justify-center items-center pointer-events-none"
    @loadingAnimation
  >
    <img src="assets/bunnyanimation-white.gif" alt="Loading..." class="w-20" />
  </div>
}

<form
  [formGroup]="formGroup"
  class="flex flex-col items-center relative mx-auto justify-center"
>
  <div class="w-full mb-0 md:-mb-6 z-30">
    <app-search-input
      [autofocus]="true"
      formControlName="search"
      placeholder="Suche"
    ></app-search-input>
    <div class="mb-6 md:mb-15"></div>
    <lib-filter-dropdown
      (selectedFilterChange)="selectFilter($event)"
      (selectedSortChange)="selectSorting($event)"
      [filterOptions]="$filterOptions | async"
      [selectedFilter]="$selectedFilter | async"
      [selectedSorting]="$selectedSorting | async"
      [sortingOptions]="sortingOptions"
      style="max-width: 1696px; justify-content: center"
    >
    </lib-filter-dropdown>
  </div>
</form>

<!--<section class="container">-->
<!--  <lib-breadcrumbs-->
<!--    [breadcrumbs]="[{ link: '/search', label: 'Suche' }]"-->
<!--    class="mt-8 md:mt-0"-->
<!--  >-->
<!--  </lib-breadcrumbs>-->
<!--</section>-->
@if (!($girls | async)?.length && !($loading | async)) {
  <div class="p-4 text-center my-6 md:mb-10 md:mt-0">
    Keine Ergebnisse für <b>{{ term }}</b> gefunden
  </div>
} @else if (!!term) {
  <h3 class="p-4 w-full text-center my-6 md:mb-15 md:mt-0" id="title-proposal">
    Ergebnisse für "{{ term }}"
  </h3>
}

<ng-template
  #article
  let-id="id"
  let-image="image"
  let-link="link"
  let-model="item"
>
  <a [routerLink]="link" class="w-full flex cursor-pointer h-full max-h-full">
    <app-gallery-card
      [image]="image"
      [paywallImages]="model?.paywallImages || []"
      [isAAContent]="model?.fieldPlusAccess !== true"
      [previewData]="model"
      [autoSize]="false"
      class="w-full"
    ></app-gallery-card>
  </a>
</ng-template>

@if ($girls | async; as girls) {
  <app-pagination-grid
    [paginationAnimationElementSelector]="'app-search'"
    [items]="girls"
    [page]="$page | async"
    [pages]="$pageAmount | async"
    [loading]="$loading | async"
    [template]="article"
  ></app-pagination-grid>
}
