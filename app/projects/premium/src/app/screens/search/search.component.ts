import {
  animate,
  keyframes,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Meta, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { distinctJSON, FilterDropdownComponent } from '@pb/ui';
import { IFilterOptions } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { $ShownPerScreen } from '@pb/ui/utils/screen';
import { IPreview } from 'projects/premium/src/app/models/preview';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  skip,
} from 'rxjs/operators';
import { CategoriesService } from '../../services/categories/categories.service';

import {
  GirlInfoListQueryParams,
  GirlInfoService,
  ICategoryFilterOptions,
  IGirlInfoServiceData,
  SortOptions,
} from '../../services/girl-info/girl-info.service';
import { ICategoryQueryVariableSort } from '../../services/girl-info/models';
import {
  GenerateMainQueryParams,
  IGirlInfoPreviewByDescriptionIdQueryParams,
} from '../../services/girl-info/queries';
import { AsyncPipe } from '@angular/common';
import { GalleryCardComponent, PaginationGridComponent } from '../../shared';
import { SearchInputComponent } from '../../components/search-input/search-input.component';

@Component({
  selector: 'app-search',
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.css'],
  animations: [
    trigger('loadingAnimation', [
      transition('void => *', [
        animate(
          '250ms ease-out',
          keyframes([style({ opacity: 0 }), style({ opacity: 1 })]),
        ),
      ]),
      transition('* => void', [
        animate(
          '250ms ease-out',
          keyframes([style({ opacity: 1 }), style({ opacity: 0 })]),
        ),
      ]),
    ]),
  ],
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    FilterDropdownComponent,
    RouterLink,
    GalleryCardComponent,
    PaginationGridComponent,
    SearchInputComponent,
  ],
})
export class SearchComponent implements OnInit, OnDestroy {
  // $queryResult: Observable<IGirlInfoServiceData>;
  private _queryResultSubject = new BehaviorSubject<IGirlInfoServiceData>(null);
  private loadingSubject = new BehaviorSubject(true);

  $girls: Observable<IPreview[]> = this._queryResultSubject.pipe(
    map(
      (v) =>
        v?.data.map(({ image, imageLowRes, ...preview }) => ({
          ...preview,
          image: imageLowRes,
        })) || [],
    ),
    distinctJSON(),
  );

  $headerImage: Observable<IPreview | undefined> = this.$girls.pipe(
    map((v) => v[0]),
    distinctJSON(),
  );

  term: String;
  get Loading(): boolean {
    return this.loadingSubject.getValue();
  }
  set Loading(loading: boolean) {
    this.loadingSubject.next(loading);
  }
  $loading = this.loadingSubject;

  searchInput = new UntypedFormControl('', Validators.required);
  formGroup = new UntypedFormGroup({
    search: this.searchInput,
  });

  $pageAmount = combineLatest([$ShownPerScreen, this._queryResultSubject]).pipe(
    filter(([_, v]) => !!v),
    map(([perScreen, { count }]) => Math.ceil(count / perScreen)),
    distinctUntilChanged((a, b) => a === b),
  );
  $page: Observable<number>;

  private selectedSorting = new BehaviorSubject<SortOptions | undefined>(
    undefined,
  );
  private selectedFilter = new BehaviorSubject<Partial<ICategoryFilterOptions>>(
    {},
  );

  private $activeSorting: Observable<ICategoryQueryVariableSort>;
  public $selectedSorting = this.selectedSorting.pipe(distinctJSON());
  public $selectedFilter = this.selectedFilter.pipe(distinctJSON());

  sortingOptions: SortOptions[] = [];
  $filterOptions: Observable<IFilterOptions>;

  private sub: Subscription;
  private loadingSub: Subscription;
  private girlsSub: Subscription;
  private paramsSub: Subscription;

  public set Page(page: number) {
    this.router.navigate([], {
      queryParams: { page },
      queryParamsHandling: 'merge',
    });
  }

  constructor(
    titleService: Title,
    private router: Router,
    private route: ActivatedRoute,
    private meta: Meta,
    private girlInfo: GirlInfoService,
    categories: CategoriesService,
  ) {
    this.$page = route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );

    titleService.setTitle('Search | Playboy All Access');
    this.searchInput.setValue((route.snapshot.queryParams as any).query, {
      onlySelf: true,
    });
    this.term = (route.snapshot.queryParams as any).query;

    this.sub = this.searchInput.valueChanges
      .pipe(distinctUntilChanged(), debounceTime(300))
      .subscribe((searchTerm) => {
        this.term = searchTerm;
        this.Loading = true;
        return this.router.navigate([], { queryParams: { query: searchTerm } });
      });

    this.$filterOptions = combineLatest([
      girlInfo.$filterOptions,
      categories.$filterOptions,
    ]).pipe(
      map(([defaultFilters, categoryFilters]) => ({
        ...defaultFilters,
        ...categoryFilters,
      })),
    );
    this.$activeSorting = girlInfo.mapSortingObservable(this.$selectedSorting);
    this.sortingOptions = girlInfo.sortingOptions;
  }

  ngOnInit() {
    const $filterParams = combineLatest([
      this.$selectedFilter,
      this.$activeSorting,
    ]).pipe(
      distinctJSON(),
      map((d) => GenerateMainQueryParams(...d)),
      distinctJSON(),
    );
    const $params = combineLatest([
      $ShownPerScreen,
      this.$page,
      this.route.queryParamMap.pipe(
        map((v) => v.get('query')),
        distinctUntilChanged((a, b) => a === b),
      ),
      $filterParams,
    ]).pipe(
      debounceTime(300),
      distinctJSON(),
      map(
        ([perScreen, screen, searchName, params]): [
          GirlInfoListQueryParams,
          Partial<IGirlInfoPreviewByDescriptionIdQueryParams>,
        ] => [
          { perScreen, screen },
          { ...params, searchName },
        ],
      ),
    );
    this.girlsSub = this.girlInfo
      .getGirlInfoPreviews($params)
      .subscribe((data) => this._queryResultSubject.next(data));

    this.loadingSub = this._queryResultSubject.subscribe(
      (d) => (this.Loading = !!d?.loading),
    );

    this.paramsSub = $filterParams
      .pipe(skip(1))
      .subscribe(() => (this.Page = 0));
    this.meta.removeTag(`name='og:site_name'`);
    this.meta.removeTag(`name='og:type'`);
    this.meta.removeTag(`name='og:title'`);
    this.meta.removeTag(`name='og:description'`);
    this.meta.removeTag(`name='og:locale'`);
    this.meta.removeTag(`name='og:url'`);
    this.meta.removeTag(`name='og:image'`);
    this.meta.removeTag(`name='article:published_time'`);
    this.meta.removeTag(`name='profile:first_name'`);
    this.meta.removeTag(`name='profile:last_name'`);
    this.meta.removeTag(`name='profile:gender'`);
    this.meta.removeTag(`name='description'`);
    this.meta.removeTag(`name='author'`);
    this.meta.removeTag(`name='meta'`);

    this.meta.addTag({ name: 'og:site_name', content: 'Playboy All Access' });
    this.meta.addTag({ name: 'og:type', content: 'website' });
    this.meta.addTag({ name: 'og:title', content: 'Suche' });
    this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
    this.meta.addTag({
      name: 'og:url',
      content: 'https://premium.playboy.de/search',
    });
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
    this.loadingSub?.unsubscribe();
    this.girlsSub?.unsubscribe();
    this.paramsSub?.unsubscribe();
  }

  trackByGirl(index: number, girl: IPreview) {
    return girl.id;
  }

  selectSorting(data: SortOptions): void {
    this.selectedSorting.next(data);
    this.Page = 0;
  }

  selectFilter(data: { [key: string]: string[] }): void {
    this.selectedFilter.next(data);
    this.Page = 0;
  }
}
