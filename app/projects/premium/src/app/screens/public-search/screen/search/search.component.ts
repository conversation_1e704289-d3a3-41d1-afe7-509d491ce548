import { Component, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { distinctJSON } from '@pb/ui';
import { IFilterOptions } from '@pb/ui/components/filter-dropdown/filter-dropdown.component';
import { IPreview } from 'projects/premium/src/app/models/preview';
import { SortOptions } from 'projects/premium/src/app/services/girl-info/girl-info.service';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';

import { AsyncPipe } from '@angular/common';
import {
  ReactiveFormsModule,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Meta, Title } from '@angular/platform-browser';

import { PublicService } from 'projects/premium/src/app/services/public.service';
import { PaginationGridComponent } from 'projects/premium/src/app/shared';
import {
  ICategoryFilterOptions,
  ICategoryQueryVariableSort,
} from '../../../../services/girl-info/models';
import { GalleryCardComponent } from '../../../../shared/components/gallery-card/gallery-card.component';
import { SearchInputComponent } from '../../../../components/search-input/search-input.component';

@Component({
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.css'],
  imports: [
    AsyncPipe,
    RouterLink,
    GalleryCardComponent,
    PaginationGridComponent,
    ReactiveFormsModule,
    SearchInputComponent,
  ],
})
export class SearchComponent implements OnDestroy {
  private selectedSorting = new BehaviorSubject<SortOptions | undefined>(
    undefined,
  );
  private selectedFilter = new BehaviorSubject<Partial<ICategoryFilterOptions>>(
    {},
  );

  public $selectedSorting = this.selectedSorting.pipe(distinctJSON());

  private $activeSorting: Observable<ICategoryQueryVariableSort>;
  public $selectedFilter = this.selectedFilter.pipe(distinctJSON());

  sortingOptions: SortOptions[] = [];
  $filterOptions: Observable<IFilterOptions>;
  $pageAmount: Observable<number>;

  models: IPreview[];
  pages: number;
  page: number = 0;
  loading = true;

  $page: Observable<number>;

  term: String;
  searchInput = new UntypedFormControl('', Validators.required);
  formGroup = new UntypedFormGroup({
    search: this.searchInput,
  });

  private inputSub: Subscription;
  private gallerySub: Subscription;

  constructor(
    private publicData: PublicService,
    route: ActivatedRoute,
    private meta: Meta,
    public readonly router: Router,
    titleService: Title,
  ) {
    // set page title
    titleService.setTitle('Search | Playboy All Access');

    // handle page parameter from the URL
    this.$page = route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );

    // set form value based on URL initially
    this.searchInput.setValue((route.snapshot.queryParams as any).query, {
      onlySelf: true,
    });

    // set initial term based on URL initially
    this.term = (route.snapshot.queryParams as any).query;

    // subscription to listen for input changes
    this.inputSub = this.searchInput.valueChanges
      .pipe(distinctUntilChanged(), debounceTime(500))
      .subscribe((searchTerm) => {
        // set input search
        this.term = searchTerm;
        // update URL to entered search term
        return this.router.navigate([], { queryParams: { query: searchTerm } });
      });

    // react on changes for page and
    combineLatest([route.queryParamMap, this.$page])
      .pipe(distinctUntilChanged())
      .subscribe((e) => {
        // turn on loading immediately on change
        this.loading = true;

        if (!!this.term) {
          // load gallery
          this.gallerySub = this.publicData
            .getGirlInfos(e[1], null, this.term)
            .subscribe((data) => {
              this.models = data.map((model) => {
                return {
                  ...model,
                  image: model.imageLowRes,
                };
              });

              // turn off loading
              this.loading = false;

              // unsubscribe the sub again
              this.gallerySub.unsubscribe();
            });
        } else {
          // turn off loading for fallback
          this.loading = false;
        }

        // load total galleries count
        this.$pageAmount = this.publicData.getGirlInfosCount();
      });
  }
  ngOnInit() {
    this.meta.removeTag(`name='og:site_name'`);
    this.meta.removeTag(`name='og:type'`);
    this.meta.removeTag(`name='og:title'`);
    this.meta.removeTag(`name='og:description'`);
    this.meta.removeTag(`name='og:locale'`);
    this.meta.removeTag(`name='og:url'`);
    this.meta.removeTag(`name='og:image'`);
    this.meta.removeTag(`name='article:published_time'`);
    this.meta.removeTag(`name='profile:first_name'`);
    this.meta.removeTag(`name='profile:last_name'`);
    this.meta.removeTag(`name='profile:gender'`);
    this.meta.removeTag(`name='description'`);
    this.meta.removeTag(`name='author'`);
    this.meta.removeTag(`name='meta'`);

    this.meta.addTag({ name: 'og:site_name', content: 'Playboy All Access' });
    this.meta.addTag({ name: 'og:type', content: 'website' });
    this.meta.addTag({ name: 'og:title', content: 'Suche' });
    this.meta.addTag({ name: 'og:locale', content: 'de_DE' });
    this.meta.addTag({
      name: 'og:url',
      content: 'https://premium.playboy.de/search',
    });
  }

  selectSorting(data: SortOptions): void {
    this.selectedSorting.next(data);
  }

  selectFilter(data: { [key: string]: string[] }): void {
    this.selectedFilter.next(data);
  }

  ngOnDestroy() {
    this.inputSub?.unsubscribe();
    this.gallerySub?.unsubscribe();
  }
}
