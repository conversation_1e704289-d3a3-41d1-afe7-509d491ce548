import { Router, Routes } from '@angular/router';
import { CouponComponent } from './screens/coupon/coupon.component';

import { inject } from '@angular/core';
import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { LoggedInGuard } from './guards/logged-in.guard';
import { MaintainanceGuard } from './guards/maintainance.guard';
import { NotMaintainanceGuard } from './guards/not-maintainance.guard';
import { NotSubscribedGuard } from './guards/not-subscribed.guard';
import { SubscribedGuard } from './guards/subscribed.guard';
import { BildplusComponent } from './screens/bildplus/bildplus.component';
import { MaintainanceComponent } from './screens/maintainance/maintainance.component';
import { NotFoundComponent } from './screens/not-found/not-found.component';
import { ProfileComponent } from './screens/profile/profile.component';
import { SearchComponent } from './screens/public-search/screen/search/search.component';
import { AccountService } from './services/account.service';

export const routes: Routes = [
  {
    canActivate: [NotMaintainanceGuard],
    path: 'maintainance',
    component: MaintainanceComponent,
    data: {
      disableFooterSpacing: true,
    },
  },
  {
    canActivate: [SubscribedGuard, MaintainanceGuard],
    path: '',
    loadChildren: () => import('./screens/landing/landing-routing.module'),
  },
  {
    path: 'girl',
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    loadChildren: () => import('./screens/model/model-routing.module'),
  },
  {
    path: 'p/girl',
    canActivate: [MaintainanceGuard],
    loadChildren: () => import('./screens/public/public-routing.module'),
  },
  {
    canActivate: [MaintainanceGuard],
    path: 'p/categories',
    loadChildren: () =>
      import('./screens/public-category/public-category-routing.module'),
  },
  {
    path: 'p/search',
    component: SearchComponent,
    canActivate: [MaintainanceGuard],
  },
  {
    path: 'bildplus/:coupon',
    canActivate: [NotSubscribedGuard, MaintainanceGuard],
    component: BildplusComponent,
  },

  {
    path: 'videos',
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    loadChildren: () => import('./screens/videos/videos-routing.module'),
  },
  {
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    path: 'girl-info/:girl_info_id/:resource_id',
    data: { navbar: false },
    loadChildren: () => import('./screens/gallery/gallery-routing.module'),
  },
  {
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    path: 'categories',
    loadChildren: () =>
      import('./screens/categories/categories-routing.module'),
  },
  {
    canActivate: [LoggedInGuard, MaintainanceGuard],
    path: 'profile',
    component: ProfileComponent,
  },
  {
    path: 'c/:slug',
    component: CouponComponent,
    canActivate: [NotSubscribedGuard, MaintainanceGuard],
  },
  {
    path: 'c/:slug/:coupon',
    component: CouponComponent,
    canActivate: [NotSubscribedGuard, MaintainanceGuard],
  },

  { path: '404', component: NotFoundComponent },

  {
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    path: 'favorites',
    loadChildren: () => import('./screens/favorites/favorites-routing.module'),
  },
  {
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    path: 'e-paper',
    loadChildren: () => import('./screens/e-paper/e-paper-routing.module'),
  },
  {
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    path: 'e-paper-special',
    loadChildren: () =>
      import('./screens/e-paper-special/e-paper-special-routing.module'),
  },
  {
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    path: 'girl-infos',
    loadChildren: () =>
      import('./screens/girl-infos/girl-infos-routing.module'),
  },
  {
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    path: 'search',
    loadChildren: () => import('./screens/search/search-routing.module'),
  },
  {
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    path: 'premium-tv',
    loadChildren: () =>
      import('./screens/premium-tv/premium-tv-routing.module'),
  },
  {
    path: 'admin',
    canMatch: [
      () => {
        const acc = inject(AccountService);
        const router = inject(Router);
        return combineLatest([acc.Admin, acc.LoggedIn]).pipe(
          map(([admin, loggedIn]) => {
            if ((loggedIn && !admin) || !window) {
              return router.createUrlTree(['/']);
            } else {
              window.location.href = 'https://api.premium.playboy.de/';
            }
          }),
        );
      },
    ],
    redirectTo: '404',
  },
  {
    path: 'pages/:page_url',
    loadChildren: () =>
      import('./screens/pages/pages.module').then((m) => m.PagesModule),
  },
  {
    path: 'p/:modular_url',
    canActivate: [MaintainanceGuard],
    loadComponent: () => import('./screens/public-modular/modular.component').then(m => m.ModularComponent),
  },
  {
    path: ':modular_url',
    canActivate: [LoggedInGuard, SubscribedGuard, MaintainanceGuard],
    loadComponent: () => import('./screens/modular/modular.component').then(m => m.ModularComponent),
  },
  { path: '**', redirectTo: '404' },
];
