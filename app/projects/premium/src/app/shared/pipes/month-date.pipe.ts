import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'pbMonthDateFormat',
  standalone: true,
})
export class MonthDatePipe implements PipeTransform {
  transform(release?: string): string {
    if (!release) {
      return '';
    }

    const date = new Date(release);
    if (!(date instanceof Date)) {
      // invalid date!
      return '';
    }

    return (
      date.toLocaleDateString('de-DE', { year: 'numeric', month: 'long' }) || ''
    );
  }
}
