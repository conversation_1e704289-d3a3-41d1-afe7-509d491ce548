import { Pipe, PipeTransform } from '@angular/core';
import { ICategory } from '../../services/categories/categories.service';

export function CategoryNameToSlug(name: string): string {
  return name.toLowerCase().replace(' ', '-');
}

@Pipe({
  name: 'categorySlug',
  standalone: true,
})
export class CategorySlugPipe implements PipeTransform {
  transform(value: ICategory | string): string {
    if (!value) {
      return 'unknown';
    }
    return CategoryNameToSlug(
      typeof value === 'object' ? value.entityLabel : value,
    );
  }
}
