import { Pipe, PipeTransform } from '@angular/core';
import { Observable } from 'rxjs';

import {
  CategoriesService,
  ICategory,
} from '../../services/categories/categories.service';

@Pipe({
  name: 'category',
  standalone: true,
})
export class CategoryPipe implements PipeTransform {
  constructor(private categoriesService: CategoriesService) {}

  transform(id: number): Observable<ICategory | undefined> {
    return this.categoriesService.getById(id);
  }
}
