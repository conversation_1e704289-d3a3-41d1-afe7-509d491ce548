import { Pipe } from '@angular/core';

export function GetFocalPoint(data?: {
  fieldFocalPointX: number;
  fieldFocalPointY: number;
}): { x: number; y: number } | undefined {
  return data?.fieldFocalPointX && data?.fieldFocalPointY
    ? { x: data.fieldFocalPointX, y: data.fieldFocalPointY }
    : undefined;
}

@Pipe({
  name: 'focalPoint',
  standalone: true,
})
export class FocalPointPipe {
  transform(data?: {
    fieldFocalPointX: number;
    fieldFocalPointY: number;
  }): { x: number; y: number } | undefined {
    return GetFocalPoint(data);
  }
}
