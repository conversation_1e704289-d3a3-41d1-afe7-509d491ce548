import { map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { Observable, Subscription } from 'rxjs';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { environment } from 'projects/premium/src/environments/environment';
import { PromoService } from '../../../services/promo.service';

@Component({
  selector: 'app-promotional-separator',
  templateUrl: './promotional-separator.component.html',
  styleUrls: ['./promotional-separator.component.css'],
  standalone: true,
})
export class PromotionalSeparatorComponent implements OnDestroy {
  banner: {
    image: string;
    title: string;
    price: string;
  };

  sub: Subscription;

  constructor(promo: PromoService) {
    this.sub = promo.Data.pipe(map((v) => v.banner)).subscribe(
      (v) => (this.banner = v),
    );
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
  }
}
