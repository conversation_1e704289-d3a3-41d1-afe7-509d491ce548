import { Component } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { $ShownPerScreen } from '@pb/ui/utils/screen';
import { combineLatest, Observable } from 'rxjs';
import { distinctUntilChanged, filter, map } from 'rxjs/operators';

import {
  GirlInfoService,
  IGirlInfoServiceData,
} from '../../../services/girl-info/girl-info.service';
import { GalleryCardComponent } from '../gallery-card/gallery-card.component';
import { AsyncPipe } from '@angular/common';
import { PaginationGridComponent } from '../pagination-grid/pagination-grid.component';

@Component({
  selector: 'app-article-pagination-grid',
  templateUrl: './article-pagination-grid.component.html',
  styleUrls: [],
  imports: [
    GalleryCardComponent,
    RouterLink,
    AsyncPipe,
    PaginationGridComponent,
  ],
})
export class ArticlePaginationGridComponent {
  $data?: Observable<IGirlInfoServiceData>;
  $page: Observable<number>;
  $pageAmount: Observable<number>;
  $loading: Observable<boolean>;

  constructor(girlInfoService: GirlInfoService, route: ActivatedRoute) {
    this.$page = route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );

    this.$data = girlInfoService.getGirlInfoPreviews(
      combineLatest([$ShownPerScreen, this.$page]).pipe(
        map(([perScreen, screen]) => [{ perScreen, screen }, {}]),
      ),
    );

    this.$pageAmount = combineLatest([$ShownPerScreen, this.$data]).pipe(
      filter(([_, v]) => !!v),
      map(([perScreen, { count }]) => Math.ceil(count / perScreen)),
      distinctUntilChanged((a, b) => a === b),
    );
    this.$loading = this.$data.pipe(map((v) => v.loading));
  }
}
