<ng-template
  #article
  let-id="id"
  let-link="link"
  let-model="item"
  let-image="image"
>
  <a class="w-full h-full flex cursor-pointer" [routerLink]="link">
    <app-gallery-card
      class="w-full h-full max-h-full"
      [previewData]="model"
      [image]="image"
    ></app-gallery-card>
  </a>
</ng-template>

@if ($data | async; as data) {
  <app-pagination-grid
    [items]="data.data"
    [page]="$page | async"
    [pages]="$pageAmount | async"
    [loading]="$loading | async"
    [template]="article"
  ></app-pagination-grid>
}
