import { Component, HostBinding, Input, OnInit } from '@angular/core';
import { IPackage } from '../../../services/packages.service';
import { ButtonComponent } from '@pb/ui';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-package',
  templateUrl: './package.component.html',
  styleUrls: ['./package.component.css'],
  imports: [ButtonComponent, NgClass],
})
export class PackageComponent implements OnInit {
  @Input() set package(p: IPackage) {
    this.months = p.months;
    this.monthlyPrice = p.monthlyPrice;
    this.fullPrice = p.fullPrice;
    this.newPrice = p.newPrice;
    this.link = p.link;
    this.recommendation = !!p.recommendation;
  }
  clicked = false;

  @Input() months: number;
  @Input() monthlyPrice: string;
  @Input() fullPrice: string;
  @Input() newPrice?: string;
  @Input() link: string;

  @HostBinding('class.is-selected')
  @Input()
  selected = false;

  @HostBinding('class.is-single')
  @Input()
  single = false;

  @HostBinding('class.is-recommendation')
  @Input()
  recommendation?: boolean;

  constructor() {}

  ngOnInit(): void {}
}
