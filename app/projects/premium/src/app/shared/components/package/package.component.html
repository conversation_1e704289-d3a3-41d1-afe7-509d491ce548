@if (recommendation) {
  <lib-button class="absolute top-0 right-0 rounded-md m-3 pointer-events-none">
    UNSER TIPP
  </lib-button>
}
<div class="flex flex-wrap" [ngClass]="{ 'justify-center': single }">
  <div
    [ngClass]="{
      'w-full justify-center flex flex-row items-baseline': single,
      'w-1/2 sm:w-full': !single,
    }"
  >
    <p class="text-golden font-bauer text-8xl sm:text-9xl">
      {{ months }}
    </p>
    <h3 class="text-golden text-3xl sm:text-4xl">
      Monat{{ months > 1 ? "e" : "" }}
    </h3>
  </div>

  <div
    class="flex flex-col justify-end w-1/2 sm:w-full"
    [ngClass]="{ 'w-1/2 sm:w-full': !single }"
  >
    <p class="font-stag text-2xl sm:text-4xl my-0 sm:my-4">
      @if (newPrice) {
        <span class="line-through opacity-50">{{ monthlyPrice }}</span>
        {{ newPrice }}
      }
      @if (!newPrice) {
        {{ monthlyPrice }}
      }
      <span class="text-sm md:text-lg">mtl.</span>
    </p>
    <p class="text-sm sm:text-lg">
      {{ fullPrice }}<br />
      für {{ months }} Monat{{ months > 1 ? "e" : "" }}
    </p>
  </div>
  <div
    class="flex mt-12 mb:mt-8 mb-8"
    [class.w-full]="single"
    [ngClass]="{ 'cursor-wait': clicked }"
  >
    <a
      [href]="link"
      [class.mx-auto]="single"
      [ngClass]="{ 'pointer-events-none opacity-40': clicked }"
      (click)="clicked = true"
    >
      <lib-button class="py-4 px-8"> Paket wählen </lib-button>
    </a>
  </div>
</div>
