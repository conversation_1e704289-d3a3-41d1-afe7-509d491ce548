import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { distinctJSON } from '@pb/ui';
import { $ShownPerScreen } from '@pb/ui/utils/screen';
import { IPreview } from '../../../../models/preview';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
} from 'rxjs/operators';

import {
  GirlInfoListQueryParams,
  GirlInfoService,
  IGirlInfoServiceData,
} from '../../../../services/girl-info/girl-info.service';
import { AsyncPipe } from '@angular/common';
import { GalleryCardComponent, PaginationGridComponent } from '../../../index';

@Component({
  selector: 'feed-module',
  templateUrl: './feed-module.component.html',
  imports: [
    AsyncPipe,
    RouterLink,
    GalleryCardComponent,
    PaginationGridComponent,
  ],
})
export class FeedModuleComponent implements OnInit, OnDestroy {
  @Input() title: string;

  private _queryResultSubject = new BehaviorSubject<IGirlInfoServiceData>(null);

  $girls: Observable<IPreview[]> = this._queryResultSubject.pipe(
    map(
      (v) =>
        v?.data.map(({ image, imageLowRes, ...preview }) => ({
          ...preview,
          image: imageLowRes,
        })) || [],
    ),
    distinctJSON(),
  );

  $pageAmount = combineLatest([$ShownPerScreen, this._queryResultSubject]).pipe(
    filter(([_, v]) => !!v),
    map(([perScreen, { count }]) => Math.ceil(count / perScreen)),
    distinctUntilChanged((a, b) => a === b),
  );
  $page: Observable<number>;

  private girlsSub: Subscription;

  public set Page(page: number) {
    this.router.navigate([], {
      queryParams: { page },
      queryParamsHandling: 'merge',
    });
  }

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private girlInfo: GirlInfoService,
  ) {
    this.$page = route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );
  }

  ngOnInit() {
    const $params = combineLatest([
      $ShownPerScreen,
      this.$page,
    ]).pipe(
      debounceTime(300),
      distinctJSON(),
      map(
        ([perScreen, screen]): [GirlInfoListQueryParams, {}] => [
          { perScreen, screen },
          {},
        ],
      ),
    );

    this.girlsSub = this.girlInfo
      .getGirlInfoPreviews($params)
      .subscribe((data) => this._queryResultSubject.next(data));
  }

  ngOnDestroy(): void {
    this.girlsSub?.unsubscribe();
  }
}
