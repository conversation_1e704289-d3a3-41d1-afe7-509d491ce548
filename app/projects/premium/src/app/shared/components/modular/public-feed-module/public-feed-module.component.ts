import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { distinctJSON } from '@pb/ui';
import { $ShownPerScreen } from '@pb/ui/utils/screen';
import { IPreview } from '../../../../models/preview';
import { BehaviorSubject, combineLatest, Observable, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
} from 'rxjs/operators';

import { AsyncPipe } from '@angular/common';

import { PublicService } from 'projects/premium/src/app/services/public.service';
import { PaginationGridComponent } from 'projects/premium/src/app/shared';
import { GalleryCardComponent } from '../../gallery-card/gallery-card.component';
import { IsNew } from '../../../../utils/newCheck';

@Component({
  selector: 'public-feed-module',
  templateUrl: './public-feed-module.component.html',
  imports: [
    AsyncPipe,
    RouterLink,
    GalleryCardComponent,
    PaginationGridComponent,
  ],
})
export class PublicFeedModuleComponent implements OnInit, OnDestroy {
  @Input() title: string;

  private _queryResultSubject = new BehaviorSubject<IPreview[]>([]);

  $girls: Observable<IPreview[]> = this._queryResultSubject.pipe(
    distinctJSON(),
  );

  $pageAmount: Observable<number>;
  $page: Observable<number>;

  private girlsSub: Subscription;

  public set Page(page: number) {
    this.router.navigate([], {
      queryParams: { page },
      queryParamsHandling: 'merge',
    });
  }

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private publicData: PublicService,
  ) {
    this.$page = route.queryParamMap.pipe(
      map((v) => parseInt(v.get('page'))),
      map((v) => (isNaN(v) ? 0 : v)),
    );
  }

  ngOnInit() {
    const $params = combineLatest([
      $ShownPerScreen,
      this.$page,
    ]).pipe(
      debounceTime(300),
      distinctJSON(),
    );

    this.girlsSub = $params.subscribe(([perScreen, page]) => {
      this.publicData.getGirlInfos(page, null, null).subscribe((data) => {
        const girls = data.map((model) => ({
          ...model,
          isNew: IsNew(model.field_latest_gallery_release),
          image: model.imageLowRes,
        }));

        this._queryResultSubject.next(girls);
      });
    });

    this.$pageAmount = this.publicData.getGirlInfosCount();
  }

  ngOnDestroy(): void {
    this.girlsSub?.unsubscribe();
  }
}
