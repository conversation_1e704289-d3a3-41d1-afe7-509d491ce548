import { Directive, Input } from '@angular/core';
import { FavoriteType } from '../../../services/favorites/favorites.service';

export interface ICardCarouselSlideData {
  image: string;
  title: string;
  text: string;
  year: number;
  month: number;
  nexxID?: string;
  inlineVideo?: true;
  link?: string | number | (string | number)[];
  new?: true;
  favoriteType?: FavoriteType | undefined;
  favoriteId?: string | undefined;
  isAAContent?: boolean;
}

@Directive({
  selector: '[cardCarouselSlide]',
  standalone: true,
})
export class CardCarouselSlideDirective implements ICardCarouselSlideData {
  @Input() image: string;
  @Input() title: string;
  @Input() month: number;
  @Input() year: number;
  @Input() text: string;
  @Input() link?: string | number | (string | number)[];
  @Input() nexxID?: string;
  @Input() inlineVideo?: true;
  @Input() new?: true;
  @Input() meta?: {
    images: number;
    videos: number;
    girlInfos: number;
  };
  @Input() favoriteType?: FavoriteType | undefined;
  @Input() favoriteId?: string | undefined;
  @Input() isAAContent?: boolean;
}
