@if (title) {
  <h2 class="mb-6 md:mb-10 py-4 w-full text-center">{{ title }}</h2>
}

<ng-container>
  <swiper-container
    #swiperContainer
    class="w-full flex swiper-container"
    [attr.auto-height]="false"
    [attr.slides-per-view]="('md' | responsive | async) ? slidesPerView : 1.3"
    [attr.slides-per-group]="('md' | responsive | async) ? slidesPerView : 1"
    [attr.centered-slides]="true"
    [attr.space-between]="12"
    [attr.pagination]="{ clickable: true }"
    (swiperactiveindexchange)="
      refreshIndex(swiperContainer?.swiper.activeIndex)
    "
  >
    @for (item of slides; track $index) {
      <swiper-slide>
        <a [routerLink]="item.link">
          @switch (item.type) {
            @default {
              <app-gallery-card
                [image]="item.image"
                [month]="item.month"
                [year]="item.year"
                [title]="item.title"
                [name]="item.text"
                [nexxID]="item.nexxID"
                [inlineVideo]="item.inlineVideo"
                [isNew]="item.new"
                [meta]="item.meta"
                [favoriteId]="item.favoriteId ?? null"
                [favoriteType]="item.favoriteType ?? null"
                [isAAContent]="item?.isAAContent ?? undefined"
              >
              </app-gallery-card>
            }
          }
        </a>
      </swiper-slide>
    }
  </swiper-container>
  <!--<swiper #swiper  class="w-full flex"
  (activeIndexChange)="refreshIndex($event.activeIndex)"

  >
  <ng-template swiperSlide *ngFor="let item of slides">
    < !-- Gallery -- >
    <a [routerLink]="item.link" [ngSwitch]="item.type">
      <app-gallery-card *ngSwitchDefault [image]="item.image" [month]="item.month" [year]="item.year" [title]="item.title" [name]="item.text"
        [nexxID]="item.nexxID" [inlineVideo]="item.inlineVideo" [isNew]="item.new" [meta]="item.meta">
      </app-gallery-card>
    </a>
  </ng-template>
</swiper>-->
</ng-container>

@if (swiperContainer?.swiper?.slides?.length > slidesPerView) {
  <div
    class="flex flex-col md:flex-row w-full items-center md:items-end md:w-5/6 mx-auto md:mt-0 h-10"
  >
    <div class="hidden md:flex flex-1"></div>
    <div class="flex">
      @if (link && linkText) {
        <a
          class="leading-10 uppercase underline hover:no-underline relative z-10"
          [routerLink]="link"
          >{{ linkText }}</a
        >
      }
    </div>
  </div>
}
