import { CardCarouselSlideDirective } from './card-carousel-slide.directive';
import {
  Component,
  ContentChildren,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  Input,
  NgZone,
  QueryList,
  ViewChild,
} from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { ResponsivePipe } from '@pb/ui';
import { GalleryCardComponent } from '../gallery-card/gallery-card.component';
import { SwiperContainer } from 'swiper/element/bundle';

@Component({
  selector: 'app-card-carousel',
  templateUrl: './card-carousel.component.html',
  styleUrls: ['./card-carousel.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [RouterLink, ResponsivePipe, AsyncPipe, GalleryCardComponent],
})
export class CardCarouselComponent {
  @Input() title: string;
  @Input() link?: string | number | (string | number)[];
  @Input() linkText = 'Alle anzeigen';
  index = 0;
  @Input() slidesPerView = 4;

  @ViewChild('swiperContainer') swiperContainer: ElementRef<SwiperContainer>;

  @ContentChildren(CardCarouselSlideDirective)
  slides: QueryList<CardCarouselSlideDirective>;

  refreshIndex(index: number) {
    this.zone.run(() => {
      this.index = index;
    });
  }

  selectIndex(index: number) {
    this.swiperContainer.nativeElement.swiper.slideTo(index);
    this.zone.run(() => {
      this.index = index;
    });
  }

  constructor(private zone: NgZone) {}
}
