import {
  debounceTime,
  distinctUntilChanged,
  filter,
  startWith,
} from 'rxjs/operators';
import { Directive, ElementRef, NgZone, OnDestroy } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { ElResize, UnObserve } from '@pb/ui/utils/resizeObserver';

@Directive({
  selector: '[columnLayoutCell]',
  standalone: true,
})
export class ColumnLayoutCellDirective implements OnDestroy {
  private ratio = new BehaviorSubject<number | undefined>(2);
  $ratio = this.ratio.asObservable().pipe(
    filter((v) => !!v),
    distinctUntilChanged((a, b) => a === b),
  );

  sub?: Subscription;

  constructor(
    public element: ElementRef,
    private zone: NgZone,
  ) {
    this.sub?.unsubscribe();

    this.sub = ElResize(this.element.nativeElement)
      .pipe(startWith(), debounceTime(100))
      .subscribe(() => {
        const { width, height } =
          this.element.nativeElement.getBoundingClientRect();
        this.ratio.next(height / width);
        // console.log('resize', height / width)
      });
  }

  private observing = false;

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
    UnObserve(this.element.nativeElement);
  }
}
