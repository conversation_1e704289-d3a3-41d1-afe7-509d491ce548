import {
  animate,
  keyframes,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  Component,
  ContentChildren,
  ElementRef,
  HostBinding,
  Input,
  QueryList,
  ViewChildren,
} from '@angular/core';
import {
  BehaviorSubject,
  combineLatest,
  fromEvent,
  Subscription,
  timer,
} from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  startWith,
  switchMap,
  take,
  throttleTime,
  tap,
} from 'rxjs/operators';

import { ColumnLayoutCellDirective } from './column-layout-cell.directive';
import { ColumnLayoutItemDirective } from './column-layout-item.directive';
import { AsyncPipe, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-column-layout',
  templateUrl: './column-layout.component.html',
  styleUrls: ['./column-layout.component.css'],
  animations: [
    // trigger('shown', [
    //   state('false', style({ opacity: 0, marginTop: 100 })),
    //   transition('false => true', [
    //     animate('250ms ease-out', keyframes([
    //       style({ opacity: 0, marginTop: 100 }),
    //       style({ opacity: 1, marginTop: 5, transition: 'transform' }),
    //     ]))
    //   ])
    // ])
  ],
  imports: [NgTemplateOutlet, AsyncPipe, ColumnLayoutCellDirective],
})
export class ColumnLayoutComponent {
  @ContentChildren(ColumnLayoutItemDirective)
  items: QueryList<ColumnLayoutItemDirective>;
  @ViewChildren(ColumnLayoutCellDirective)
  cellsItems: QueryList<ColumnLayoutCellDirective>;

  private colsSubject = new BehaviorSubject(4);

  @Input() set columns(columns: number) {
    this.colsSubject.next(columns);
  }

  $cols = this.colsSubject.pipe(distinctUntilChanged((a, b) => a === b));
  positions: ({ col: number; y: number } | undefined)[] = [];

  @HostBinding('style.height.px') heightInPx: number = 0;

  private sub: Subscription;

  constructor(private readonly element: ElementRef) {}

  ngAfterViewInit(): void {
    this.sub?.unsubscribe();
    this.sub = this.cellsItems?.changes
      .pipe(
        startWith(this.cellsItems),
        switchMap((v: QueryList<ColumnLayoutCellDirective>) =>
          combineLatest([this.$cols, combineLatest(v.map((c) => c.$ratio))]),
        ),
        debounceTime(100),
        // map(v => JSON.parse(JSON.stringify(v))),
        map(([colAmount, items]) => {
          const cols: number[] = new Array(colAmount).fill(0);
          const rendered = items.map((item) => {
            if (!item) {
              return undefined;
            }
            // Find index of smallest column
            const selectedCol = cols.reduce(
              (smallestIndex, cVal, currentIndex) =>
                cols[smallestIndex] <= cVal ? smallestIndex : currentIndex,
              0,
            );

            // Use height of smallest column as the starting position for the new cell
            const y = cols[selectedCol];

            // Calculate new column height
            cols[selectedCol] += item;
            return { col: selectedCol, y };
          });
          return {
            positions: rendered,
            colAmount,
            height:
              cols.reduce(
                (tallest, current) => (tallest > current ? tallest : current),
                cols[0],
              ) / colAmount,
          };
        }),
        switchMap(({ colAmount, ...v }) =>
          fromEvent(window, 'resize').pipe(
            startWith(undefined),
            () => timer(200, 1000).pipe(take(2)),
            map(
              () =>
                this.element.nativeElement.getBoundingClientRect()
                  .width as number,
            ),
            map((width) => ({
              height: width * v.height,
              positions: v.positions.map((pos) => ({
                ...pos,
                y: pos.y * (width / colAmount),
              })),
            })),
            distinctUntilChanged(
              (a, b) =>
                a.height === b.height &&
                JSON.stringify(a.positions) === JSON.stringify(b.positions),
            ),
          ),
        ),
        throttleTime(100),
      )
      .subscribe((positions) => {
        this.heightInPx = positions.height;
        this.positions = positions.positions;
      });
  }

  ngOnDestroy(): void {
    this.sub?.unsubscribe();
  }

  trackByCell(index: number, item: ColumnLayoutItemDirective) {
    return item.columnLayoutItem;
  }

  trackByColumn(index: number) {
    return index;
  }

  // @ContentChildren(ColumnLayoutItemDirective) items: QueryList<ColumnLayoutItemDirective>;

  // private colsSubject = new BehaviorSubject(4);

  // @Input() set columns(columns: number) {
  //   this.colsSubject.next(columns);
  // }

  // $grid: Observable<ColumnLayoutItemDirective[][]> = of([]);

  // constructor() { }

  // trackByQLI(index: number, item: ColumnLayoutItemDirective) {
  //   return item.columnLayoutItem;
  // }

  // trackByColumn(index: number) {
  //   return index;
  // }

  // ngAfterContentInit(): void {
  //   this.$grid = combineLatest([
  //     this.colsSubject.pipe(distinctJSON()),
  //     this.items.changes.pipe(
  //       map((v: QueryList<ColumnLayoutItemDirective>) => v),
  //       startWith(this.items),
  //       map(v => v.toArray().sort((a, b) => a.template.elementRef.nativeElement.getBoundingClientRect().height - b.template.elementRef.nativeElement.getBoundingClientRect().height))
  //     )
  //   ]).pipe(
  //     map(([colSize, items]) => {
  //       const rowAmount = Math.ceil(items.length / colSize);
  //       return (new Array(colSize)).fill(null).map((_, col) =>
  //         (new Array(rowAmount)).fill(null).map((_, row) => {
  //           const index = col + row * colSize;
  //           if (items[(col + row * colSize)] && !items[(col + row * colSize)].columnLayoutItem) {
  //             items[(col + row * colSize)].columnLayoutItem = '' + index;
  //           }
  //           return items[(col + row * colSize)];
  //         }).filter(v => !!v)
  //       )
  //     })
  //   );
  // }
}
