import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  input,
} from '@angular/core';
import { ArticlePreviewComponent } from '../article-preview/article-preview.component';

@Component({
  selector: 'app-category-tile',
  template: `
    @if (tileTitle()) {
      <app-article-preview
        class="h-full w-full overflow-hidden"
        style="aspect-ratio: 1.5 / 1"
        [image]="image()"
        [imageAlt]="imageAlt()"
        [focalPoint]="focalPoint()"
      ></app-article-preview>
      <div
        class="absolute h-full w-full overflow-hidden top-0 left-0 right-0 bottom-0 m-auto bg-black bg-opacity-50 flex items-center justify-center p-8"
      >
        <div
          class="font-bauer text-2xl lg:text-4xl font-bold text-center inline-block "
        >
          {{ tileTitle() }}
        </div>
      </div>
    }
  `,
  styleUrls: ['./category-tile.component.css'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticlePreviewComponent],
})
export class CategoryTile {
  image = input<string>();
  imageAlt = input<string>();
  tileTitle = input<string>();
  focalPoint = input<{ x: number; y: number }>();
  protected readonly JSON = JSON;
}
