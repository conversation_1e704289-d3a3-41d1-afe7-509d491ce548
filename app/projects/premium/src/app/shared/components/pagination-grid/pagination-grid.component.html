@if (!loading && ($cols | async); as cols) {
  <div
    class="mt-6 flex flex-row grid grid-cols-1 xxl:grid-cols-6 lg:grid-cols-4 md:grid-cols-3 grid-rows-auto gap-x-6 gap-y-14"
  >
    @for (_ of [].constructor(rows); track $index; let row = $index) {
      @for (_ of [].constructor(cols); track $index; let col = $index) {
        @if (items[row * cols + col]; as item) {
          <div class="flex flex-col w-full">
            <ng-container
              *ngTemplateOutlet="template; context: buildContext(item)"
            ></ng-container>
          </div>
        }
      }
    }
  </div>
}

@if (pages > 0) {
  <app-route-pagination
    [animationElementSelector]="paginationAnimationElementSelector"
    type="query"
    class="mb-12 mt-15 mx-5"
    baseRoute="/"
    [screenAmount]="pages"
    [screen]="page"
  ></app-route-pagination>
}
