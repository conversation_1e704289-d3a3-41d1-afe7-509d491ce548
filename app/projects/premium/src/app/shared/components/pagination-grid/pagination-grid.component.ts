import { Component, Input, TemplateRef } from '@angular/core';
import { $GridColumns, $ShownPerScreen, GRID_ROWS } from '@pb/ui/utils/screen';
import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import { RoutePaginationComponent } from '@pb/ui';

@Component({
  selector: 'app-pagination-grid',
  templateUrl: './pagination-grid.component.html',
  styleUrls: ['./pagination-grid.component.css'],
  imports: [AsyncPipe, RoutePaginationComponent, NgTemplateOutlet],
})
export class PaginationGridComponent<Item extends { [key: string]: any }> {
  @Input() template: TemplateRef<any>;
  @Input() items: Item[];
  @Input() page: number;
  @Input() pages: number;
  @Input() loading = false;
  @Input() paginationAnimationElementSelector: string | null = null;
  rows = GRID_ROWS;
  $cols = $GridColumns;
  $pageSize = $ShownPerScreen;

  buildContext(item: Item): { [key: string]: any } {
    return { ...item, item };
  }
}
