:host {
  @apply relative flex flex-col;
}

:host:not(.auto-size)::after {
  content: "";
  padding-bottom: 75%;
  @apply flex;
}

:host-context(app-pagination-grid),
:host-context(app-landing-subscribed),
:host-context(app-landing-preview),
:host-context(modular) {
  @apply overflow-hidden w-full h-auto;
  aspect-ratio: 1 / 1;
  flex: 0 0 auto;
}

:host-context(app-landing-subscribed):is(.aspect-2\/1),
:host-context(app-landing-preview):is(.aspect-2\/1),
:host-context(modular):is(.aspect-2\/1) {
  aspect-ratio: 2 / 1;
}

:host-context(.gallery-list) {
  @apply overflow-hidden;
  aspect-ratio: 1 / 1;
}

:host-context(.gallery-hero) {
  @apply h-full overflow-hidden;
}

@media (max-width: 767px) {
  :host-context(swiper-slide) {
    flex: 1 1 auto;
  }
}
