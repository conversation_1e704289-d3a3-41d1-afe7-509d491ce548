<div class="card-grid">
  @for (_ of [].constructor(numberColumns); track _; let j = $index) {
    <div class="card-cell card-cell-{{ j }}">
      @for (modelInfo of gridData[j]; track modelInfo; let i = $index) {
        <div
          class="card-item"
          [routerLink]="[
            '/girl',
            modelInfo.queryGirl.entities[0]?.entityId,
            modelInfo.id,
          ]"
        >
          <app-gallery-card
            [image]="
              modelInfo.queryMainImages.entities[0]?.fieldMediaImage?.url
            "
            [name]="modelInfo.entityLabel"
            [title]="modelInfo.fieldCategory?.entity?.name"
          >
          </app-gallery-card>
        </div>
      }
    </div>
  }
</div>
<!-- <div (appScrollIn)="loadMoreData()"></div> -->
