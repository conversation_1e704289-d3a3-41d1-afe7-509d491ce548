import { Component } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { BehaviorSubject, combineLatest, of } from 'rxjs';
import { map, mergeMap } from 'rxjs/operators';
import { IGirlInfoWithGirl } from '../../../screens/girl-infos/infinite-scroll/definitions/models';
import { GET_GIRL_INFOS } from '../../../screens/girl-infos/infinite-scroll/definitions/queries';
import { GalleryCardComponent } from '../gallery-card/gallery-card.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-article-infinite-scroll',
  templateUrl: './article-infinite-scroll.component.html',
  styleUrls: ['./article-infinite-scroll.component.css'],
  imports: [GalleryCardComponent, RouterLink],
})
export class ArticleInfiniteScrollComponent {
  data: any = [...Array(10).keys()];

  numberColumns = 4;
  gridData = Array.from(Array(this.numberColumns), () => new Array(0));
  lastNumber = 10;

  private readonly pagesBehav = new BehaviorSubject(1);
  public pageSize = of(8);

  constructor(apollo: Apollo) {
    combineLatest([this.pageSize, this.pagesBehav])
      .pipe(
        mergeMap(([size, pages]) =>
          combineLatest(
            new Array(pages).fill(null).map((_, page) =>
              apollo.query<{
                pbGirlInfo: {
                  results: IGirlInfoWithGirl[];
                };
              }>({
                query: GET_GIRL_INFOS,
                variables: { size, page },
              }),
            ),
          ),
        ),
        map((v) =>
          v.reduce((a, b) => [...a, ...b.data.pbGirlInfo.results], []),
        ),
      )
      .subscribe((data) => this.splitData(data));
  }

  splitData(data) {
    this.gridData = Array.from(Array(this.numberColumns), () => new Array(0));

    for (let i = 0; i < data.length; i++) {
      this.gridData[i % 4].push(data[i]);
    }
  }

  loadMoreData(): void {
    function _range(start, end) {
      var lowEnd = start;
      var highEnd = end;
      var arr = [];
      while (lowEnd <= highEnd) {
        arr.push(lowEnd++);
      }
      return arr;
    }

    // update this.data by pushign the new data, and resplit it to gridData
    this.data.push(..._range(this.lastNumber, this.lastNumber + 10));

    this.lastNumber += 10;
    this.splitData(this.data);
  }
}
