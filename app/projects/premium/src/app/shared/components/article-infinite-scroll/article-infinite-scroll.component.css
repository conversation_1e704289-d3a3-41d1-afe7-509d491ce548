.card-grid {
  display: grid;
  grid-template-areas: "first second third fourth";
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.card-cell {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card-cell-0 {
  grid-area: first;
}
.card-cell-1 {
  grid-area: second;
}
.card-cell-2 {
  grid-area: third;
}
.card-cell-3 {
  grid-area: fourth;
}

.card-item {
  height: fit-content;
  cursor: pointer;
}
