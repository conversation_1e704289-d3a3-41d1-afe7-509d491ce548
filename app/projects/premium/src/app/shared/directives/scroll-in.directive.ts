import {
  AfterViewInit,
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  Output,
} from '@angular/core';
import { Subject } from 'rxjs';

@Directive({
  selector: '[appScrollIn]',
  standalone: true,
})
export class ScrollInDirective implements AfterViewInit {
  @Output() appScrollIn = new Subject<boolean>();
  scrolledIn = false;

  @Input() multiplier = 2;

  constructor(private element: ElementRef) {}

  ngAfterViewInit(): void {
    this.onScroll();
  }

  @HostListener('window:scroll')
  onScroll(): void {
    const isIn =
      this.element.nativeElement.getBoundingClientRect().top <=
      window.innerHeight * this.multiplier;

    if (this.scrolledIn !== isIn) {
      this.appScrollIn.next(isIn);
      this.scrolledIn = isIn;
    }
  }
}
