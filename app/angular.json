{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"premium": {"projectType": "application", "schematics": {"@schematics/angular:application": {"strict": true}}, "root": "projects/premium", "sourceRoot": "projects/premium/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/premium"}, "index": "projects/premium/src/index.html", "polyfills": ["zone.js", "fullscreen-api-polyfill"], "tsConfig": "projects/premium/tsconfig.app.json", "aot": true, "assets": ["projects/premium/src/favicon.ico", "projects/premium/src/assets"], "styles": ["projects/premium/src/styles.css", "node_modules/swiper/swiper-bundle.min.css"], "scripts": [], "browser": "projects/premium/src/main.ts", "server": "projects/premium/src/main.server.ts", "outputMode": "server", "prerender": false, "ssr": {"entry": "projects/premium/server.ts"}}, "defaultConfiguration": "development", "configurations": {"development": {"optimization": false, "extractLicenses": true, "sourceMap": true}, "graphql": {"fileReplacements": [{"replace": "projects/premium/src/environments/environment.ts", "with": "projects/premium/src/environments/environment.graphql.ts"}]}, "preview": {"fileReplacements": [{"replace": "projects/premium/src/environments/environment.ts", "with": "projects/premium/src/environments/environment.preview.ts"}]}, "production": {"fileReplacements": [{"replace": "projects/premium/src/environments/environment.ts", "with": "projects/premium/src/environments/environment.prod.ts"}], "optimization": {"scripts": true, "fonts": {"inline": true}, "styles": {"inlineCritical": false, "minify": true, "removeSpecialComments": true}}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "6mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "200kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "premium:build"}, "configurations": {"development": {"buildTarget": "premium:build"}, "production": {"buildTarget": "premium:build:production"}, "preview": {"buildTarget": "premium:build:preview"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/premium/src/test.ts", "polyfills": ["zone.js", "zone.js/testing", "fullscreen-api-polyfill"], "tsConfig": "projects/premium/tsconfig.spec.json", "karmaConfig": "projects/premium/karma.conf.js", "assets": ["projects/premium/src/favicon.ico", "projects/premium/src/assets"], "styles": ["projects/premium/src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": "1dbd72e7-7548-4146-bfe1-cfd201862683"}}