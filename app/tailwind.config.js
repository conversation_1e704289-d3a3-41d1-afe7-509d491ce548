function stepToRem(step) {
  return `${step / 4}rem`;
}

function pxToRem(px) {
  return stepToRem((px / 16) * 4);
}

function step(step) {
  return { [`${step}`]: stepToRem(step) };
}

const headerHeight = { header: pxToRem(100) };
const headerSpacing = { "header-spacing": pxToRem(100) };

module.exports = {
  future: {
    // removeDeprecatedGapUtilities: true,
    // purgeLayersByDefault: true,
  },
  purge: [],
  theme: {
    extend: {
      screens: {
        xlm: "1450px",
        xxl: "2000px",
      },
      letterSpacing: {
        w25: "2.5px",
      },
      height: {
        ...step(11),
        ...step(15),
        ...step(18),
        ...headerHeight,
        "screen-3/4": "75vh",
        "screen-1/2": "50vh",
        "2/1": "200%",
        90: "90vh",
        slider: "calc(100vh - 100px)",
      },
      width: {
        w25: "25%",
        winScrollbar: "17px",
        ...step(15),
        ...step(18),
        ...step(28),
        ...step(72),
        ...step(78),
        ...step(88),
        ...step(195),
        "2/1": "200%",
      },
      borderRadius: {
        "4xl": "2rem",
      },
      boxShadow: {
        xl: "0px 0px 40px #000000",
        md: "0px 0px 12px rgba(0, 0, 0, 0.25)",
      },
      minHeight: { ...step(16), ...step(112) },
      maxWidth: { ...step(88) },
      minWidth: { ...step(10) },
      colors: {
        "gray-100": "#f0f0f0",
        "gray-200": "#E5E5E5",
        "gray-300": "#d9d9d9",
        "gray-400": "#bbbbbb",
        "gray-500": "#979797",
        "gray-700": "#515151",
        "gray-750": "#343434", // the amount of colors is getting ridiculous
        "gray-775": "#2D2D2D",
        "gray-800": "#252525",
        "gray-900": "#1C1C1C",
        golden: "#C5A350",
        "green-400": "#4EC978",
        red: {
          DEFAULT: "#EE5C6E",
        },
      },
      fontSize: {
        xxs: ".5rem",
        "4xxl": pxToRem(32),
      },
      fontFamily: {
        bauer: "Bauer Bodoni",
        hero: "HeroicCondensed-Bold",
        stag: "Stag Sans Web",
        inter: "Inter",
        garamond: "adobe-garamond-pro",
        // roboto: "Roboto",
        georgia: "Georgia, 'EB Garamond', 'Times New Roman', serif",
      },
      inset: {
        ...step(2),
        "1/2": "50%",
        full: "100%",
        ...headerHeight,
      },
      margin: {
        ...headerSpacing,
        ...step(15),
        ...step(18),
        ...step(1.5),
        31: pxToRem(120),
        "28-5": pxToRem(120),
      },
      padding: {
        ...headerSpacing,
        15: pxToRem(60),
        ...step(60),
        ...step(18),
      },
      backgroundOpacity: {
        65: "0.65",
        84: "0.84",
      },
      backdropBlur: {
        xl: "20px",
      },
      listStyleType: {
        square: "square",
        roman: "upper-roman",
        "lower-alpha": "lower-alpha",
      },
      cursor: {
        "ew-resize": "ew-resize",
        grab: "grab",
        grabbing: "grabbing",
      },
    },
  },
  variants: {
    display: ["group-hover", "responsive"],
    extend: {
      transform: ["group-hover"],
      scale: ["group-hover"],
      gradientColorStops: ["group-hover"],
      height: ["group-hover"],
      width: ["group-hover"],
    },
  },
  plugins: [],
};
