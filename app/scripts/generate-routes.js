// filepath: /Users/<USER>/Sites/playboy/playboy-premium/app/scripts/generate-routes.js
const fs = require("fs");
const path = require("path");
const fetch = require("node-fetch");
const angularJsonPath = path.join(__dirname, "../angular.json");
const angularJson = require(angularJsonPath);

// Replace this with your actual database fetching logic
async function fetchDynamicIds() {
  const url = "https://api.premium.playboy.de/api/v1/public/girls";
  try {
    const json = await fetch(url).then((res) => res.json());
    console.log(json);

    return json.map((value) => value.id);
  } catch (error) {
    console.error(error.message);
  }
}

async function generateRoutes() {
  const staticRoutes = ["/profile"];
  const dynamicIds = await fetchDynamicIds();
  const routes = dynamicIds.map((id) => `/p/girl/${id}`);

  angularJson.projects.premium.architect.prerender.options.routes =
    routes.concat(staticRoutes);

  fs.writeFileSync(angularJsonPath, JSON.stringify(angularJson, null, 2));
  console.log("Routes generated successfully");
}

generateRoutes().catch((err) => {
  console.error("Error generating routes:", err);
  process.exit(1);
});
